#!/usr/bin/env python3
"""
تشغيل مبسط للنظام
Simple System Run
"""
import sys
import os

print("🚀 بدء تشغيل نظام المحاسبة...")

# Add the src directory to the Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(current_dir, 'src')
sys.path.insert(0, src_dir)

print(f"📁 مجلد العمل: {current_dir}")
print(f"📁 مجلد المصدر: {src_dir}")

try:
    print("🔍 اختبار PyQt6...")
    from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QPushButton
    from PyQt6.QtCore import Qt
    from PyQt6.QtGui import QFont
    print("✅ PyQt6 متاح!")
    
    class SimpleTestWindow(QMainWindow):
        def __init__(self):
            super().__init__()
            self.setWindowTitle("🧪 اختبار نظام المحاسبة")
            self.setGeometry(200, 200, 800, 600)
            
            central_widget = QWidget()
            self.setCentralWidget(central_widget)
            
            layout = QVBoxLayout()
            central_widget.setLayout(layout)
            
            # Title
            title = QLabel("🎉 نظام المحاسبة يعمل بنجاح!")
            title.setFont(QFont("Arial", 18, QFont.Weight.Bold))
            title.setAlignment(Qt.AlignmentFlag.AlignCenter)
            title.setStyleSheet("color: #2c3e50; margin: 20px; padding: 20px;")
            layout.addWidget(title)
            
            # Status
            status = QLabel("✅ تم تحميل PyQt6 بنجاح\n✅ النظام جاهز للاستخدام")
            status.setFont(QFont("Arial", 12))
            status.setAlignment(Qt.AlignmentFlag.AlignCenter)
            status.setStyleSheet("color: #27ae60; margin: 10px; padding: 10px;")
            layout.addWidget(status)
            
            # Test buttons
            self.test_imports_btn = QPushButton("🔍 اختبار الاستيراد")
            self.test_imports_btn.clicked.connect(self.test_imports)
            layout.addWidget(self.test_imports_btn)
            
            self.test_forms_btn = QPushButton("🧪 اختبار النماذج")
            self.test_forms_btn.clicked.connect(self.test_forms)
            layout.addWidget(self.test_forms_btn)
            
            # Results area
            self.results_label = QLabel("اضغط على الأزرار أعلاه لاختبار النظام")
            self.results_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            self.results_label.setStyleSheet("background-color: #f8f9fa; padding: 20px; margin: 10px; border-radius: 5px;")
            layout.addWidget(self.results_label)
        
        def test_imports(self):
            results = []
            try:
                import sqlalchemy
                results.append("✅ SQLAlchemy")
            except:
                results.append("❌ SQLAlchemy")
            
            try:
                import bcrypt
                results.append("✅ bcrypt")
            except:
                results.append("❌ bcrypt")
            
            try:
                from src.database.database import SessionLocal
                results.append("✅ Database")
            except Exception as e:
                results.append(f"❌ Database: {str(e)[:50]}")
            
            self.results_label.setText("نتائج اختبار الاستيراد:\n" + "\n".join(results))
        
        def test_forms(self):
            results = []
            forms = [
                ('product_form', 'ProductForm', '🛍️ المنتجات'),
                ('invoice_form', 'InvoiceForm', '🧾 الفواتير'),
                ('employee_form', 'EmployeeForm', '👥 الموظفين'),
            ]
            
            for module_name, class_name, display_name in forms:
                try:
                    module = __import__(f'src.ui.forms.{module_name}', fromlist=[class_name])
                    form_class = getattr(module, class_name)
                    results.append(f"✅ {display_name}")
                except Exception as e:
                    results.append(f"❌ {display_name}: {str(e)[:30]}")
            
            self.results_label.setText("نتائج اختبار النماذج:\n" + "\n".join(results))
    
    print("🎨 إنشاء التطبيق...")
    app = QApplication(sys.argv)
    
    print("🏠 إنشاء النافذة الرئيسية...")
    window = SimpleTestWindow()
    window.show()
    
    print("✅ النظام جاهز! النافذة مفتوحة الآن.")
    print("💡 يمكنك إغلاق النافذة للخروج.")
    
    # Run the application
    sys.exit(app.exec())

except ImportError as e:
    print(f"❌ خطأ في الاستيراد: {e}")
    print("💡 تأكد من تثبيت PyQt6:")
    print("   pip install PyQt6")
    
except Exception as e:
    print(f"❌ خطأ عام: {e}")
    import traceback
    traceback.print_exc()

print("👋 انتهى التشغيل.")
