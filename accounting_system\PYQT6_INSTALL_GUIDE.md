# 🔧 دليل تثبيت PyQt6 الشامل

## 🚨 مشكلة: PyQt6 غير مثبت

PyQt6 مطلوب لتشغيل النظام ولكنه غير مثبت حالياً. إليك طرق متعددة لتثبيته:

---

## 🛠️ الطرق المختلفة للتثبيت

### 🥇 الطريقة 1: التثبيت الأساسي
```bash
# افتح Command Prompt أو Terminal
pip install PyQt6
```

### 🥈 الطريقة 2: التثبيت للمستخدم الحالي
```bash
pip install --user PyQt6
```

### 🥉 الطريقة 3: استخدام Python Module
```bash
python -m pip install PyQt6
```

### 🔧 الطريقة 4: التثبيت مع تحديث pip
```bash
python -m pip install --upgrade pip
python -m pip install PyQt6
```

### 🧹 الطريقة 5: تنظيف ذاكرة التخزين المؤقت
```bash
pip cache purge
pip install --no-cache-dir PyQt6
```

### 👑 الطريقة 6: التثبيت كمدير (Windows)
```bash
# شغل Command Prompt كمدير (Run as Administrator)
pip install PyQt6
```

### 🐍 الطريقة 7: استخدام Conda (إذا كان متاح)
```bash
conda install pyqt
# أو
conda install -c conda-forge pyqt6
```

---

## 🪟 تعليمات خاصة بـ Windows

### الطريقة A: استخدام Command Prompt
1. اضغط `Win + R`
2. اكتب `cmd` واضغط Enter
3. انتقل إلى مجلد النظام: `cd C:\path\to\accounting_system`
4. شغل: `pip install PyQt6`

### الطريقة B: استخدام PowerShell
1. اضغط `Win + X` واختر "Windows PowerShell (Admin)"
2. انتقل إلى مجلد النظام: `cd C:\path\to\accounting_system`
3. شغل: `python -m pip install PyQt6`

### الطريقة C: استخدام الملفات المرفقة
```bash
# شغل أحد هذه الملفات:
install_pyqt6.bat
# أو
powershell -ExecutionPolicy Bypass -File install_pyqt6.ps1
```

---

## 🐧 تعليمات خاصة بـ Linux

### Ubuntu/Debian:
```bash
sudo apt update
sudo apt install python3-pyqt6
# أو
pip3 install --user PyQt6
```

### CentOS/RHEL/Fedora:
```bash
sudo dnf install python3-pyqt6
# أو
pip3 install --user PyQt6
```

---

## 🍎 تعليمات خاصة بـ macOS

### استخدام pip:
```bash
pip3 install PyQt6
```

### استخدام Homebrew:
```bash
brew install pyqt6
```

---

## 🧪 اختبار التثبيت

بعد التثبيت، اختبر PyQt6:

```bash
python -c "from PyQt6.QtWidgets import QApplication; print('✅ PyQt6 يعمل!')"
```

إذا ظهرت الرسالة "✅ PyQt6 يعمل!" فالتثبيت نجح!

---

## 🐛 حل المشاكل الشائعة

### ❌ خطأ: "No module named 'PyQt6'"
**الحل:**
```bash
# تأكد من استخدام نفس Python
which python
python -m pip install PyQt6
```

### ❌ خطأ: "Permission denied"
**الحل:**
```bash
# استخدم --user
pip install --user PyQt6
# أو شغل كمدير
sudo pip install PyQt6  # Linux/Mac
```

### ❌ خطأ: "No space left on device"
**الحل:**
```bash
# نظف ذاكرة التخزين المؤقت
pip cache purge
# أو ثبت بدون ذاكرة التخزين المؤقت
pip install --no-cache-dir PyQt6
```

### ❌ خطأ: "Could not find a version"
**الحل:**
```bash
# تحديث pip
python -m pip install --upgrade pip
# ثم حاول مرة أخرى
pip install PyQt6
```

### ❌ خطأ: "Microsoft Visual C++ required"
**الحل (Windows):**
1. حمل وثبت "Microsoft Visual C++ Redistributable"
2. أو استخدم: `pip install --only-binary=all PyQt6`

---

## 🔄 طرق بديلة

### 1️⃣ تحميل ملف Wheel يدوياً:
```bash
# اذهب إلى: https://pypi.org/project/PyQt6/#files
# حمل الملف المناسب لنظامك
pip install downloaded_file.whl
```

### 2️⃣ استخدام بيئة افتراضية جديدة:
```bash
python -m venv new_env
# Windows:
new_env\Scripts\activate
# Linux/Mac:
source new_env/bin/activate
pip install PyQt6
```

### 3️⃣ استخدام Anaconda:
```bash
conda create -n pyqt_env python=3.11
conda activate pyqt_env
conda install pyqt6
```

---

## ✅ التحقق النهائي

بعد التثبيت الناجح:

1. **اختبر PyQt6:**
   ```bash
   python -c "from PyQt6.QtCore import PYQT_VERSION_STR; print(f'PyQt6 version: {PYQT_VERSION_STR}')"
   ```

2. **شغل النظام:**
   ```bash
   python final_launch.py
   ```

3. **إذا نجح التشغيل، ستظهر نافذة النظام!**

---

## 📞 الدعم الإضافي

إذا فشلت جميع الطرق:

1. **تحقق من إصدار Python:** `python --version`
2. **تحقق من pip:** `pip --version`
3. **جرب Python مختلف:** `python3` بدلاً من `python`
4. **أعد تشغيل الجهاز** وجرب مرة أخرى
5. **استخدم بيئة افتراضية جديدة**

---

## 🎯 الهدف النهائي

بعد تثبيت PyQt6 بنجاح، ستتمكن من:
- ✅ تشغيل النظام بالكامل
- ✅ استخدام جميع النماذج السبعة
- ✅ الاستفادة من الواجهة الرسومية الكاملة

**🚀 ابدأ الآن بإحدى الطرق المذكورة أعلاه!**
