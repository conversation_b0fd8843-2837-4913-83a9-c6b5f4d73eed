#!/usr/bin/env python3
"""
🚀 ابدأ من هنا - Start Here
ملف التشغيل الرئيسي لنظام المحاسبة المتكامل
Main Launch File for Comprehensive Accounting System
"""
import sys
import os
import subprocess
from pathlib import Path

def check_requirements():
    """التحقق من المتطلبات الأساسية"""
    print("🔍 التحقق من المتطلبات...")
    
    # Check Python version
    if sys.version_info < (3, 8):
        print("❌ يتطلب Python 3.8 أو أحدث")
        return False
    
    # Check required packages
    required_packages = ['PyQt6', 'sqlalchemy', 'bcrypt']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.lower().replace('pyqt6', 'PyQt6'))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ الحزم المفقودة: {', '.join(missing_packages)}")
        print("💡 لتثبيت الحزم المفقودة:")
        for package in missing_packages:
            print(f"   pip install {package}")
        return False
    
    print("✅ جميع المتطلبات متوفرة!")
    return True

def setup_database():
    """إعداد قاعدة البيانات"""
    print("\n🔧 إعداد قاعدة البيانات...")
    
    try:
        # Run setup script
        result = subprocess.run([sys.executable, "setup_test_data.py"], 
                              capture_output=True, text=True, cwd=Path(__file__).parent)
        
        if result.returncode == 0:
            print("✅ تم إعداد قاعدة البيانات بنجاح!")
            return True
        else:
            print("❌ فشل في إعداد قاعدة البيانات:")
            print(result.stderr)
            return False
    except Exception as e:
        print(f"❌ خطأ في إعداد قاعدة البيانات: {e}")
        return False

def launch_system():
    """تشغيل النظام"""
    print("\n🚀 تشغيل نظام المحاسبة...")
    
    try:
        subprocess.run([sys.executable, "test_all_forms.py"], cwd=Path(__file__).parent)
    except Exception as e:
        print(f"❌ خطأ في تشغيل النظام: {e}")

def main():
    """الدالة الرئيسية"""
    print("=" * 70)
    print("🏢 نظام المحاسبة المتكامل - Comprehensive Accounting System")
    print("=" * 70)
    print("📋 النماذج المتاحة:")
    print("   🛍️  إدارة المنتجات والمخزون")
    print("   🧾  إدارة الفواتير والمبيعات") 
    print("   👥  إدارة الموظفين والموارد البشرية")
    print("   📦  إدارة المشتريات وأوامر الشراء")
    print("   💰  إدارة المصروفات والنفقات")
    print("   👤  إدارة المستخدمين والصلاحيات")
    print("   💵  إدارة الرواتب والأجور")
    print("=" * 70)
    
    # Check requirements
    if not check_requirements():
        input("\n⏸️ اضغط Enter للخروج...")
        return
    
    # Check if database exists
    db_file = Path(__file__).parent / "accounting.db"
    
    if not db_file.exists():
        print("\n📊 لم يتم العثور على قاعدة البيانات. سيتم إنشاؤها الآن...")
        if not setup_database():
            input("\n⏸️ اضغط Enter للخروج...")
            return
    else:
        print("\n✅ قاعدة البيانات موجودة!")
        
        choice = input("\n🤔 هل تريد إعادة إنشاء البيانات التجريبية؟ (y/n): ").lower().strip()
        if choice in ['y', 'yes', 'نعم', 'ن']:
            if not setup_database():
                input("\n⏸️ اضغط Enter للخروج...")
                return
    
    print("\n🎯 البيانات التجريبية:")
    print("   👤 مستخدم: admin / admin123")
    print("   🛍️ منتجات تجريبية متاحة")
    print("   👥 موظفين وعملاء تجريبيين")
    
    input("\n⏸️ اضغط Enter لتشغيل النظام...")
    
    # Launch the system
    launch_system()
    
    print("\n👋 شكراً لاستخدام نظام المحاسبة!")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 تم إلغاء العملية. وداعاً!")
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        input("⏸️ اضغط Enter للخروج...")
