"""
Excel report generation functionality.
"""
import os
from datetime import datetime
import pandas as pd
from src.utils.translation import _

class ExcelReportGenerator:
    """Base class for Excel report generation."""
    
    def __init__(self):
        self.writer = None
        self.filename = None
    
    def generate(self, data: dict, filename: str = None):
        """
        Generate Excel report with multiple sheets.
        
        Args:
            data (dict): Dictionary with sheet names as keys and pandas DataFrames as values
            filename (str, optional): Output filename. Defaults to None.
        
        Returns:
            str: Path to the generated Excel file
        """
        if not filename:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = os.path.join('reports', f'report_{timestamp}.xlsx')
        
        # Ensure reports directory exists
        os.makedirs('reports', exist_ok=True)
        
        # Create Excel writer
        self.writer = pd.ExcelWriter(filename, engine='xlsxwriter')
        self.filename = filename
        
        # Write each sheet
        for sheet_name, df in data.items():
            df.to_excel(self.writer, sheet_name=sheet_name, index=False)
            self._adjust_column_width(df, sheet_name)
        
        # Save and close
        self.writer.close()
        return filename
    
    def _adjust_column_width(self, df, sheet_name):
        """Adjust column widths based on content."""
        worksheet = self.writer.sheets[sheet_name]
        for i, col in enumerate(df.columns):
            max_length = max(
                df[col].astype(str).apply(len).max(),
                len(str(col))
            )
            worksheet.set_column(i, i, max_length + 2)
