#!/usr/bin/env python3
"""
اختبار سريع للنظام
Quick System Test
"""
import sys
import os

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_imports():
    """اختبار الاستيراد"""
    print("🔍 اختبار الاستيراد...")
    
    try:
        print("  ✓ اختبار PyQt6...")
        from PyQt6.QtWidgets import QApplication, QWidget
        print("  ✅ PyQt6 متاح!")
        
        print("  ✓ اختبار SQLAlchemy...")
        import sqlalchemy
        print("  ✅ SQLAlchemy متاح!")
        
        print("  ✓ اختبار bcrypt...")
        import bcrypt
        print("  ✅ bcrypt متاح!")
        
        print("  ✓ اختبار قاعدة البيانات...")
        from src.database.database import SessionLocal
        print("  ✅ قاعدة البيانات متاحة!")
        
        print("  ✓ اختبار النماذج...")
        from src.models.inventory import Product
        from src.models.customers import Customer
        print("  ✅ النماذج متاحة!")
        
        print("  ✓ اختبار الترجمة...")
        from src.utils.translation import _
        print("  ✅ الترجمة متاحة!")
        
        return True
        
    except ImportError as e:
        print(f"  ❌ خطأ في الاستيراد: {e}")
        return False
    except Exception as e:
        print(f"  ❌ خطأ عام: {e}")
        return False

def test_forms():
    """اختبار النماذج"""
    print("\n🧪 اختبار النماذج...")
    
    forms_to_test = [
        ('product_form', 'ProductForm', '🛍️ المنتجات'),
        ('invoice_form', 'InvoiceForm', '🧾 الفواتير'),
        ('employee_form', 'EmployeeForm', '👥 الموظفين'),
        ('purchase_form', 'PurchaseForm', '📦 المشتريات'),
        ('expense_form', 'ExpenseForm', '💰 المصروفات'),
        ('user_management_form', 'UserManagementForm', '👤 المستخدمين'),
        ('salary_form', 'SalaryForm', '💵 الرواتب'),
    ]
    
    successful_forms = []
    failed_forms = []
    
    for module_name, class_name, display_name in forms_to_test:
        try:
            print(f"  ✓ اختبار {display_name}...")
            module = __import__(f'src.ui.forms.{module_name}', fromlist=[class_name])
            form_class = getattr(module, class_name)
            print(f"  ✅ {display_name} متاح!")
            successful_forms.append(display_name)
        except Exception as e:
            print(f"  ❌ {display_name} فشل: {e}")
            failed_forms.append((display_name, str(e)))
    
    return successful_forms, failed_forms

def main():
    """الدالة الرئيسية"""
    print("🚀 اختبار سريع لنظام المحاسبة")
    print("=" * 50)
    
    # Test imports
    if not test_imports():
        print("\n❌ فشل في اختبار الاستيراد!")
        return False
    
    # Test forms
    successful_forms, failed_forms = test_forms()
    
    print("\n📊 نتائج الاختبار:")
    print(f"  ✅ النماذج الناجحة: {len(successful_forms)}")
    print(f"  ❌ النماذج الفاشلة: {len(failed_forms)}")
    
    if successful_forms:
        print("\n✅ النماذج المتاحة:")
        for form in successful_forms:
            print(f"    • {form}")
    
    if failed_forms:
        print("\n❌ النماذج الفاشلة:")
        for form, error in failed_forms:
            print(f"    • {form}: {error}")
    
    if len(successful_forms) >= 5:  # At least 5 forms working
        print("\n🎉 النظام جاهز للاستخدام!")
        print("💡 لتشغيل النظام الكامل:")
        print("   python test_all_forms.py")
        return True
    else:
        print("\n⚠️ النظام يحتاج إلى إصلاحات!")
        return False

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"\n❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
