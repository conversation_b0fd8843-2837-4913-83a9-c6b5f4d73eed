"""
Login window for the accounting system.
"""
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QPushButton, 
    QLabel, QLineEdit, QMessageBox
)
from PyQt6.QtCore import Qt, pyqtSignal
from src.config import APP_NAME
import bcrypt

class LoginWindow(QWidget):
    """Login window class."""
    
    # Signal emitted when login is successful
    login_successful = pyqtSignal()
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle(f'تسجيل الدخول - {APP_NAME}')
        self.setFixedSize(400, 300)
        self._init_ui()
    
    def _init_ui(self):
        """Initialize the UI."""
        # Create main layout
        layout = QVBoxLayout()
        layout.setSpacing(20)
        self.setLayout(layout)
        
        # Add title
        title = QLabel(APP_NAME)
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title.setStyleSheet('font-size: 24px; font-weight: bold; margin: 20px;')
        layout.addWidget(title)
        
        # Add username field
        username_layout = QHBoxLayout()
        username_label = QLabel('اسم المستخدم:')
        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText('أدخل اسم المستخدم')
        username_layout.addWidget(username_label)
        username_layout.addWidget(self.username_input)
        layout.addLayout(username_layout)
        
        # Add password field
        password_layout = QHBoxLayout()
        password_label = QLabel('كلمة المرور:')
        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText('أدخل كلمة المرور')
        self.password_input.setEchoMode(QLineEdit.EchoMode.Password)
        password_layout.addWidget(password_label)
        password_layout.addWidget(self.password_input)
        layout.addLayout(password_layout)
        
        # Add login button
        login_btn = QPushButton('تسجيل الدخول')
        login_btn.setFixedHeight(40)
        login_btn.clicked.connect(self._handle_login)
        layout.addWidget(login_btn)
        
        # Add stretch to push everything to the top
        layout.addStretch()
        
        # Set window style
        self.setStyleSheet("""
            QWidget {
                font-size: 14px;
            }
            QLineEdit {
                padding: 8px;
                border: 1px solid #ccc;
                border-radius: 4px;
            }
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
        """)
    
    def _handle_login(self):
        """Handle login button click."""
        username = self.username_input.text()
        password = self.password_input.text()
        
        if not username or not password:
            QMessageBox.warning(
                self,
                'خطأ',
                'الرجاء إدخال اسم المستخدم وكلمة المرور'
            )
            return
        
        # Here you would normally verify against the database
        # For now, we'll use a simple check
        if self._verify_credentials(username, password):
            self.login_successful.emit()
            self.close()
        else:
            QMessageBox.warning(
                self,
                'خطأ',
                'اسم المستخدم أو كلمة المرور غير صحيحة'
            )
    
    def _verify_credentials(self, username: str, password: str) -> bool:
        """
        Verify login credentials.
        
        Args:
            username: The username to verify
            password: The password to verify
        
        Returns:
            bool: True if credentials are valid, False otherwise
        """
        # TODO: Implement actual database verification
        # For now, return True for testing
        return True
