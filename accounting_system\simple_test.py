#!/usr/bin/env python3
"""
اختبار بسيط للنظام
Simple System Test
"""
import sys
import os

print("🚀 اختبار نظام المحاسبة...")
print(f"🐍 Python: {sys.version}")
print(f"📁 المجلد: {os.getcwd()}")

# اختبار المتطلبات
print("\n🔍 اختبار المتطلبات:")

# اختبار SQLAlchemy
try:
    import sqlalchemy
    print(f"✅ SQLAlchemy: {sqlalchemy.__version__}")
except ImportError as e:
    print(f"❌ SQLAlchemy: {e}")

# اختبار bcrypt
try:
    import bcrypt
    print("✅ bcrypt: متاح")
except ImportError as e:
    print(f"❌ bcrypt: {e}")

# اختبار PyQt6
try:
    from PyQt6.QtWidgets import QApplication
    from PyQt6.QtCore import PYQT_VERSION_STR
    print(f"✅ PyQt6: {PYQT_VERSION_STR}")
    pyqt_available = True
except ImportError as e:
    print(f"❌ PyQt6: {e}")
    pyqt_available = False

# إذا كان PyQt6 متاح، شغل تطبيق بسيط
if pyqt_available:
    print("\n🎨 إنشاء تطبيق PyQt6...")
    try:
        from PyQt6.QtWidgets import QMainWindow, QLabel, QVBoxLayout, QWidget
        from PyQt6.QtCore import Qt
        from PyQt6.QtGui import QFont
        
        app = QApplication(sys.argv)
        
        window = QMainWindow()
        window.setWindowTitle("🧪 اختبار نظام المحاسبة")
        window.setGeometry(300, 300, 600, 400)
        
        central = QWidget()
        window.setCentralWidget(central)
        layout = QVBoxLayout()
        central.setLayout(layout)
        
        title = QLabel("🎉 نظام المحاسبة يعمل!")
        title.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title.setStyleSheet("color: #2c3e50; padding: 20px; background-color: #ecf0f1; border-radius: 10px;")
        layout.addWidget(title)
        
        info = QLabel("✅ PyQt6 يعمل بنجاح!\n✅ النظام جاهز للاستخدام")
        info.setAlignment(Qt.AlignmentFlag.AlignCenter)
        info.setStyleSheet("color: #27ae60; padding: 20px; font-size: 14px;")
        layout.addWidget(info)
        
        window.show()
        print("✅ تم فتح النافذة!")
        print("💡 أغلق النافذة للخروج")
        
        sys.exit(app.exec())
        
    except Exception as e:
        print(f"❌ خطأ في PyQt6: {e}")
        import traceback
        traceback.print_exc()
else:
    print("\n⚠️ PyQt6 غير متاح!")
    print("💡 لتثبيت PyQt6:")
    print("   pip install PyQt6")
    print("   أو")
    print("   python -m pip install --user PyQt6")

print("\n👋 انتهى الاختبار.")
