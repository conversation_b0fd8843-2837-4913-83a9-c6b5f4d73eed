<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚀 تشغيل نظام المحاسبة</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        
        .content {
            padding: 40px;
        }
        
        .step {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            border-left: 5px solid #e74c3c;
            transition: transform 0.3s ease;
        }
        
        .step:hover {
            transform: translateX(-10px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        
        .step-number {
            background: #e74c3c;
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 1.2em;
            margin-bottom: 15px;
        }
        
        .step-title {
            font-size: 1.4em;
            font-weight: bold;
            margin-bottom: 10px;
            color: #2c3e50;
        }
        
        .step-desc {
            color: #6c757d;
            line-height: 1.6;
            margin-bottom: 15px;
        }
        
        .code-block {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            overflow-x: auto;
            cursor: pointer;
            transition: background 0.3s ease;
        }
        
        .code-block:hover {
            background: #34495e;
        }
        
        .code-block::before {
            content: "📋 انقر للنسخ - Click to copy";
            display: block;
            font-size: 0.8em;
            color: #95a5a6;
            margin-bottom: 5px;
        }
        
        .btn {
            display: inline-block;
            padding: 15px 30px;
            background: #e74c3c;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: bold;
            transition: all 0.3s ease;
            margin: 10px;
            font-size: 1.1em;
        }
        
        .btn:hover {
            background: #c0392b;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(231, 76, 60, 0.3);
        }
        
        .btn-success {
            background: #27ae60;
        }
        
        .btn-success:hover {
            background: #229954;
        }
        
        .btn-info {
            background: #3498db;
        }
        
        .btn-info:hover {
            background: #2980b9;
        }
        
        .alert {
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 5px solid;
        }
        
        .alert-success {
            background: #d4edda;
            border-color: #28a745;
            color: #155724;
        }
        
        .alert-warning {
            background: #fff3cd;
            border-color: #ffc107;
            color: #856404;
        }
        
        .alert-info {
            background: #d1ecf1;
            border-color: #17a2b8;
            color: #0c5460;
        }
        
        .files-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .file-card {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 15px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .file-card:hover {
            border-color: #e74c3c;
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .file-icon {
            font-size: 2em;
            margin-bottom: 10px;
        }
        
        .footer {
            background: #2c3e50;
            color: white;
            padding: 20px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 تشغيل نظام المحاسبة</h1>
            <p>دليل التشغيل السريع - Quick Launch Guide</p>
        </div>
        
        <div class="content">
            <div class="alert alert-info">
                <strong>📍 الموقع الحالي:</strong> C:\Users\<USER>\Bob\accounting_system<br>
                <strong>🎯 الهدف:</strong> تشغيل نظام المحاسبة المتكامل
            </div>
            
            <div class="step">
                <div class="step-number">1</div>
                <div class="step-title">🖥️ فتح Command Prompt</div>
                <div class="step-desc">افتح موجه الأوامر بإحدى الطرق التالية:</div>
                <ul style="margin: 10px 0; padding-right: 20px;">
                    <li>اضغط Win + R ثم اكتب cmd</li>
                    <li>اضغط Win + X واختر Command Prompt</li>
                    <li>ابحث عن "cmd" في قائمة ابدأ</li>
                </ul>
            </div>
            
            <div class="step">
                <div class="step-number">2</div>
                <div class="step-title">📁 الانتقال لمجلد النظام</div>
                <div class="step-desc">انسخ والصق هذا الأمر في Command Prompt:</div>
                <div class="code-block" onclick="copyToClipboard('cd C:\\Users\\<USER>\\Bob\\accounting_system')">
cd C:\Users\<USER>\Bob\accounting_system
                </div>
            </div>
            
            <div class="step">
                <div class="step-number">3</div>
                <div class="step-title">🔧 تثبيت PyQt6</div>
                <div class="step-desc">جرب هذه الأوامر بالترتيب حتى ينجح أحدها:</div>
                <div class="code-block" onclick="copyToClipboard('pip install PyQt6')">
pip install PyQt6
                </div>
                <div class="code-block" onclick="copyToClipboard('python -m pip install PyQt6')">
python -m pip install PyQt6
                </div>
                <div class="code-block" onclick="copyToClipboard('pip install --user PyQt6')">
pip install --user PyQt6
                </div>
            </div>
            
            <div class="step">
                <div class="step-number">4</div>
                <div class="step-title">🧪 اختبار التثبيت</div>
                <div class="step-desc">تأكد من نجاح تثبيت PyQt6:</div>
                <div class="code-block" onclick="copyToClipboard('python -c \"from PyQt6.QtWidgets import QApplication; print(\'✅ PyQt6 يعمل!\')\"')">
python -c "from PyQt6.QtWidgets import QApplication; print('✅ PyQt6 يعمل!')"
                </div>
            </div>
            
            <div class="step">
                <div class="step-number">5</div>
                <div class="step-title">🚀 تشغيل النظام</div>
                <div class="step-desc">شغل النظام بأحد هذه الأوامر:</div>
                <div class="code-block" onclick="copyToClipboard('python quick_start.py')">
python quick_start.py
                </div>
                <div class="code-block" onclick="copyToClipboard('python immediate_launch.py')">
python immediate_launch.py
                </div>
                <div class="code-block" onclick="copyToClipboard('RUN_SYSTEM.bat')">
RUN_SYSTEM.bat
                </div>
            </div>
            
            <div class="alert alert-success">
                <strong>🎉 عند النجاح ستحصل على:</strong><br>
                ✅ نافذة نظام المحاسبة<br>
                ✅ 7 نماذج متكاملة<br>
                ✅ واجهة عربية احترافية<br>
                ✅ قاعدة بيانات متطورة
            </div>
            
            <div class="alert alert-warning">
                <strong>🔑 بيانات الدخول التجريبية:</strong><br>
                👤 اسم المستخدم: <code>admin</code><br>
                🔒 كلمة المرور: <code>admin123</code>
            </div>
            
            <h3 style="text-align: center; margin: 30px 0; color: #2c3e50;">📁 الملفات المتاحة للتشغيل</h3>
            
            <div class="files-grid">
                <div class="file-card" onclick="openFile('quick_start.py')">
                    <div class="file-icon">🚀</div>
                    <div><strong>quick_start.py</strong></div>
                    <div style="font-size: 0.9em; color: #6c757d;">التشغيل السريع</div>
                </div>
                
                <div class="file-card" onclick="openFile('immediate_launch.py')">
                    <div class="file-icon">⚡</div>
                    <div><strong>immediate_launch.py</strong></div>
                    <div style="font-size: 0.9em; color: #6c757d;">التشغيل الفوري</div>
                </div>
                
                <div class="file-card" onclick="openFile('RUN_SYSTEM.bat')">
                    <div class="file-icon">🖥️</div>
                    <div><strong>RUN_SYSTEM.bat</strong></div>
                    <div style="font-size: 0.9em; color: #6c757d;">ملف Windows</div>
                </div>
                
                <div class="file-card" onclick="openFile('final_launch.py')">
                    <div class="file-icon">🎯</div>
                    <div><strong>final_launch.py</strong></div>
                    <div style="font-size: 0.9em; color: #6c757d;">التشغيل النهائي</div>
                </div>
            </div>
            
            <div style="text-align: center; margin-top: 30px;">
                <a href="file:///C:/Users/<USER>/Bob/accounting_system" class="btn btn-success">📁 فتح مجلد النظام</a>
                <a href="file:///C:/Users/<USER>/Bob/accounting_system/FINAL_INSTRUCTIONS.md" class="btn btn-info">📖 التعليمات الكاملة</a>
                <a href="file:///C:/Users/<USER>/Bob/accounting_system/web_interface.html" class="btn">🌐 الواجهة التفاعلية</a>
            </div>
        </div>
        
        <div class="footer">
            <p>🏢 نظام المحاسبة المتكامل - جاهز للتشغيل!</p>
            <p>Comprehensive Accounting System - Ready to Launch!</p>
        </div>
    </div>
    
    <script>
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(function() {
                alert('✅ تم نسخ الأمر! الصقه في Command Prompt');
            }, function(err) {
                console.error('فشل في النسخ: ', err);
                prompt('انسخ هذا الأمر:', text);
            });
        }
        
        function openFile(filename) {
            alert('🚀 لتشغيل ' + filename + ':\n\n1. افتح Command Prompt\n2. انتقل للمجلد: cd C:\\Users\\<USER>\\Bob\\accounting_system\n3. شغل: python ' + filename);
        }
    </script>
</body>
</html>
