#!/usr/bin/env python3
"""
تشغيل نهائي للنظام
Final System Launch
"""
import sys
import os

print("🚀 تشغيل نظام المحاسبة...")
print(f"📁 المجلد: {os.getcwd()}")
print(f"🐍 Python: {sys.version}")

# إضافة مجلد src
src_path = os.path.join(os.getcwd(), 'src')
if os.path.exists(src_path):
    sys.path.insert(0, src_path)
    print(f"✅ تم إضافة مجلد src")

# اختبار المتطلبات
print("\n🔍 اختبار المتطلبات...")

# اختبار PyQt6
try:
    from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QPushButton
    from PyQt6.QtCore import Qt
    from PyQt6.QtGui import QFont
    print("✅ PyQt6 متاح!")
    pyqt_available = True
except ImportError as e:
    print(f"❌ PyQt6 غير متاح: {e}")
    pyqt_available = False

# اختبار SQLAlchemy
try:
    import sqlalchemy
    print("✅ SQLAlchemy متاح!")
    sqlalchemy_available = True
except ImportError as e:
    print(f"❌ SQLAlchemy غير متاح: {e}")
    sqlalchemy_available = False

# اختبار bcrypt
try:
    import bcrypt
    print("✅ bcrypt متاح!")
    bcrypt_available = True
except ImportError as e:
    print(f"❌ bcrypt غير متاح: {e}")
    bcrypt_available = False

# إذا كان PyQt6 متاح، شغل النظام
if pyqt_available:
    print("\n🎨 إنشاء التطبيق...")
    
    try:
        app = QApplication(sys.argv)
        
        # إنشاء النافذة الرئيسية
        window = QMainWindow()
        window.setWindowTitle("🏢 نظام المحاسبة المتكامل")
        window.setGeometry(200, 200, 1000, 700)
        
        # إنشاء الواجهة
        central = QWidget()
        window.setCentralWidget(central)
        layout = QVBoxLayout()
        central.setLayout(layout)
        
        # العنوان
        title = QLabel("🎉 نظام المحاسبة يعمل بنجاح!")
        title.setFont(QFont("Arial", 18, QFont.Weight.Bold))
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title.setStyleSheet("""
            color: #2c3e50; 
            padding: 20px; 
            background-color: #ecf0f1; 
            border-radius: 10px; 
            margin: 20px;
            border: 2px solid #3498db;
        """)
        layout.addWidget(title)
        
        # معلومات النظام
        info_text = f"""
        ✅ PyQt6: {'متاح' if pyqt_available else 'غير متاح'}
        ✅ SQLAlchemy: {'متاح' if sqlalchemy_available else 'غير متاح'}
        ✅ bcrypt: {'متاح' if bcrypt_available else 'غير متاح'}
        
        📋 النماذج المتاحة:
        🛍️ إدارة المنتجات والمخزون
        🧾 إدارة الفواتير والمبيعات
        👥 إدارة الموظفين والموارد البشرية
        📦 إدارة المشتريات وأوامر الشراء
        💰 إدارة المصروفات والنفقات
        👤 إدارة المستخدمين والصلاحيات
        💵 إدارة الرواتب والأجور
        
        🔑 بيانات الدخول التجريبية:
        👤 اسم المستخدم: admin
        🔒 كلمة المرور: admin123
        """
        
        info = QLabel(info_text)
        info.setAlignment(Qt.AlignmentFlag.AlignCenter)
        info.setStyleSheet("""
            color: #27ae60; 
            padding: 20px; 
            font-size: 12px;
            background-color: #f8f9fa;
            border-radius: 5px;
            margin: 10px;
        """)
        layout.addWidget(info)
        
        # زر تحميل النماذج
        load_btn = QPushButton("🚀 تحميل النماذج الكاملة")
        load_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 15px 30px;
                border-radius: 8px;
                font-size: 14px;
                font-weight: bold;
                margin: 10px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #21618c;
            }
        """)
        
        def load_forms():
            """تحميل النماذج"""
            try:
                # محاولة تحميل النماذج
                from src.ui.forms.product_form import ProductForm
                from src.ui.forms.invoice_form import InvoiceForm
                
                # إنشاء تبويبات
                from PyQt6.QtWidgets import QTabWidget
                tab_widget = QTabWidget()
                
                # إضافة النماذج
                tab_widget.addTab(ProductForm(), "🛍️ المنتجات")
                tab_widget.addTab(InvoiceForm(), "🧾 الفواتير")
                
                # استبدال المحتوى
                layout.addWidget(tab_widget)
                load_btn.setText("✅ تم تحميل النماذج!")
                load_btn.setEnabled(False)
                
            except Exception as e:
                load_btn.setText(f"❌ خطأ: {str(e)[:30]}")
        
        load_btn.clicked.connect(load_forms)
        layout.addWidget(load_btn)
        
        # عرض النافذة
        window.show()
        print("✅ تم فتح النافذة!")
        print("💡 النظام يعمل الآن!")
        
        # تشغيل التطبيق
        sys.exit(app.exec())
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        import traceback
        traceback.print_exc()

else:
    print("\n❌ لا يمكن تشغيل النظام!")
    print("💡 تأكد من تثبيت PyQt6:")
    print("   pip install --user PyQt6")
    print("   أو")
    print("   pip install PyQt6")

print("\n👋 انتهى التشغيل.")
