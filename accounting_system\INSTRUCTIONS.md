# 🚀 تعليمات تشغيل نظام المحاسبة المتكامل

## ⚡ التشغيل السريع (الطريقة الموصى بها)

```bash
python START_HERE.py
```

**هذا كل شيء!** الملف سيقوم بكل شيء تلقائياً:
- ✅ التحقق من المتطلبات
- ✅ إنشاء قاعدة البيانات
- ✅ إضافة البيانات التجريبية
- ✅ تشغيل النظام

---

## 📋 النماذج المتاحة

| النموذج | الوصف | الميزات |
|---------|--------|---------|
| 🛍️ **المنتجات** | إدارة المنتجات والمخزون | بحث، فلترة، تنبيهات المخزون |
| 🧾 **الفواتير** | إدارة الفواتير والمبيعات | حساب تلقائي، ترقيم تلقائي |
| 👥 **الموظفين** | إدارة الموظفين والموارد البشرية | معلومات شخصية ووظيفية |
| 📦 **المشتريات** | إدارة أوامر الشراء | استلام البضائع، تحديث المخزون |
| 💰 **المصروفات** | إدارة المصروفات والنفقات | تصنيف، ربط بالموظفين |
| 👤 **المستخدمين** | إدارة المستخدمين والصلاحيات | تشفير كلمات المرور، أدوار |
| 💵 **الرواتب** | إدارة الرواتب والأجور | حساب تلقائي، بدلات وخصومات |

---

## 🔑 بيانات الدخول التجريبية

- **اسم المستخدم:** `admin`
- **كلمة المرور:** `admin123`

---

## 🛠️ طرق التشغيل البديلة

### الطريقة اليدوية:
```bash
# 1. إعداد البيانات
python setup_test_data.py

# 2. تشغيل النظام
python test_all_forms.py
```

### الطريقة التفاعلية:
```bash
python run_test.py
```

---

## 📦 المتطلبات

- **Python 3.8+**
- **PyQt6** - للواجهة الرسومية
- **SQLAlchemy** - لقاعدة البيانات
- **bcrypt** - لتشفير كلمات المرور

### تثبيت المتطلبات:
```bash
pip install PyQt6 sqlalchemy bcrypt
```

---

## 🐛 حل المشاكل الشائعة

### ❌ خطأ: "No module named 'PyQt6'"
```bash
pip install PyQt6
```

### ❌ خطأ: "No module named 'sqlalchemy'"
```bash
pip install sqlalchemy
```

### ❌ خطأ: "No module named 'bcrypt'"
```bash
pip install bcrypt
```

### ❌ خطأ في قاعدة البيانات:
1. احذف ملف `accounting.db`
2. شغل `python setup_test_data.py`

### ❌ لا تظهر البيانات:
تأكد من تشغيل `setup_test_data.py` أولاً

---

## 🎯 ميزات النظام

### ✅ **التصميم:**
- واجهة عربية كاملة
- تصميم احترافي موحد
- ألوان متناسقة

### ✅ **الوظائف:**
- بحث وفلترة متقدمة
- نوافذ حوار منفصلة
- التحقق من صحة البيانات
- معالجة الأخطاء

### ✅ **الأمان:**
- تشفير كلمات المرور
- إدارة الصلاحيات
- التحقق من البيانات

---

## 📞 الدعم

إذا واجهت أي مشاكل:

1. **تحقق من رسائل الخطأ** في وحدة التحكم
2. **تأكد من تثبيت المتطلبات** بشكل صحيح
3. **جرب إعادة تشغيل النظام**
4. **احذف قاعدة البيانات وأعد إنشاؤها**

---

## 🎉 استمتع بالاختبار!

النظام جاهز للاستخدام الكامل. يمكنك اختبار جميع الميزات والتأكد من عمل النماذج بشكل صحيح.

**ملاحظة مهمة:** هذا نظام اختبار للتطوير. لا تستخدمه في بيئة الإنتاج بدون مراجعة إضافية للأمان والأداء.

---

## 🏁 البداية السريعة

1. **افتح Terminal/Command Prompt**
2. **انتقل إلى مجلد النظام**
3. **شغل الأمر:** `python START_HERE.py`
4. **اتبع التعليمات على الشاشة**
5. **استمتع بالاختبار!** 🚀
