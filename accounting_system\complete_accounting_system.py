#!/usr/bin/env python3
"""
نظام المحاسبة المتكامل الكامل
Complete Comprehensive Accounting System
"""
import sys
import os
from datetime import datetime, date
from decimal import Decimal

# إضافة مسار src للمشروع
current_dir = os.path.dirname(os.path.abspath(__file__))
src_path = os.path.join(current_dir, 'src')
sys.path.insert(0, src_path)
sys.path.insert(0, current_dir)

try:
    from PyQt6.QtWidgets import (
        QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, QWidget,
        QMenuBar, QMenu, QToolBar, QStatusBar, QTabWidget, QLabel,
        QPushButton, QMessageBox, QSplashScreen, QProgressBar,
        QSystemTrayIcon, QFrame, QGridLayout, QTableWidget, QTableWidgetItem,
        QLineEdit, QTextEdit, QComboBox, QDateEdit, QSpinBox, QDoubleSpinBox,
        QCheckBox, QFormLayout, QGroupBox, QDialog, QDialogButtonBox,
        QHeaderView, QAbstractItemView, QTreeWidget, QTreeWidgetItem,
        QFileDialog, QColorDialog, QFontDialog, QInputDialog
    )
    from PyQt6.QtCore import Qt, QTimer, QThread, pyqtSignal, QSize, QDate
    from PyQt6.QtGui import QFont, QIcon, QPixmap, QPainter, QColor, QAction
except ImportError as e:
    print(f"❌ خطأ في تحميل PyQt6: {e}")
    print("💡 لتثبيت PyQt6: pip install PyQt6")
    sys.exit(1)

class SplashScreen(QSplashScreen):
    """شاشة البداية المحسنة"""
    def __init__(self):
        # إنشاء صورة شاشة البداية
        pixmap = QPixmap(500, 350)
        pixmap.fill(QColor(44, 62, 80))
        
        painter = QPainter(pixmap)
        painter.setPen(QColor(255, 255, 255))
        
        # العنوان الرئيسي
        painter.setFont(QFont("Arial", 20, QFont.Weight.Bold))
        painter.drawText(pixmap.rect().adjusted(0, 50, 0, 0), Qt.AlignmentFlag.AlignHCenter | Qt.AlignmentFlag.AlignTop, 
                        "🏢 نظام المحاسبة المتكامل")
        
        # العنوان الفرعي
        painter.setFont(QFont("Arial", 14))
        painter.drawText(pixmap.rect().adjusted(0, 100, 0, 0), Qt.AlignmentFlag.AlignHCenter | Qt.AlignmentFlag.AlignTop, 
                        "Comprehensive Accounting System")
        
        # الميزات
        painter.setFont(QFont("Arial", 10))
        features = [
            "✅ إدارة العملاء والموردين",
            "✅ إدارة المنتجات والمخزون", 
            "✅ الفواتير والمبيعات",
            "✅ المشتريات وأوامر الشراء",
            "✅ إدارة الموظفين والرواتب",
            "✅ المحاسبة والتقارير المالية",
            "✅ إدارة المصروفات",
            "✅ النسخ الاحتياطي والأمان"
        ]
        
        y_pos = 150
        for feature in features:
            painter.drawText(50, y_pos, feature)
            y_pos += 20
        
        painter.setFont(QFont("Arial", 8))
        painter.drawText(pixmap.rect().adjusted(0, 0, 0, -20), Qt.AlignmentFlag.AlignHCenter | Qt.AlignmentFlag.AlignBottom, 
                        "جاري التحميل... Loading...")
        
        painter.end()
        
        super().__init__(pixmap)
        self.setWindowFlags(Qt.WindowType.WindowStaysOnTopHint | Qt.WindowType.FramelessWindowHint)

class LoginDialog(QDialog):
    """نافذة تسجيل الدخول"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.user_data = None
        self.setup_ui()
        
    def setup_ui(self):
        self.setWindowTitle("🔐 تسجيل الدخول")
        self.setFixedSize(400, 300)
        self.setModal(True)
        
        layout = QVBoxLayout()
        self.setLayout(layout)
        
        # شعار النظام
        logo_label = QLabel("🏢")
        logo_label.setFont(QFont("Arial", 48))
        logo_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(logo_label)
        
        # عنوان
        title_label = QLabel("نظام المحاسبة المتكامل")
        title_label.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)
        
        # نموذج تسجيل الدخول
        form_layout = QFormLayout()
        
        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText("اسم المستخدم")
        self.username_input.setText("admin")
        form_layout.addRow("👤 اسم المستخدم:", self.username_input)
        
        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText("كلمة المرور")
        self.password_input.setEchoMode(QLineEdit.EchoMode.Password)
        self.password_input.setText("admin123")
        form_layout.addRow("🔒 كلمة المرور:", self.password_input)
        
        layout.addLayout(form_layout)
        
        # أزرار
        buttons_layout = QHBoxLayout()
        
        login_btn = QPushButton("🚀 دخول")
        login_btn.clicked.connect(self.login)
        login_btn.setDefault(True)
        buttons_layout.addWidget(login_btn)
        
        cancel_btn = QPushButton("❌ إلغاء")
        cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(cancel_btn)
        
        layout.addLayout(buttons_layout)
        
        # ربط Enter بتسجيل الدخول
        self.username_input.returnPressed.connect(self.login)
        self.password_input.returnPressed.connect(self.login)
        
        # تطبيق الأنماط
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1, 
                    stop:0 #f8f9fa, stop:1 #e9ecef);
            }
            QLineEdit {
                padding: 10px;
                border: 2px solid #bdc3c7;
                border-radius: 6px;
                font-size: 12px;
            }
            QLineEdit:focus {
                border-color: #3498db;
            }
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
                margin: 5px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:default {
                background-color: #27ae60;
            }
        """)
    
    def login(self):
        username = self.username_input.text().strip()
        password = self.password_input.text()
        
        if not username or not password:
            QMessageBox.warning(self, "خطأ", "يرجى إدخال اسم المستخدم وكلمة المرور")
            return
        
        # تحقق بسيط (يمكن تطويره لاحقاً)
        if username == "admin" and password == "admin123":
            self.user_data = {
                'username': username,
                'full_name': 'مدير النظام',
                'role': 'admin',
                'login_time': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
            QMessageBox.information(self, "نجح تسجيل الدخول", f"مرحباً {username}!")
            self.accept()
        else:
            QMessageBox.critical(self, "فشل تسجيل الدخول", "اسم المستخدم أو كلمة المرور غير صحيحة")

class DataTableWidget(QWidget):
    """ويدجت جدول البيانات المحسن"""
    def __init__(self, title, columns, parent=None):
        super().__init__(parent)
        self.title = title
        self.columns = columns
        self.data = []
        self.setup_ui()
    
    def setup_ui(self):
        layout = QVBoxLayout()
        self.setLayout(layout)
        
        # عنوان الجدول
        title_label = QLabel(self.title)
        title_label.setFont(QFont("Arial", 14, QFont.Weight.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)
        
        # شريط الأدوات
        toolbar_layout = QHBoxLayout()
        
        self.add_btn = QPushButton("➕ إضافة")
        self.add_btn.clicked.connect(self.add_item)
        toolbar_layout.addWidget(self.add_btn)
        
        self.edit_btn = QPushButton("✏️ تعديل")
        self.edit_btn.clicked.connect(self.edit_item)
        toolbar_layout.addWidget(self.edit_btn)
        
        self.delete_btn = QPushButton("🗑️ حذف")
        self.delete_btn.clicked.connect(self.delete_item)
        toolbar_layout.addWidget(self.delete_btn)
        
        toolbar_layout.addStretch()
        
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("🔍 البحث...")
        self.search_input.textChanged.connect(self.search_data)
        toolbar_layout.addWidget(self.search_input)
        
        layout.addLayout(toolbar_layout)
        
        # الجدول
        self.table = QTableWidget()
        self.table.setColumnCount(len(self.columns))
        self.table.setHorizontalHeaderLabels(self.columns)
        self.table.horizontalHeader().setStretchLastSection(True)
        self.table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.table.setAlternatingRowColors(True)
        layout.addWidget(self.table)
        
        # شريط الحالة
        self.status_label = QLabel("جاهز")
        layout.addWidget(self.status_label)
    
    def add_item(self):
        """إضافة عنصر جديد"""
        dialog = DataEntryDialog(self.title, self.columns, parent=self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            data = dialog.get_data()
            self.data.append(data)
            self.refresh_table()
            self.status_label.setText(f"تم إضافة عنصر جديد - المجموع: {len(self.data)}")
    
    def edit_item(self):
        """تعديل العنصر المحدد"""
        current_row = self.table.currentRow()
        if current_row >= 0:
            dialog = DataEntryDialog(self.title, self.columns, self.data[current_row], parent=self)
            if dialog.exec() == QDialog.DialogCode.Accepted:
                data = dialog.get_data()
                self.data[current_row] = data
                self.refresh_table()
                self.status_label.setText("تم تحديث العنصر بنجاح")
        else:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار عنصر للتعديل")
    
    def delete_item(self):
        """حذف العنصر المحدد"""
        current_row = self.table.currentRow()
        if current_row >= 0:
            reply = QMessageBox.question(self, "تأكيد الحذف", "هل أنت متأكد من حذف هذا العنصر؟")
            if reply == QMessageBox.StandardButton.Yes:
                del self.data[current_row]
                self.refresh_table()
                self.status_label.setText(f"تم حذف العنصر - المجموع: {len(self.data)}")
        else:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار عنصر للحذف")
    
    def search_data(self, text):
        """البحث في البيانات"""
        for row in range(self.table.rowCount()):
            match = False
            for col in range(self.table.columnCount()):
                item = self.table.item(row, col)
                if item and text.lower() in item.text().lower():
                    match = True
                    break
            self.table.setRowHidden(row, not match)
    
    def refresh_table(self):
        """تحديث الجدول"""
        self.table.setRowCount(len(self.data))
        for row, item_data in enumerate(self.data):
            for col, value in enumerate(item_data):
                self.table.setItem(row, col, QTableWidgetItem(str(value)))

class DataEntryDialog(QDialog):
    """نافذة إدخال البيانات"""
    def __init__(self, title, fields, data=None, parent=None):
        super().__init__(parent)
        self.title = title
        self.fields = fields
        self.data = data or [""] * len(fields)
        self.inputs = []
        self.setup_ui()
    
    def setup_ui(self):
        self.setWindowTitle(f"{'تعديل' if self.data[0] else 'إضافة'} {self.title}")
        self.setModal(True)
        self.resize(400, 300)
        
        layout = QVBoxLayout()
        self.setLayout(layout)
        
        # نموذج الإدخال
        form_layout = QFormLayout()
        
        for i, field in enumerate(self.fields):
            input_widget = QLineEdit()
            input_widget.setText(str(self.data[i]) if i < len(self.data) else "")
            self.inputs.append(input_widget)
            form_layout.addRow(f"{field}:", input_widget)
        
        layout.addLayout(form_layout)
        
        # أزرار
        buttons = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)
        layout.addWidget(buttons)
    
    def get_data(self):
        """الحصول على البيانات المدخلة"""
        return [input_widget.text() for input_widget in self.inputs]

class ReportsWidget(QWidget):
    """ويدجت التقارير"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
    
    def setup_ui(self):
        layout = QVBoxLayout()
        self.setLayout(layout)
        
        # عنوان
        title_label = QLabel("📊 التقارير المالية")
        title_label.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)
        
        # شبكة التقارير
        reports_layout = QGridLayout()
        
        reports = [
            ("📈 تقرير المبيعات", "عرض تقرير المبيعات اليومي والشهري"),
            ("📦 تقرير المخزون", "حالة المخزون والمنتجات"),
            ("💰 الميزانية العمومية", "الأصول والخصوم وحقوق الملكية"),
            ("📋 قائمة الدخل", "الإيرادات والمصروفات والأرباح"),
            ("👥 تقرير الموظفين", "بيانات الموظفين والرواتب"),
            ("🧾 تقرير الفواتير", "فواتير المبيعات والمشتريات"),
            ("💳 تقرير المدفوعات", "المدفوعات والمقبوضات"),
            ("📊 التحليل المالي", "تحليل الأداء المالي")
        ]
        
        for i, (title, description) in enumerate(reports):
            report_frame = QFrame()
            report_frame.setFrameStyle(QFrame.Shape.Box)
            report_frame.setStyleSheet("""
                QFrame {
                    background-color: white;
                    border: 2px solid #bdc3c7;
                    border-radius: 8px;
                    padding: 10px;
                    margin: 5px;
                }
                QFrame:hover {
                    border-color: #3498db;
                    background-color: #f8f9fa;
                }
            """)
            
            frame_layout = QVBoxLayout()
            report_frame.setLayout(frame_layout)
            
            title_label = QLabel(title)
            title_label.setFont(QFont("Arial", 12, QFont.Weight.Bold))
            frame_layout.addWidget(title_label)
            
            desc_label = QLabel(description)
            desc_label.setWordWrap(True)
            desc_label.setStyleSheet("color: #7f8c8d;")
            frame_layout.addWidget(desc_label)
            
            view_btn = QPushButton("📄 عرض التقرير")
            view_btn.clicked.connect(lambda checked, t=title: self.show_report(t))
            frame_layout.addWidget(view_btn)
            
            row = i // 2
            col = i % 2
            reports_layout.addWidget(report_frame, row, col)
        
        layout.addLayout(reports_layout)
    
    def show_report(self, report_title):
        """عرض التقرير"""
        QMessageBox.information(self, "عرض التقرير", f"سيتم عرض {report_title}")

class SettingsWidget(QWidget):
    """ويدجت الإعدادات"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
    
    def setup_ui(self):
        layout = QVBoxLayout()
        self.setLayout(layout)
        
        # عنوان
        title_label = QLabel("⚙️ إعدادات النظام")
        title_label.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)
        
        # تبويبات الإعدادات
        tabs = QTabWidget()
        layout.addWidget(tabs)
        
        # إعدادات عامة
        general_widget = QWidget()
        general_layout = QFormLayout()
        general_widget.setLayout(general_layout)
        
        self.company_name = QLineEdit("شركة المحاسبة")
        general_layout.addRow("اسم الشركة:", self.company_name)
        
        self.currency = QComboBox()
        self.currency.addItems(["ريال سعودي", "دولار أمريكي", "يورو"])
        general_layout.addRow("العملة:", self.currency)
        
        self.language = QComboBox()
        self.language.addItems(["العربية", "English"])
        general_layout.addRow("اللغة:", self.language)
        
        tabs.addTab(general_widget, "🔧 عام")
        
        # إعدادات المظهر
        appearance_widget = QWidget()
        appearance_layout = QFormLayout()
        appearance_widget.setLayout(appearance_layout)
        
        self.theme = QComboBox()
        self.theme.addItems(["فاتح", "داكن", "تلقائي"])
        appearance_layout.addRow("النمط:", self.theme)
        
        self.font_size = QSpinBox()
        self.font_size.setRange(8, 24)
        self.font_size.setValue(12)
        appearance_layout.addRow("حجم الخط:", self.font_size)
        
        tabs.addTab(appearance_widget, "🎨 المظهر")
        
        # إعدادات النسخ الاحتياطي
        backup_widget = QWidget()
        backup_layout = QFormLayout()
        backup_widget.setLayout(backup_layout)
        
        self.auto_backup = QCheckBox("نسخ احتياطي تلقائي")
        self.auto_backup.setChecked(True)
        backup_layout.addRow("", self.auto_backup)
        
        self.backup_frequency = QComboBox()
        self.backup_frequency.addItems(["يومي", "أسبوعي", "شهري"])
        backup_layout.addRow("تكرار النسخ:", self.backup_frequency)
        
        backup_btn = QPushButton("💾 إنشاء نسخة احتياطية الآن")
        backup_btn.clicked.connect(self.create_backup)
        backup_layout.addRow("", backup_btn)
        
        tabs.addTab(backup_widget, "💾 النسخ الاحتياطي")
        
        # زر الحفظ
        save_btn = QPushButton("💾 حفظ الإعدادات")
        save_btn.clicked.connect(self.save_settings)
        layout.addWidget(save_btn)
    
    def create_backup(self):
        """إنشاء نسخة احتياطية"""
        QMessageBox.information(self, "نسخة احتياطية", "تم إنشاء النسخة الاحتياطية بنجاح!")
    
    def save_settings(self):
        """حفظ الإعدادات"""
        QMessageBox.information(self, "حفظ الإعدادات", "تم حفظ الإعدادات بنجاح!")

class ComprehensiveAccountingSystem(QMainWindow):
    """النظام الرئيسي الكامل"""
    
    def __init__(self):
        super().__init__()
        self.current_user = None
        
        # عرض نافذة تسجيل الدخول
        if not self.show_login():
            sys.exit()
        
        self.setWindowTitle("🏢 نظام المحاسبة المتكامل - Comprehensive Accounting System")
        self.setGeometry(100, 100, 1600, 1000)
        self.setMinimumSize(1400, 900)
        
        # إعداد الواجهة
        self.setup_ui()
        self.apply_styles()
        self.setup_menus()
        self.setup_toolbar()
        self.setup_statusbar()
        
        # تحميل البيانات التجريبية
        self.load_sample_data()
    
    def show_login(self):
        """عرض نافذة تسجيل الدخول"""
        login_dialog = LoginDialog()
        if login_dialog.exec() == QDialog.DialogCode.Accepted:
            self.current_user = login_dialog.user_data
            return True
        return False
