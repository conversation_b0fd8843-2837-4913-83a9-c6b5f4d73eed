#!/usr/bin/env python3
"""
نظام المحاسبة المتكامل الكامل
Complete Comprehensive Accounting System
"""
import sys
import os
from datetime import datetime, date
from decimal import Decimal

# إضافة مسار src للمشروع
current_dir = os.path.dirname(os.path.abspath(__file__))
src_path = os.path.join(current_dir, 'src')
sys.path.insert(0, src_path)
sys.path.insert(0, current_dir)

try:
    from PyQt6.QtWidgets import (
        QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, QWidget,
        QMenuBar, QMenu, QToolBar, QStatusBar, QTabWidget, QLabel,
        QPushButton, QMessageBox, QSplashScreen, QProgressBar,
        QSystemTrayIcon, QFrame, QGridLayout, QTableWidget, QTableWidgetItem,
        QLineEdit, QTextEdit, QComboBox, QDateEdit, QSpinBox, QDoubleSpinBox,
        QCheckBox, QFormLayout, QGroupBox, QDialog, QDialogButtonBox,
        QHeaderView, QAbstractItemView, QTreeWidget, QTreeWidgetItem,
        QFileDialog, QColorDialog, QFontDialog, QInputDialog
    )
    from PyQt6.QtCore import Qt, QTimer, QThread, pyqtSignal, QSize, QDate
    from PyQt6.QtGui import QFont, QIcon, QPixmap, QPainter, QColor, QAction
except ImportError as e:
    print(f"❌ خطأ في تحميل PyQt6: {e}")
    print("💡 لتثبيت PyQt6: pip install PyQt6")
    sys.exit(1)

class SplashScreen(QSplashScreen):
    """شاشة البداية المحسنة"""
    def __init__(self):
        # إنشاء صورة شاشة البداية
        pixmap = QPixmap(500, 350)
        pixmap.fill(QColor(44, 62, 80))

        painter = QPainter(pixmap)
        painter.setPen(QColor(255, 255, 255))

        # العنوان الرئيسي
        painter.setFont(QFont("Arial", 20, QFont.Weight.Bold))
        painter.drawText(pixmap.rect().adjusted(0, 50, 0, 0), Qt.AlignmentFlag.AlignHCenter | Qt.AlignmentFlag.AlignTop,
                        "🏢 نظام المحاسبة المتكامل")

        # العنوان الفرعي
        painter.setFont(QFont("Arial", 14))
        painter.drawText(pixmap.rect().adjusted(0, 100, 0, 0), Qt.AlignmentFlag.AlignHCenter | Qt.AlignmentFlag.AlignTop,
                        "Comprehensive Accounting System")

        # الميزات
        painter.setFont(QFont("Arial", 10))
        features = [
            "✅ إدارة العملاء والموردين",
            "✅ إدارة المنتجات والمخزون",
            "✅ الفواتير والمبيعات",
            "✅ المشتريات وأوامر الشراء",
            "✅ إدارة الموظفين والرواتب",
            "✅ المحاسبة والتقارير المالية",
            "✅ إدارة المصروفات",
            "✅ النسخ الاحتياطي والأمان"
        ]

        y_pos = 150
        for feature in features:
            painter.drawText(50, y_pos, feature)
            y_pos += 20

        painter.setFont(QFont("Arial", 8))
        painter.drawText(pixmap.rect().adjusted(0, 0, 0, -20), Qt.AlignmentFlag.AlignHCenter | Qt.AlignmentFlag.AlignBottom,
                        "جاري التحميل... Loading...")

        painter.end()

        super().__init__(pixmap)
        self.setWindowFlags(Qt.WindowType.WindowStaysOnTopHint | Qt.WindowType.FramelessWindowHint)

class LoginDialog(QDialog):
    """نافذة تسجيل الدخول"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.user_data = None
        self.setup_ui()

    def setup_ui(self):
        self.setWindowTitle("🔐 تسجيل الدخول")
        self.setFixedSize(400, 300)
        self.setModal(True)

        layout = QVBoxLayout()
        self.setLayout(layout)

        # شعار النظام
        logo_label = QLabel("🏢")
        logo_label.setFont(QFont("Arial", 48))
        logo_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(logo_label)

        # عنوان
        title_label = QLabel("نظام المحاسبة المتكامل")
        title_label.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)

        # نموذج تسجيل الدخول
        form_layout = QFormLayout()

        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText("اسم المستخدم")
        self.username_input.setText("admin")
        form_layout.addRow("👤 اسم المستخدم:", self.username_input)

        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText("كلمة المرور")
        self.password_input.setEchoMode(QLineEdit.EchoMode.Password)
        self.password_input.setText("admin123")
        form_layout.addRow("🔒 كلمة المرور:", self.password_input)

        layout.addLayout(form_layout)

        # أزرار
        buttons_layout = QHBoxLayout()

        login_btn = QPushButton("🚀 دخول")
        login_btn.clicked.connect(self.login)
        login_btn.setDefault(True)
        buttons_layout.addWidget(login_btn)

        cancel_btn = QPushButton("❌ إلغاء")
        cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(cancel_btn)

        layout.addLayout(buttons_layout)

        # ربط Enter بتسجيل الدخول
        self.username_input.returnPressed.connect(self.login)
        self.password_input.returnPressed.connect(self.login)

        # تطبيق الأنماط
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #f8f9fa, stop:1 #e9ecef);
            }
            QLineEdit {
                padding: 10px;
                border: 2px solid #bdc3c7;
                border-radius: 6px;
                font-size: 12px;
            }
            QLineEdit:focus {
                border-color: #3498db;
            }
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
                margin: 5px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:default {
                background-color: #27ae60;
            }
        """)

    def login(self):
        username = self.username_input.text().strip()
        password = self.password_input.text()

        if not username or not password:
            QMessageBox.warning(self, "خطأ", "يرجى إدخال اسم المستخدم وكلمة المرور")
            return

        # تحقق بسيط (يمكن تطويره لاحقاً)
        if username == "admin" and password == "admin123":
            self.user_data = {
                'username': username,
                'full_name': 'مدير النظام',
                'role': 'admin',
                'login_time': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
            QMessageBox.information(self, "نجح تسجيل الدخول", f"مرحباً {username}!")
            self.accept()
        else:
            QMessageBox.critical(self, "فشل تسجيل الدخول", "اسم المستخدم أو كلمة المرور غير صحيحة")

class DataTableWidget(QWidget):
    """ويدجت جدول البيانات المحسن"""
    def __init__(self, title, columns, parent=None):
        super().__init__(parent)
        self.title = title
        self.columns = columns
        self.data = []
        self.setup_ui()

    def setup_ui(self):
        layout = QVBoxLayout()
        self.setLayout(layout)

        # عنوان الجدول
        title_label = QLabel(self.title)
        title_label.setFont(QFont("Arial", 14, QFont.Weight.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)

        # شريط الأدوات
        toolbar_layout = QHBoxLayout()

        self.add_btn = QPushButton("➕ إضافة")
        self.add_btn.clicked.connect(self.add_item)
        toolbar_layout.addWidget(self.add_btn)

        self.edit_btn = QPushButton("✏️ تعديل")
        self.edit_btn.clicked.connect(self.edit_item)
        toolbar_layout.addWidget(self.edit_btn)

        self.delete_btn = QPushButton("🗑️ حذف")
        self.delete_btn.clicked.connect(self.delete_item)
        toolbar_layout.addWidget(self.delete_btn)

        toolbar_layout.addStretch()

        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("🔍 البحث...")
        self.search_input.textChanged.connect(self.search_data)
        toolbar_layout.addWidget(self.search_input)

        layout.addLayout(toolbar_layout)

        # الجدول
        self.table = QTableWidget()
        self.table.setColumnCount(len(self.columns))
        self.table.setHorizontalHeaderLabels(self.columns)
        self.table.horizontalHeader().setStretchLastSection(True)
        self.table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.table.setAlternatingRowColors(True)
        layout.addWidget(self.table)

        # شريط الحالة
        self.status_label = QLabel("جاهز")
        layout.addWidget(self.status_label)

    def add_item(self):
        """إضافة عنصر جديد"""
        dialog = DataEntryDialog(self.title, self.columns, parent=self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            data = dialog.get_data()
            self.data.append(data)
            self.refresh_table()
            self.status_label.setText(f"تم إضافة عنصر جديد - المجموع: {len(self.data)}")

    def edit_item(self):
        """تعديل العنصر المحدد"""
        current_row = self.table.currentRow()
        if current_row >= 0:
            dialog = DataEntryDialog(self.title, self.columns, self.data[current_row], parent=self)
            if dialog.exec() == QDialog.DialogCode.Accepted:
                data = dialog.get_data()
                self.data[current_row] = data
                self.refresh_table()
                self.status_label.setText("تم تحديث العنصر بنجاح")
        else:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار عنصر للتعديل")

    def delete_item(self):
        """حذف العنصر المحدد"""
        current_row = self.table.currentRow()
        if current_row >= 0:
            reply = QMessageBox.question(self, "تأكيد الحذف", "هل أنت متأكد من حذف هذا العنصر؟")
            if reply == QMessageBox.StandardButton.Yes:
                del self.data[current_row]
                self.refresh_table()
                self.status_label.setText(f"تم حذف العنصر - المجموع: {len(self.data)}")
        else:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار عنصر للحذف")

    def search_data(self, text):
        """البحث في البيانات"""
        for row in range(self.table.rowCount()):
            match = False
            for col in range(self.table.columnCount()):
                item = self.table.item(row, col)
                if item and text.lower() in item.text().lower():
                    match = True
                    break
            self.table.setRowHidden(row, not match)

    def refresh_table(self):
        """تحديث الجدول"""
        self.table.setRowCount(len(self.data))
        for row, item_data in enumerate(self.data):
            for col, value in enumerate(item_data):
                self.table.setItem(row, col, QTableWidgetItem(str(value)))

class DataEntryDialog(QDialog):
    """نافذة إدخال البيانات"""
    def __init__(self, title, fields, data=None, parent=None):
        super().__init__(parent)
        self.title = title
        self.fields = fields
        self.data = data or [""] * len(fields)
        self.inputs = []
        self.setup_ui()

    def setup_ui(self):
        self.setWindowTitle(f"{'تعديل' if self.data[0] else 'إضافة'} {self.title}")
        self.setModal(True)
        self.resize(400, 300)

        layout = QVBoxLayout()
        self.setLayout(layout)

        # نموذج الإدخال
        form_layout = QFormLayout()

        for i, field in enumerate(self.fields):
            input_widget = QLineEdit()
            input_widget.setText(str(self.data[i]) if i < len(self.data) else "")
            self.inputs.append(input_widget)
            form_layout.addRow(f"{field}:", input_widget)

        layout.addLayout(form_layout)

        # أزرار
        buttons = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)
        layout.addWidget(buttons)

    def get_data(self):
        """الحصول على البيانات المدخلة"""
        return [input_widget.text() for input_widget in self.inputs]

class ReportsWidget(QWidget):
    """ويدجت التقارير"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()

    def setup_ui(self):
        layout = QVBoxLayout()
        self.setLayout(layout)

        # عنوان
        title_label = QLabel("📊 التقارير المالية")
        title_label.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)

        # شبكة التقارير
        reports_layout = QGridLayout()

        reports = [
            ("📈 تقرير المبيعات", "عرض تقرير المبيعات اليومي والشهري"),
            ("📦 تقرير المخزون", "حالة المخزون والمنتجات"),
            ("💰 الميزانية العمومية", "الأصول والخصوم وحقوق الملكية"),
            ("📋 قائمة الدخل", "الإيرادات والمصروفات والأرباح"),
            ("👥 تقرير الموظفين", "بيانات الموظفين والرواتب"),
            ("🧾 تقرير الفواتير", "فواتير المبيعات والمشتريات"),
            ("💳 تقرير المدفوعات", "المدفوعات والمقبوضات"),
            ("📊 التحليل المالي", "تحليل الأداء المالي")
        ]

        for i, (title, description) in enumerate(reports):
            report_frame = QFrame()
            report_frame.setFrameStyle(QFrame.Shape.Box)
            report_frame.setStyleSheet("""
                QFrame {
                    background-color: white;
                    border: 2px solid #bdc3c7;
                    border-radius: 8px;
                    padding: 10px;
                    margin: 5px;
                }
                QFrame:hover {
                    border-color: #3498db;
                    background-color: #f8f9fa;
                }
            """)

            frame_layout = QVBoxLayout()
            report_frame.setLayout(frame_layout)

            title_label = QLabel(title)
            title_label.setFont(QFont("Arial", 12, QFont.Weight.Bold))
            frame_layout.addWidget(title_label)

            desc_label = QLabel(description)
            desc_label.setWordWrap(True)
            desc_label.setStyleSheet("color: #7f8c8d;")
            frame_layout.addWidget(desc_label)

            view_btn = QPushButton("📄 عرض التقرير")
            view_btn.clicked.connect(lambda checked, t=title: self.show_report(t))
            frame_layout.addWidget(view_btn)

            row = i // 2
            col = i % 2
            reports_layout.addWidget(report_frame, row, col)

        layout.addLayout(reports_layout)

    def show_report(self, report_title):
        """عرض التقرير"""
        QMessageBox.information(self, "عرض التقرير", f"سيتم عرض {report_title}")

class SettingsWidget(QWidget):
    """ويدجت الإعدادات"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()

    def setup_ui(self):
        layout = QVBoxLayout()
        self.setLayout(layout)

        # عنوان
        title_label = QLabel("⚙️ إعدادات النظام")
        title_label.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)

        # تبويبات الإعدادات
        tabs = QTabWidget()
        layout.addWidget(tabs)

        # إعدادات عامة
        general_widget = QWidget()
        general_layout = QFormLayout()
        general_widget.setLayout(general_layout)

        self.company_name = QLineEdit("شركة المحاسبة")
        general_layout.addRow("اسم الشركة:", self.company_name)

        self.currency = QComboBox()
        self.currency.addItems(["ريال سعودي", "دولار أمريكي", "يورو"])
        general_layout.addRow("العملة:", self.currency)

        self.language = QComboBox()
        self.language.addItems(["العربية", "English"])
        general_layout.addRow("اللغة:", self.language)

        tabs.addTab(general_widget, "🔧 عام")

        # إعدادات المظهر
        appearance_widget = QWidget()
        appearance_layout = QFormLayout()
        appearance_widget.setLayout(appearance_layout)

        self.theme = QComboBox()
        self.theme.addItems(["فاتح", "داكن", "تلقائي"])
        appearance_layout.addRow("النمط:", self.theme)

        self.font_size = QSpinBox()
        self.font_size.setRange(8, 24)
        self.font_size.setValue(12)
        appearance_layout.addRow("حجم الخط:", self.font_size)

        tabs.addTab(appearance_widget, "🎨 المظهر")

        # إعدادات النسخ الاحتياطي
        backup_widget = QWidget()
        backup_layout = QFormLayout()
        backup_widget.setLayout(backup_layout)

        self.auto_backup = QCheckBox("نسخ احتياطي تلقائي")
        self.auto_backup.setChecked(True)
        backup_layout.addRow("", self.auto_backup)

        self.backup_frequency = QComboBox()
        self.backup_frequency.addItems(["يومي", "أسبوعي", "شهري"])
        backup_layout.addRow("تكرار النسخ:", self.backup_frequency)

        backup_btn = QPushButton("💾 إنشاء نسخة احتياطية الآن")
        backup_btn.clicked.connect(self.create_backup)
        backup_layout.addRow("", backup_btn)

        tabs.addTab(backup_widget, "💾 النسخ الاحتياطي")

        # زر الحفظ
        save_btn = QPushButton("💾 حفظ الإعدادات")
        save_btn.clicked.connect(self.save_settings)
        layout.addWidget(save_btn)

    def create_backup(self):
        """إنشاء نسخة احتياطية"""
        QMessageBox.information(self, "نسخة احتياطية", "تم إنشاء النسخة الاحتياطية بنجاح!")

    def save_settings(self):
        """حفظ الإعدادات"""
        QMessageBox.information(self, "حفظ الإعدادات", "تم حفظ الإعدادات بنجاح!")

class ComprehensiveAccountingSystem(QMainWindow):
    """النظام الرئيسي الكامل"""

    def __init__(self):
        super().__init__()
        self.current_user = None

        # عرض نافذة تسجيل الدخول
        if not self.show_login():
            sys.exit()

        self.setWindowTitle("🏢 نظام المحاسبة المتكامل - Comprehensive Accounting System")
        self.setGeometry(100, 100, 1600, 1000)
        self.setMinimumSize(1400, 900)

        # إعداد الواجهة
        self.setup_ui()
        self.apply_styles()
        self.setup_menus()
        self.setup_toolbar()
        self.setup_statusbar()

        # تحميل البيانات التجريبية
        self.load_sample_data()

    def show_login(self):
        """عرض نافذة تسجيل الدخول"""
        login_dialog = LoginDialog()
        if login_dialog.exec() == QDialog.DialogCode.Accepted:
            self.current_user = login_dialog.user_data
            return True
        return False

    def setup_ui(self):
        """إعداد الواجهة الرئيسية"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        main_layout = QVBoxLayout()
        central_widget.setLayout(main_layout)

        # عنوان ترحيبي
        welcome_frame = QFrame()
        welcome_frame.setFrameStyle(QFrame.Shape.Box)
        welcome_layout = QVBoxLayout()
        welcome_frame.setLayout(welcome_layout)

        title_label = QLabel("🏢 نظام المحاسبة المتكامل")
        title_label.setFont(QFont("Arial", 20, QFont.Weight.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        welcome_layout.addWidget(title_label)

        user_label = QLabel(f"مرحباً {self.current_user['full_name']} - {self.current_user['role']}")
        user_label.setFont(QFont("Arial", 12))
        user_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        welcome_layout.addWidget(user_label)

        main_layout.addWidget(welcome_frame)

        # التبويبات الرئيسية
        self.tab_widget = QTabWidget()
        self.tab_widget.setTabPosition(QTabWidget.TabPosition.North)
        main_layout.addWidget(self.tab_widget)

        # إضافة التبويبات
        self.setup_tabs()

    def setup_tabs(self):
        """إعداد التبويبات"""
        # تبويب العملاء
        customers_widget = DataTableWidget(
            "العملاء",
            ["الكود", "الاسم", "الهاتف", "البريد الإلكتروني", "الرصيد"]
        )
        self.tab_widget.addTab(customers_widget, "👥 العملاء")

        # تبويب الموردين
        suppliers_widget = DataTableWidget(
            "الموردين",
            ["الكود", "الاسم", "الهاتف", "البريد الإلكتروني", "الرصيد"]
        )
        self.tab_widget.addTab(suppliers_widget, "🏭 الموردين")

        # تبويب المنتجات
        products_widget = DataTableWidget(
            "المنتجات",
            ["الكود", "الاسم", "التصنيف", "الوحدة", "سعر البيع", "الكمية"]
        )
        self.tab_widget.addTab(products_widget, "📦 المنتجات")

        # تبويب الفواتير
        invoices_widget = DataTableWidget(
            "الفواتير",
            ["رقم الفاتورة", "العميل", "التاريخ", "المبلغ", "الحالة"]
        )
        self.tab_widget.addTab(invoices_widget, "🧾 الفواتير")

        # تبويب المشتريات
        purchases_widget = DataTableWidget(
            "المشتريات",
            ["رقم الطلب", "المورد", "التاريخ", "المبلغ", "الحالة"]
        )
        self.tab_widget.addTab(purchases_widget, "🛒 المشتريات")

        # تبويب الموظفين
        employees_widget = DataTableWidget(
            "الموظفين",
            ["الكود", "الاسم", "القسم", "المنصب", "الراتب", "تاريخ التوظيف"]
        )
        self.tab_widget.addTab(employees_widget, "👨‍💼 الموظفين")

        # تبويب المصروفات
        expenses_widget = DataTableWidget(
            "المصروفات",
            ["رقم المصروف", "التصنيف", "الوصف", "المبلغ", "التاريخ"]
        )
        self.tab_widget.addTab(expenses_widget, "💰 المصروفات")

        # تبويب التقارير
        reports_widget = ReportsWidget()
        self.tab_widget.addTab(reports_widget, "📊 التقارير")

        # تبويب الإعدادات
        settings_widget = SettingsWidget()
        self.tab_widget.addTab(settings_widget, "⚙️ الإعدادات")

    def setup_menus(self):
        """إعداد القوائم"""
        menubar = self.menuBar()

        # قائمة الملف
        file_menu = menubar.addMenu("📁 ملف")

        new_action = QAction("🆕 جديد", self)
        new_action.setShortcut("Ctrl+N")
        file_menu.addAction(new_action)

        open_action = QAction("📂 فتح", self)
        open_action.setShortcut("Ctrl+O")
        file_menu.addAction(open_action)

        save_action = QAction("💾 حفظ", self)
        save_action.setShortcut("Ctrl+S")
        file_menu.addAction(save_action)

        file_menu.addSeparator()

        backup_action = QAction("💾 نسخة احتياطية", self)
        backup_action.triggered.connect(self.create_backup)
        file_menu.addAction(backup_action)

        file_menu.addSeparator()

        exit_action = QAction("🚪 خروج", self)
        exit_action.setShortcut("Ctrl+Q")
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)

        # قائمة البيانات
        data_menu = menubar.addMenu("📋 البيانات")

        customers_action = QAction("👥 العملاء", self)
        customers_action.triggered.connect(lambda: self.tab_widget.setCurrentIndex(0))
        data_menu.addAction(customers_action)

        suppliers_action = QAction("🏭 الموردين", self)
        suppliers_action.triggered.connect(lambda: self.tab_widget.setCurrentIndex(1))
        data_menu.addAction(suppliers_action)

        products_action = QAction("📦 المنتجات", self)
        products_action.triggered.connect(lambda: self.tab_widget.setCurrentIndex(2))
        data_menu.addAction(products_action)

        # قائمة المعاملات
        transactions_menu = menubar.addMenu("💼 المعاملات")

        invoices_action = QAction("🧾 الفواتير", self)
        invoices_action.triggered.connect(lambda: self.tab_widget.setCurrentIndex(3))
        transactions_menu.addAction(invoices_action)

        purchases_action = QAction("🛒 المشتريات", self)
        purchases_action.triggered.connect(lambda: self.tab_widget.setCurrentIndex(4))
        transactions_menu.addAction(purchases_action)

        expenses_action = QAction("💰 المصروفات", self)
        expenses_action.triggered.connect(lambda: self.tab_widget.setCurrentIndex(6))
        transactions_menu.addAction(expenses_action)

        # قائمة الموارد البشرية
        hr_menu = menubar.addMenu("👨‍💼 الموارد البشرية")

        employees_action = QAction("👨‍💼 الموظفين", self)
        employees_action.triggered.connect(lambda: self.tab_widget.setCurrentIndex(5))
        hr_menu.addAction(employees_action)

        salaries_action = QAction("💵 الرواتب", self)
        hr_menu.addAction(salaries_action)

        # قائمة التقارير
        reports_menu = menubar.addMenu("📊 التقارير")

        sales_report = QAction("📈 تقرير المبيعات", self)
        sales_report.triggered.connect(lambda: self.show_report("المبيعات"))
        reports_menu.addAction(sales_report)

        inventory_report = QAction("📦 تقرير المخزون", self)
        inventory_report.triggered.connect(lambda: self.show_report("المخزون"))
        reports_menu.addAction(inventory_report)

        financial_report = QAction("💰 التقرير المالي", self)
        financial_report.triggered.connect(lambda: self.show_report("المالي"))
        reports_menu.addAction(financial_report)

        reports_menu.addSeparator()
        reports_menu.addAction("📊 جميع التقارير").triggered.connect(lambda: self.tab_widget.setCurrentIndex(7))

        # قائمة الأدوات
        tools_menu = menubar.addMenu("🔧 أدوات")

        settings_action = QAction("⚙️ الإعدادات", self)
        settings_action.triggered.connect(lambda: self.tab_widget.setCurrentIndex(8))
        tools_menu.addAction(settings_action)

        calculator_action = QAction("🧮 آلة حاسبة", self)
        calculator_action.triggered.connect(self.open_calculator)
        tools_menu.addAction(calculator_action)

        # قائمة المساعدة
        help_menu = menubar.addMenu("❓ مساعدة")

        about_action = QAction("ℹ️ حول البرنامج", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)

        help_action = QAction("📖 دليل المستخدم", self)
        help_menu.addAction(help_action)

    def setup_toolbar(self):
        """إعداد شريط الأدوات"""
        toolbar = self.addToolBar("الأدوات الرئيسية")
        toolbar.setToolButtonStyle(Qt.ToolButtonStyle.ToolButtonTextBesideIcon)

        # أزرار سريعة
        customers_btn = QPushButton("👥 العملاء")
        customers_btn.clicked.connect(lambda: self.tab_widget.setCurrentIndex(0))
        toolbar.addWidget(customers_btn)

        products_btn = QPushButton("📦 المنتجات")
        products_btn.clicked.connect(lambda: self.tab_widget.setCurrentIndex(2))
        toolbar.addWidget(products_btn)

        invoices_btn = QPushButton("🧾 فاتورة جديدة")
        invoices_btn.clicked.connect(self.new_invoice)
        toolbar.addWidget(invoices_btn)

        toolbar.addSeparator()

        reports_btn = QPushButton("📊 التقارير")
        reports_btn.clicked.connect(lambda: self.tab_widget.setCurrentIndex(7))
        toolbar.addWidget(reports_btn)

        settings_btn = QPushButton("⚙️ الإعدادات")
        settings_btn.clicked.connect(lambda: self.tab_widget.setCurrentIndex(8))
        toolbar.addWidget(settings_btn)

    def setup_statusbar(self):
        """إعداد شريط الحالة"""
        statusbar = self.statusBar()

        # معلومات المستخدم
        user_label = QLabel(f"👤 {self.current_user['full_name']}")
        statusbar.addWidget(user_label)

        statusbar.addPermanentWidget(QLabel(" | "))

        # الوقت والتاريخ
        self.time_label = QLabel()
        self.update_time()
        statusbar.addPermanentWidget(self.time_label)

        statusbar.addPermanentWidget(QLabel(" | "))

        # حالة النظام
        status_label = QLabel("✅ النظام جاهز")
        statusbar.addPermanentWidget(status_label)

        # تحديث الوقت كل دقيقة
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_time)
        self.timer.start(60000)

    def update_time(self):
        """تحديث الوقت"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M")
        self.time_label.setText(f"🕐 {current_time}")

    def load_sample_data(self):
        """تحميل البيانات التجريبية"""
        # بيانات العملاء التجريبية
        customers_widget = self.tab_widget.widget(0)
        customers_widget.data = [
            ["C001", "أحمد محمد", "0501234567", "<EMAIL>", "5000.00"],
            ["C002", "فاطمة علي", "0507654321", "<EMAIL>", "3200.50"],
            ["C003", "محمد سالم", "0509876543", "<EMAIL>", "1800.75"],
            ["C004", "نورا أحمد", "0502468135", "<EMAIL>", "4500.25"],
        ]
        customers_widget.refresh_table()

        # بيانات الموردين التجريبية
        suppliers_widget = self.tab_widget.widget(1)
        suppliers_widget.data = [
            ["S001", "شركة التوريد المتقدم", "0112345678", "<EMAIL>", "15000.00"],
            ["S002", "مؤسسة الجودة", "0119876543", "<EMAIL>", "8500.50"],
            ["S003", "شركة الإمداد", "0114567890", "<EMAIL>", "12300.75"],
        ]
        suppliers_widget.refresh_table()

        # بيانات المنتجات التجريبية
        products_widget = self.tab_widget.widget(2)
        products_widget.data = [
            ["P001", "لابتوب ديل", "إلكترونيات", "قطعة", "3500.00", "25"],
            ["P002", "طابعة HP", "إلكترونيات", "قطعة", "800.00", "15"],
            ["P003", "كرسي مكتبي", "أثاث", "قطعة", "450.00", "50"],
            ["P004", "ورق A4", "مكتبية", "علبة", "25.00", "200"],
        ]
        products_widget.refresh_table()

        # بيانات الفواتير التجريبية
        invoices_widget = self.tab_widget.widget(3)
        invoices_widget.data = [
            ["INV001", "أحمد محمد", "2024-01-15", "3500.00", "مدفوعة"],
            ["INV002", "فاطمة علي", "2024-01-16", "1200.50", "معلقة"],
            ["INV003", "محمد سالم", "2024-01-17", "800.00", "مدفوعة"],
        ]
        invoices_widget.refresh_table()

        # بيانات المشتريات التجريبية
        purchases_widget = self.tab_widget.widget(4)
        purchases_widget.data = [
            ["PO001", "شركة التوريد المتقدم", "2024-01-10", "15000.00", "مستلمة"],
            ["PO002", "مؤسسة الجودة", "2024-01-12", "8500.00", "في الانتظار"],
        ]
        purchases_widget.refresh_table()

        # بيانات الموظفين التجريبية
        employees_widget = self.tab_widget.widget(5)
        employees_widget.data = [
            ["E001", "سارة أحمد", "المحاسبة", "محاسب", "8000.00", "2023-01-15"],
            ["E002", "خالد محمد", "المبيعات", "مندوب مبيعات", "6500.00", "2023-03-01"],
            ["E003", "منى علي", "الموارد البشرية", "أخصائي موارد بشرية", "7500.00", "2023-02-10"],
        ]
        employees_widget.refresh_table()

        # بيانات المصروفات التجريبية
        expenses_widget = self.tab_widget.widget(6)
        expenses_widget.data = [
            ["EXP001", "مصروفات إدارية", "إيجار المكتب", "5000.00", "2024-01-01"],
            ["EXP002", "مصروفات تشغيلية", "فواتير الكهرباء", "800.00", "2024-01-05"],
            ["EXP003", "مصروفات تسويقية", "إعلانات", "1200.00", "2024-01-10"],
        ]
        expenses_widget.refresh_table()

    def apply_styles(self):
        """تطبيق الأنماط"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f5f6fa;
            }

            QMenuBar {
                background-color: #2c3e50;
                color: white;
                border: none;
                padding: 5px;
                font-size: 12px;
                font-weight: bold;
            }

            QMenuBar::item {
                background-color: transparent;
                padding: 8px 12px;
                border-radius: 4px;
                margin: 2px;
            }

            QMenuBar::item:selected {
                background-color: #34495e;
            }

            QMenu {
                background-color: white;
                border: 1px solid #bdc3c7;
                border-radius: 6px;
                padding: 5px;
                font-size: 11px;
            }

            QMenu::item {
                padding: 8px 20px;
                border-radius: 4px;
                margin: 1px;
            }

            QMenu::item:selected {
                background-color: #3498db;
                color: white;
            }

            QToolBar {
                background-color: #ecf0f1;
                border: none;
                padding: 5px;
                spacing: 3px;
            }

            QToolBar QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 8px 12px;
                border-radius: 6px;
                font-weight: bold;
                margin: 2px;
            }

            QToolBar QPushButton:hover {
                background-color: #2980b9;
            }

            QTabWidget::pane {
                border: 1px solid #bdc3c7;
                background-color: white;
                border-radius: 8px;
                margin-top: 5px;
            }

            QTabBar::tab {
                background-color: #ecf0f1;
                color: #2c3e50;
                padding: 12px 20px;
                margin-right: 2px;
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
                font-weight: bold;
                min-width: 100px;
            }

            QTabBar::tab:selected {
                background-color: white;
                border-bottom: 3px solid #3498db;
            }

            QTabBar::tab:hover {
                background-color: #d5dbdb;
            }

            QStatusBar {
                background-color: #34495e;
                color: white;
                border: none;
                padding: 5px;
                font-weight: bold;
            }

            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 11px;
            }

            QPushButton:hover {
                background-color: #2980b9;
            }

            QPushButton:pressed {
                background-color: #21618c;
            }

            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #3498db, stop:1 #2980b9);
                border-radius: 10px;
                margin: 5px;
                color: white;
                padding: 15px;
            }

            QTableWidget {
                gridline-color: #bdc3c7;
                background-color: white;
                alternate-background-color: #f8f9fa;
                selection-background-color: #3498db;
                border: 1px solid #bdc3c7;
                border-radius: 6px;
            }

            QTableWidget::item {
                padding: 8px;
                border: none;
            }

            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 10px;
                border: none;
                font-weight: bold;
            }
        """)

    def new_invoice(self):
        """إنشاء فاتورة جديدة"""
        QMessageBox.information(self, "فاتورة جديدة", "سيتم فتح نافذة إنشاء فاتورة جديدة")

    def show_report(self, report_type):
        """عرض تقرير"""
        QMessageBox.information(self, "عرض التقرير", f"سيتم عرض تقرير {report_type}")

    def create_backup(self):
        """إنشاء نسخة احتياطية"""
        QMessageBox.information(self, "نسخة احتياطية", "تم إنشاء النسخة الاحتياطية بنجاح!")

    def open_calculator(self):
        """فتح آلة حاسبة"""
        try:
            import subprocess
            subprocess.Popen("calc.exe")
        except:
            QMessageBox.information(self, "آلة حاسبة", "لا يمكن فتح آلة حاسبة النظام")

    def show_about(self):
        """حول البرنامج"""
        QMessageBox.about(self, "حول البرنامج",
                         "🏢 نظام المحاسبة المتكامل\n\n"
                         "نظام شامل لإدارة الحسابات والمخزون والموارد البشرية\n\n"
                         "الميزات:\n"
                         "• إدارة العملاء والموردين\n"
                         "• إدارة المنتجات والمخزون\n"
                         "• الفواتير والمبيعات\n"
                         "• المشتريات وأوامر الشراء\n"
                         "• إدارة الموظفين والرواتب\n"
                         "• التقارير المالية\n"
                         "• النسخ الاحتياطي والأمان\n\n"
                         "الإصدار: 1.0\n"
                         "تطوير: فريق التطوير")

def main():
    """الدالة الرئيسية"""
    app = QApplication(sys.argv)
    app.setApplicationName("نظام المحاسبة المتكامل")
    app.setApplicationVersion("1.0")

    # شاشة البداية
    splash = SplashScreen()
    splash.show()

    # معالجة الأحداث لعرض شاشة البداية
    app.processEvents()

    # تأخير لعرض شاشة البداية
    QTimer.singleShot(3000, splash.close)

    # النافذة الرئيسية
    window = ComprehensiveAccountingSystem()

    # إظهار النافذة بعد إغلاق شاشة البداية
    QTimer.singleShot(3000, window.show)

    sys.exit(app.exec())

if __name__ == "__main__":
    main()
