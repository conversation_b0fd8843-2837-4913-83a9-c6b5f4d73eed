"""
Main entry point for the accounting system.
"""
import sys
import os

# Add the project root directory to Python path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import QTranslator, QLocale, Qt
from src.ui.login_window import LoginWindow
from src.ui.main_window import MainWindow
from src.database.database import init_db
from src.config import DEFAULT_LANGUAGE

def main():
    """Main entry point."""
    # Create the application
    app = QApplication(sys.argv)
    
    # Set up translation
    translator = QTranslator()
    if DEFAULT_LANGUAGE == 'ar':
        # Load Arabic translation file
        translator.load('ar', 'locales')
        app.installTranslator(translator)
    
    # Set up right-to-left layout for Arabic
    app.setLayoutDirection(Qt.LayoutDirection.RightToLeft if DEFAULT_LANGUAGE == 'ar' else Qt.LayoutDirection.LeftToRight)
    
    # Initialize database
    init_db()
    
    # Create login window
    login = LoginWindow()
    
    # Create main window
    main_window = MainWindow()
    
    # Connect login success to show main window
    def on_login_successful():
        main_window.show()
    
    login.login_successful.connect(on_login_successful)
    
    # Show login window
    login.show()
    
    # Start the event loop
    sys.exit(app.exec())

if __name__ == '__main__':
    main()

