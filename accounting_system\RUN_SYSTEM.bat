@echo off
echo.
echo ========================================
echo    🏢 نظام المحاسبة المتكامل
echo    Comprehensive Accounting System
echo ========================================
echo.

echo 🚀 بدء تشغيل النظام...
echo 🚀 Starting the system...
echo.

echo 🔍 التحقق من Python...
python --version
if errorlevel 1 (
    echo ❌ Python غير متاح!
    echo ❌ Python is not available!
    pause
    exit /b 1
)

echo.
echo 📁 الانتقال إلى مجلد النظام...
cd /d "%~dp0"
echo ✅ المجلد الحالي: %CD%

echo.
echo 🔧 محاولة تثبيت PyQt6...
echo 🔧 Attempting to install PyQt6...
python -m pip install --user PyQt6 --quiet
if errorlevel 1 (
    echo ⚠️ فشل التثبيت التلقائي
    echo ⚠️ Automatic installation failed
    echo 💡 جرب تشغيل الأمر يدوياً:
    echo 💡 Try running manually:
    echo    pip install PyQt6
    echo.
)

echo.
echo 🧪 اختبار PyQt6...
python -c "from PyQt6.QtWidgets import QApplication; print('✅ PyQt6 يعمل!')"
if errorlevel 1 (
    echo ❌ PyQt6 غير متاح!
    echo ❌ PyQt6 is not available!
    echo.
    echo 💡 لتثبيت PyQt6:
    echo 💡 To install PyQt6:
    echo    pip install PyQt6
    echo    أو / or
    echo    python -m pip install --user PyQt6
    echo.
    pause
    exit /b 1
)

echo.
echo 🚀 تشغيل النظام...
echo 🚀 Running the system...
python quick_start.py

if errorlevel 1 (
    echo.
    echo ❌ فشل في تشغيل النظام!
    echo ❌ Failed to run the system!
    echo.
    echo 🔄 محاولة تشغيل ملف بديل...
    echo 🔄 Trying alternative file...
    python immediate_launch.py
    
    if errorlevel 1 (
        echo ❌ فشل في جميع المحاولات!
        echo ❌ All attempts failed!
        echo.
        echo 💡 جرب تشغيل الأوامر يدوياً:
        echo 💡 Try running commands manually:
        echo    python quick_start.py
        echo    أو / or
        echo    python immediate_launch.py
    )
)

echo.
echo 👋 انتهى التشغيل
echo 👋 Execution completed
pause
