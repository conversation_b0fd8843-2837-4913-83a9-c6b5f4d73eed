"""
Database connection and configuration module.
"""
from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from src.config import DB_TYPE, DB_HOST, DB_PORT, DB_NAME, DB_USER, DB_PASSWORD

# Create database URL
if DB_TYPE == 'sqlite':
    import os
    from pathlib import Path
    # Get the project root directory
    project_root = Path(__file__).parent.parent.parent
    data_dir = project_root / "data"
    data_dir.mkdir(exist_ok=True)
    db_path = data_dir / "accounting.db"
    DATABASE_URL = f"sqlite:///{db_path}"
else:
    DATABASE_URL = f"mysql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}"

# Create database engine
engine = create_engine(DATABASE_URL, echo=True)

# Create session factory
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Session = SessionLocal  # Add this line to make Session available

# Create base class for declarative models
Base = declarative_base()

__all__ = ['engine', 'Session', 'Base']  # Export these symbols

def get_db():
    """
    Get database session.
    """
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

def init_db():
    """
    Initialize database and create all tables.
    """
    Base.metadata.create_all(bind=engine)
