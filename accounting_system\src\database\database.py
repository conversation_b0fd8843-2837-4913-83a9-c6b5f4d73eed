#!/usr/bin/env python3
"""
إعداد قاعدة البيانات الرئيسية
Main Database Setup
"""
import os
import sys
from sqlalchemy import create_engine, event
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool

# إضافة المسار الجذر للمشروع
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
sys.path.insert(0, project_root)

try:
    from config import get_database_url, DATABASE_CONFIG
except ImportError:
    # إعدادات افتراضية في حالة عدم وجود ملف التكوين
    from pathlib import Path
    project_root = Path(__file__).parent.parent.parent
    data_dir = project_root / "data"
    data_dir.mkdir(exist_ok=True)
    db_path = data_dir / "accounting.db"

    DATABASE_CONFIG = {
        'type': 'sqlite',
        'path': db_path,
        'echo': False
    }

    def get_database_url():
        return f"sqlite:///{db_path}"

# إنشاء قاعدة البيانات
Base = declarative_base()

# إنشاء محرك قاعدة البيانات
engine = create_engine(
    get_database_url(),
    echo=DATABASE_CONFIG.get('echo', False),
    poolclass=StaticPool if DATABASE_CONFIG['type'] == 'sqlite' else None,
    connect_args={'check_same_thread': False} if DATABASE_CONFIG['type'] == 'sqlite' else {}
)

# إنشاء جلسة قاعدة البيانات
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Session = SessionLocal  # للتوافق مع الكود القديم

def get_db():
    """الحصول على جلسة قاعدة البيانات"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

def init_db():
    """تهيئة قاعدة البيانات وإنشاء الجداول"""
    try:
        # إنشاء جميع الجداول
        Base.metadata.create_all(bind=engine)
        print("✅ تم إنشاء قاعدة البيانات والجداول بنجاح")
        return True
    except Exception as e:
        print(f"❌ خطأ في إنشاء قاعدة البيانات: {e}")
        return False

# تصدير الكائنات المهمة
__all__ = ['Base', 'engine', 'SessionLocal', 'Session', 'get_db', 'init_db']
