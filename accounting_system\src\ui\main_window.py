"""
Main window of the accounting system.
"""
from typing import Optional
import sys
from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout,
    QHBoxLayout, QPushButton, QLabel, QStackedWidget,
    QMenuBar, QStatusBar, QMenu, QMessageBox, QComboBox,
    QFileDialog
)
from PyQt6.QtCore import Qt, QSize, pyqtSignal
from PyQt6.QtGui import QIcon, QAction, QKeySequence
from src.ui.login_window import LoginWindow
from src.ui.forms.customer_form import CustomerForm
from src.ui.forms.supplier_form import SupplierForm
from src.ui.forms.product_form import ProductForm
from src.ui.forms.invoice_form import InvoiceForm
from src.ui.forms.purchase_form import PurchaseForm
from src.ui.forms.employee_form import EmployeeForm
from src.ui.forms.expense_form import ExpenseForm
from src.ui.forms.salary_form import SalaryForm
from src.ui.forms.user_management_form import UserManagementForm
from src.ui.forms.inventory_form import InventoryForm
from src.ui.forms.accounts_form import AccountsForm
from src.config import APP_NAME
from src.utils.translation import _, setup_translation, change_language
from src.reports.balance_sheet import BalanceSheetReport
from src.reports.income_statement import IncomeStatementReport
from src.utils.backup import create_backup, restore_backup
from src.utils.scheduler import backup_scheduler
from src.reports.inventory_report import InventoryReport

class MainWindow(QMainWindow):
    """Main window class."""

    def __init__(self):
        super().__init__()
        self.setWindowTitle(_(APP_NAME))
        self.setMinimumSize(1200, 800)

        # Initialize UI
        self._create_menu_bar()
        self._create_status_bar()
        self._create_central_widget()

        # Set window state
        self.showMaximized()

        # Start automatic backup scheduler
        backup_scheduler.start()

    def closeEvent(self, event):
        """Handle window close event."""
        # Stop backup scheduler
        backup_scheduler.stop()
        event.accept()

    def _create_menu_bar(self):
        """Create the menu bar."""
        menubar = self.menuBar()

        # File menu
        file_menu = menubar.addMenu(_('File') if _('File') != 'File' else 'ملف')

        # Add backup/restore actions
        backup_action = QAction(_('Create Backup') if _('Create Backup') != 'Create Backup' else 'إنشاء نسخة احتياطية', self)
        backup_action.triggered.connect(self._create_backup)
        file_menu.addAction(backup_action)

        restore_action = QAction(_('Restore Backup') if _('Restore Backup') != 'Restore Backup' else 'استعادة نسخة احتياطية', self)
        restore_action.triggered.connect(self._restore_backup)
        file_menu.addAction(restore_action)

        file_menu.addSeparator()

        settings_action = QAction(_('Settings') if _('Settings') != 'Settings' else 'إعدادات', self)
        settings_action.triggered.connect(self._show_settings)
        file_menu.addAction(settings_action)

        # Add automatic backup option to File menu
        auto_backup_action = QAction(_('Auto Backup Settings'), self)
        auto_backup_action.triggered.connect(self._show_auto_backup_settings)
        file_menu.addAction(auto_backup_action)

        # Language menu
        language_menu = menubar.addMenu(_('Language') if _('Language') != 'Language' else 'اللغة')
        arabic_action = QAction('العربية', self)
        arabic_action.triggered.connect(lambda: self._change_language('ar'))
        language_menu.addAction(arabic_action)
        english_action = QAction('English', self)
        english_action.triggered.connect(lambda: self._change_language('en'))
        language_menu.addAction(english_action)

        # Users menu
        users_menu = menubar.addMenu(_('Users') if _('Users') != 'Users' else 'المستخدمون')
        manage_users_action = QAction(_('Manage Users') if _('Manage Users') != 'Manage Users' else 'إدارة المستخدمين', self)
        manage_users_action.triggered.connect(self._show_user_management)
        users_menu.addAction(manage_users_action)

        # Reports menu
        reports_menu = menubar.addMenu(_('Reports') if _('Reports') != 'Reports' else 'التقارير')
        balance_sheet_action = QAction(_('Balance Sheet') if _('Balance Sheet') != 'Balance Sheet' else 'الميزانية العمومية', self)
        balance_sheet_action.triggered.connect(self._show_balance_sheet)
        reports_menu.addAction(balance_sheet_action)
        income_statement_action = QAction(_('Income Statement') if _('Income Statement') != 'Income Statement' else 'قائمة الدخل', self)
        income_statement_action.triggered.connect(self._show_income_statement)
        reports_menu.addAction(income_statement_action)

        # Add inventory report to Reports menu
        inventory_report_action = QAction(_('Inventory Report'), self)
        inventory_report_action.triggered.connect(self._show_inventory_report)
        reports_menu.addAction(inventory_report_action)

        # Add Excel export options
        reports_menu.addSeparator()
        excel_menu = reports_menu.addMenu(_('Export to Excel'))

        excel_inventory_action = QAction(_('Inventory'), self)
        excel_inventory_action.triggered.connect(self._export_inventory_excel)
        excel_menu.addAction(excel_inventory_action)

        # Help menu
        help_menu = menubar.addMenu(_('Help') if _('Help') != 'Help' else 'مساعدة')
        about_action = QAction(_('About') if _('About') != 'About' else 'حول', self)
        about_action.triggered.connect(self._show_about)
        help_menu.addAction(about_action)

    def _change_language(self, lang_code):
        """Change the application language."""
        change_language(lang_code)
        QMessageBox.information(self, _('Language Changed'),
                              _('Please restart the application for the changes to take effect.'))

    def _show_user_management(self):
        """Show user management form."""
        self._show_unique_widget(UserManagementForm)

    def _show_settings(self):
        """Show settings dialog."""
        QMessageBox.information(self, _('Settings'), _('Settings dialog will be added soon.'))

    def _show_balance_sheet(self):
        """Generate and show balance sheet report."""
        try:
            report = BalanceSheetReport()
            filename = report.generate()
            QMessageBox.information(
                self,
                _('Report Generated'),
                _('Balance sheet report has been generated: ') + filename
            )
        except Exception as e:
            QMessageBox.critical(
                self,
                _('Error'),
                _('Failed to generate balance sheet report: ') + str(e)
            )

    def _show_income_statement(self):
        """Generate and show income statement report."""
        try:
            report = IncomeStatementReport()
            filename = report.generate()
            QMessageBox.information(
                self,
                _('Report Generated'),
                _('Income statement report has been generated: ') + filename
            )
        except Exception as e:
            QMessageBox.critical(
                self,
                _('Error'),
                _('Failed to generate income statement report: ') + str(e)
            )

    def _show_about(self):
        """Show about dialog."""
        QMessageBox.information(self, _('About'), f"{_(APP_NAME)}\n{_('Version')} 1.0")

    def _show_sales(self):
        """Show sales module."""
        self._show_unique_widget(InvoiceForm)

    def _show_purchases(self):
        """Show purchases module."""
        self._show_unique_widget(PurchaseForm)

    def _show_customers(self):
        """Show customers module."""
        self._show_unique_widget(CustomerForm)

    def _show_suppliers(self):
        """Show suppliers module."""
        self._show_unique_widget(SupplierForm)

    def _show_products(self):
        """Show products module."""
        self._show_unique_widget(ProductForm)

    def _show_inventory(self):
        """Show inventory module."""
        self._show_unique_widget(InventoryForm)

    def _show_employees(self):
        """Show employees module."""
        self._show_unique_widget(EmployeeForm)

    def _show_salaries(self):
        """Show salaries module."""
        self._show_unique_widget(SalaryForm)

    def _show_expenses(self):
        """Show expenses module."""
        self._show_unique_widget(ExpenseForm)
    def _show_unique_widget(self, widget_class):
        """Show only one instance of a widget in the stacked widget."""
        # Check if widget of this class already exists
        for i in range(self.content_widget.count()):
            widget = self.content_widget.widget(i)
            if isinstance(widget, widget_class):
                self.content_widget.setCurrentWidget(widget)
                return
        # If not found, create and add
        widget = widget_class()
        self.content_widget.addWidget(widget)
        self.content_widget.setCurrentWidget(widget)

    def _show_accounts(self):
        """Show accounts module."""
        self._show_unique_widget(AccountsForm)

    def _create_status_bar(self):
        """Create the status bar."""
        status_bar = QStatusBar()
        self.setStatusBar(status_bar)
        status_bar.showMessage(_('Ready'))

    def _create_central_widget(self):
        """Create the central widget."""
        # Create main layout
        layout = QHBoxLayout()

        # Create navigation panel
        nav_panel = QWidget()
        nav_panel.setMaximumWidth(250)
        nav_layout = QVBoxLayout()
        nav_panel.setLayout(nav_layout)

        # Add buttons to navigation panel
        self.nav_buttons = {}
        nav_items = [
            ('customers', _('Customers')),
            ('suppliers', _('Suppliers')),
            ('inventory', _('Inventory')),
            ('purchases', _('Purchases')),
            ('sales', _('Sales')),
            ('employees', _('Employees')),
            ('payroll', _('Payroll')),
            ('expenses', _('Expenses')),
            ('accounts', _('Accounts'))
        ]

        for item_id, label in nav_items:
            btn = QPushButton(label)
            btn.setObjectName(f'nav_{item_id}')
            btn.setMinimumHeight(40)
            nav_layout.addWidget(btn)
            self.nav_buttons[item_id] = btn

        nav_layout.addStretch()
        layout.addWidget(nav_panel)

        # Create stacked widget for content
        self.content_widget = QStackedWidget()
        layout.addWidget(self.content_widget)

        # Create central widget
        central = QWidget()
        central.setLayout(layout)
        self.setCentralWidget(central)

        # Connect buttons to their respective functions
        self.nav_buttons['customers'].clicked.connect(self._show_customers)
        self.nav_buttons['suppliers'].clicked.connect(self._show_suppliers)
        self.nav_buttons['inventory'].clicked.connect(self._show_inventory)
        self.nav_buttons['purchases'].clicked.connect(self._show_purchases)
        self.nav_buttons['sales'].clicked.connect(self._show_sales)
        self.nav_buttons['employees'].clicked.connect(self._show_employees)
        self.nav_buttons['payroll'].clicked.connect(self._show_salaries)
        self.nav_buttons['expenses'].clicked.connect(self._show_expenses)
        self.nav_buttons['accounts'].clicked.connect(self._show_accounts)

    def _create_backup(self):
        """Create a backup of the system."""
        try:
            backup_file = create_backup()
            QMessageBox.information(
                self,
                _('Backup Created'),
                _('Backup has been created successfully: ') + backup_file
            )
        except Exception as e:
            QMessageBox.critical(
                self,
                _('Error'),
                _('Failed to create backup: ') + str(e)
            )

    def _restore_backup(self):
        """Restore the system from a backup."""
        file_dialog = QFileDialog()
        backup_file, _ = file_dialog.getOpenFileName(
            self,
            _('Select Backup File'),
            'backups',
            'Zip Files (*.zip)'
        )

        if backup_file:
            reply = QMessageBox.warning(
                self,
                _('Confirm Restore'),
                _('This will replace your current data with the backup. Are you sure?'),
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )

            if reply == QMessageBox.StandardButton.Yes:
                try:
                    if restore_backup(backup_file):
                        QMessageBox.information(
                            self,
                            _('Restore Complete'),
                            _('System has been restored from backup. Please restart the application.')
                        )
                        self.close()
                    else:
                        QMessageBox.critical(
                            self,
                            _('Error'),
                            _('Failed to restore from backup.')
                        )
                except Exception as e:
                    QMessageBox.critical(
                        self,
                        _('Error'),
                        _('Failed to restore from backup: ') + str(e)
                    )

    def _show_auto_backup_settings(self):
        """Show automatic backup settings dialog."""
        msg = _("Automatic backup is configured to run daily at 23:00.\n")
        msg += _("Backups are stored in 'backups/automatic' folder.\n")
        msg += _("Only last 7 days of backups are kept.")

        QMessageBox.information(self, _('Auto Backup Settings'), msg)

    def _show_inventory_report(self):
        """Generate and show inventory report."""
        try:
            report = InventoryReport()
            filename = report.generate()
            QMessageBox.information(
                self,
                _('Report Generated'),
                _('Inventory report has been generated: ') + filename
            )
        except Exception as e:
            QMessageBox.critical(
                self,
                _('Error'),
                _('Failed to generate inventory report: ') + str(e)
            )

    def _export_inventory_excel(self):
        """Export inventory report to Excel."""
        try:
            report = InventoryReport()
            filename = report.generate_excel()
            QMessageBox.information(
                self,
                _('Excel Report Generated'),
                _('Inventory Excel report has been generated: ') + filename
            )
        except Exception as e:
            QMessageBox.critical(
                self,
                _('Error'),
                _('Failed to generate Excel report: ') + str(e)
            )
