# 🧪 دليل اختبار نظام المحاسبة المتكامل
# Comprehensive Accounting System Test Guide

## 🚀 كيفية تشغيل النظام

### الطريقة السريعة (الموصى بها):
```bash
python run_test.py
```

### الطريقة اليدوية:
```bash
# 1. إعداد البيانات التجريبية
python setup_test_data.py

# 2. تشغيل النظام
python test_all_forms.py
```

## 📋 النماذج المتاحة للاختبار

### 1️⃣ نموذج المنتجات (Products)
- ✅ إضافة وتعديل وحذف المنتجات
- ✅ إدارة التصنيفات والوحدات
- ✅ تتبع المخزون والتنبيهات
- ✅ بحث وفلترة متقدمة

### 2️⃣ نموذج الفواتير (Invoices)
- ✅ إنشاء فواتير جديدة
- ✅ إضافة أصناف للفاتورة
- ✅ حساب تلقائي للمجاميع والضرائب
- ✅ ترقيم تلقائي (INV-000001...)

### 3️⃣ نموذج الموظفين (Employees)
- ✅ إدارة بيانات الموظفين الشخصية
- ✅ معلومات وظيفية ومالية
- ✅ ربط بالأقسام وأنواع التوظيف
- ✅ فلترة بالقسم والحالة

### 4️⃣ نموذج المشتريات (Purchases)
- ✅ إنشاء أوامر شراء
- ✅ إدارة أصناف أوامر الشراء
- ✅ استلام البضائع وتحديث المخزون
- ✅ ترقيم تلقائي (PO-000001...)

### 5️⃣ نموذج المصروفات (Expenses)
- ✅ تسجيل المصروفات
- ✅ تصنيف المصروفات
- ✅ ربط بالموظفين
- ✅ ترقيم تلقائي (EXP-000001...)

### 6️⃣ نموذج المستخدمين (Users)
- ✅ إدارة المستخدمين
- ✅ إدارة الأدوار والصلاحيات
- ✅ تشفير كلمات المرور
- ✅ فلترة بالأدوار والحالة

### 7️⃣ نموذج الرواتب (Salaries)
- ✅ معالجة الرواتب الشهرية
- ✅ حساب تلقائي للراتب الصافي
- ✅ إدارة البدلات والخصومات
- ✅ فلترة بالشهر والسنة

## 🔧 البيانات التجريبية

### 👤 مستخدم تجريبي:
- **اسم المستخدم:** admin
- **كلمة المرور:** admin123

### 🛍️ منتجات تجريبية:
- لابتوب ديل (PROD001)
- قميص قطني (PROD002)

### 👥 موظفين تجريبيين:
- محمد أحمد (EMP001) - مندوب مبيعات
- سارة محمد (EMP002) - محاسبة

### 🏢 عملاء وموردين:
- عملاء: أحمد محمد، فاطمة علي
- موردين: شركة التقنية المتقدمة، مؤسسة الملابس الحديثة

## 🎯 ميزات النظام

### ✅ التصميم الموحد:
- واجهة عربية كاملة
- تصميم احترافي موحد
- ألوان متناسقة

### ✅ الوظائف المتقدمة:
- بحث وفلترة في جميع النماذج
- نوافذ حوار منفصلة للإضافة والتعديل
- التحقق من صحة البيانات
- معالجة الأخطاء الشاملة

### ✅ سهولة الاستخدام:
- أزرار واضحة ومنظمة
- تلميحات مفيدة
- تنقل سهل بين النماذج
- رسائل تأكيد للعمليات الحساسة

## 🐛 استكشاف الأخطاء

### إذا واجهت خطأ في تحميل النماذج:
1. تأكد من وجود جميع الملفات في مجلد `src`
2. تحقق من تثبيت PyQt6: `pip install PyQt6`
3. تحقق من تثبيت SQLAlchemy: `pip install sqlalchemy`

### إذا واجهت خطأ في قاعدة البيانات:
1. احذف ملف `accounting.db` إن وجد
2. شغل `python setup_test_data.py` مرة أخرى

### إذا لم تظهر البيانات:
1. تأكد من تشغيل `setup_test_data.py` أولاً
2. تحقق من وجود ملف `accounting.db`

## 📞 الدعم

إذا واجهت أي مشاكل:
1. تحقق من رسائل الخطأ في وحدة التحكم
2. تأكد من تثبيت جميع المتطلبات
3. جرب إعادة تشغيل النظام

## 🎉 استمتع بالاختبار!

النظام جاهز للاستخدام الكامل. يمكنك اختبار جميع الميزات والتأكد من عمل النماذج بشكل صحيح.

**ملاحظة:** هذا نظام اختبار، لا تستخدمه في بيئة الإنتاج بدون مراجعة إضافية للأمان والأداء.
