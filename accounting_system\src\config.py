"""
Configuration settings for the accounting system.
"""
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Base directory
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

# Database Configuration
DB_TYPE = os.getenv('DB_TYPE', 'sqlite')  # or 'mysql'
DB_HOST = os.getenv('DB_HOST', 'localhost')
DB_PORT = os.getenv('DB_PORT', '3306')
DB_NAME = os.getenv('DB_NAME', 'accounting_db')
DB_USER = os.getenv('DB_USER', 'root')
DB_PASSWORD = os.getenv('DB_PASSWORD', '')

# Application Settings
APP_NAME = "نظام المحاسبة المتكامل"
APP_VERSION = "1.0.0"

# Language Settings
DEFAULT_LANGUAGE = os.getenv('DEFAULT_LANGUAGE', 'ar')  # 'ar' or 'en'
SUPPORTED_LANGUAGES = ['ar', 'en']

# Paths Configuration
BACKUP_DIR = os.path.join(BASE_DIR, 'backups')
REPORTS_DIR = os.path.join(BASE_DIR, 'reports')
LOCALE_DIR = os.path.join(BASE_DIR, 'src', 'locales')

# Create necessary directories if they don't exist
for directory in [BACKUP_DIR, REPORTS_DIR]:
    if not os.path.exists(directory):
        os.makedirs(directory)

# Security Configuration
SECRET_KEY = os.getenv('SECRET_KEY', 'your-secret-key-here')
TOKEN_EXPIRATION = 24  # hours

# Report Generation Settings
COMPANY_NAME = os.getenv('COMPANY_NAME', 'شركتي')
COMPANY_ADDRESS = os.getenv('COMPANY_ADDRESS', 'العنوان')
COMPANY_PHONE = os.getenv('COMPANY_PHONE', '**********')
COMPANY_EMAIL = os.getenv('COMPANY_EMAIL', '<EMAIL>')
COMPANY_WEBSITE = os.getenv('COMPANY_WEBSITE', 'www.company.com')
COMPANY_LOGO = os.getenv('COMPANY_LOGO', os.path.join(BASE_DIR, 'assets', 'logo.png'))
