from reportlab.lib.pagesizes import A4
from reportlab.pdfgen import canvas
from src.database.database import SessionLocal
from src.models.accounting import Customer
import os

def generate_customer_report(filename: str = None):
    """Generate a PDF report for all customers."""
    session = SessionLocal()
    customers = session.query(Customer).all()
    if not filename:
        filename = os.path.join(os.getcwd(), 'customer_report.pdf')
    c = canvas.Canvas(filename, pagesize=A4)
    width, height = A4
    c.setFont("Helvetica-Bold", 16)
    c.drawString(200, height - 50, "تقرير العملاء / Customer Report")
    c.setFont("Helvetica", 12)
    y = height - 100
    c.drawString(50, y, "ID")
    c.drawString(100, y, "Name")
    c.drawString(300, y, "Phone")
    c.drawString(450, y, "Address")
    y -= 20
    for customer in customers:
        c.drawString(50, y, str(customer.id))
        c.drawString(100, y, customer.name)
        c.drawString(300, y, customer.phone or "-")
        c.drawString(450, y, customer.address or "-")
        y -= 20
        if y < 50:
            c.showPage()
            y = height - 50
    c.save()
    session.close()
    return filename
