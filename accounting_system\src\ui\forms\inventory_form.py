"""
Inventory Management Form
نموذج إدارة المخزون
"""
from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton, 
                            QTableWidget, QTableWidgetItem, QHeaderView, 
                            QMessageBox, QDialog, QFormLayout, QLineEdit, 
                            QComboBox, QSpinBox, QDoubleSpinBox, QTextEdit,
                            QLabel, QGroupBox, QGridLayout, QDateEdit)
from PyQt6.QtCore import Qt, QDate
from PyQt6.QtGui import QFont
from sqlalchemy.orm import sessionmaker
from src.database.database import engine
from src.models.inventory import Product, InventoryTransaction
from src.utils.translations import _

SessionLocal = sessionmaker(bind=engine)

class InventoryForm(QWidget):
    """Inventory management form."""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        self.load_data()
    
    def init_ui(self):
        """Initialize the user interface."""
        layout = QVBoxLayout()
        
        # Title
        title = QLabel(_('Inventory Management') if _('Inventory Management') != 'Inventory Management' else 'إدارة المخزون')
        title.setFont(QFont('Arial', 16, QFont.Weight.Bold))
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title)
        
        # Create tabs for different inventory operations
        self.create_inventory_overview(layout)
        self.create_stock_movements(layout)
        
        self.setLayout(layout)
    
    def create_inventory_overview(self, parent_layout):
        """Create inventory overview section."""
        # Group box for inventory overview
        overview_group = QGroupBox(_('Current Inventory') if _('Current Inventory') != 'Current Inventory' else 'المخزون الحالي')
        overview_layout = QVBoxLayout()
        
        # Buttons
        button_layout = QHBoxLayout()
        
        self.add_product_btn = QPushButton(_('Add Product') if _('Add Product') != 'Add Product' else 'إضافة منتج')
        self.add_product_btn.clicked.connect(self.add_product)
        button_layout.addWidget(self.add_product_btn)
        
        self.edit_product_btn = QPushButton(_('Edit Product') if _('Edit Product') != 'Edit Product' else 'تعديل منتج')
        self.edit_product_btn.clicked.connect(self.edit_product)
        button_layout.addWidget(self.edit_product_btn)
        
        self.stock_in_btn = QPushButton(_('Stock In') if _('Stock In') != 'Stock In' else 'إدخال مخزون')
        self.stock_in_btn.clicked.connect(self.stock_in)
        button_layout.addWidget(self.stock_in_btn)
        
        self.stock_out_btn = QPushButton(_('Stock Out') if _('Stock Out') != 'Stock Out' else 'إخراج مخزون')
        self.stock_out_btn.clicked.connect(self.stock_out)
        button_layout.addWidget(self.stock_out_btn)
        
        self.refresh_btn = QPushButton(_('Refresh') if _('Refresh') != 'Refresh' else 'تحديث')
        self.refresh_btn.clicked.connect(self.load_data)
        button_layout.addWidget(self.refresh_btn)
        
        button_layout.addStretch()
        overview_layout.addLayout(button_layout)
        
        # Products table
        self.products_table = QTableWidget()
        self.products_table.setColumnCount(8)
        headers = [
            _('Code') if _('Code') != 'Code' else 'الكود',
            _('Name') if _('Name') != 'Name' else 'الاسم',
            _('Category') if _('Category') != 'Category' else 'الفئة',
            _('Quantity') if _('Quantity') != 'Quantity' else 'الكمية',
            _('Unit') if _('Unit') != 'Unit' else 'الوحدة',
            _('Unit Price') if _('Unit Price') != 'Unit Price' else 'سعر الوحدة',
            _('Total Value') if _('Total Value') != 'Total Value' else 'القيمة الإجمالية',
            _('Status') if _('Status') != 'Status' else 'الحالة'
        ]
        self.products_table.setHorizontalHeaderLabels(headers)
        self.products_table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        self.products_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.products_table.setAlternatingRowColors(True)
        
        overview_layout.addWidget(self.products_table)
        overview_group.setLayout(overview_layout)
        parent_layout.addWidget(overview_group)
    
    def create_stock_movements(self, parent_layout):
        """Create stock movements section."""
        movements_group = QGroupBox(_('Recent Stock Movements') if _('Recent Stock Movements') != 'Recent Stock Movements' else 'حركات المخزون الأخيرة')
        movements_layout = QVBoxLayout()
        
        # Movements table
        self.movements_table = QTableWidget()
        self.movements_table.setColumnCount(6)
        headers = [
            _('Date') if _('Date') != 'Date' else 'التاريخ',
            _('Product') if _('Product') != 'Product' else 'المنتج',
            _('Type') if _('Type') != 'Type' else 'النوع',
            _('Quantity') if _('Quantity') != 'Quantity' else 'الكمية',
            _('Unit Price') if _('Unit Price') != 'Unit Price' else 'سعر الوحدة',
            _('Notes') if _('Notes') != 'Notes' else 'ملاحظات'
        ]
        self.movements_table.setHorizontalHeaderLabels(headers)
        self.movements_table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        self.movements_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.movements_table.setAlternatingRowColors(True)
        self.movements_table.setMaximumHeight(200)
        
        movements_layout.addWidget(self.movements_table)
        movements_group.setLayout(movements_layout)
        parent_layout.addWidget(movements_group)
    
    def load_data(self):
        """Load inventory data."""
        self.load_products()
        self.load_movements()
    
    def load_products(self):
        """Load products data."""
        session = SessionLocal()
        try:
            products = session.query(Product).all()
            self.products_table.setRowCount(len(products))
            
            for i, product in enumerate(products):
                self.products_table.setItem(i, 0, QTableWidgetItem(product.code))
                self.products_table.setItem(i, 1, QTableWidgetItem(product.name))
                self.products_table.setItem(i, 2, QTableWidgetItem(product.category or ''))
                self.products_table.setItem(i, 3, QTableWidgetItem(str(product.quantity)))
                self.products_table.setItem(i, 4, QTableWidgetItem(product.unit or ''))
                self.products_table.setItem(i, 5, QTableWidgetItem(f"{product.unit_price:.2f}"))
                
                total_value = product.quantity * product.unit_price
                self.products_table.setItem(i, 6, QTableWidgetItem(f"{total_value:.2f}"))
                
                # Status based on minimum quantity
                if product.quantity <= product.minimum_quantity:
                    status = _('Low Stock') if _('Low Stock') != 'Low Stock' else 'مخزون منخفض'
                    status_item = QTableWidgetItem(status)
                    status_item.setBackground(Qt.GlobalColor.red)
                elif product.quantity <= product.minimum_quantity * 1.5:
                    status = _('Warning') if _('Warning') != 'Warning' else 'تحذير'
                    status_item = QTableWidgetItem(status)
                    status_item.setBackground(Qt.GlobalColor.yellow)
                else:
                    status = _('Normal') if _('Normal') != 'Normal' else 'عادي'
                    status_item = QTableWidgetItem(status)
                    status_item.setBackground(Qt.GlobalColor.green)
                
                self.products_table.setItem(i, 7, status_item)
                
        finally:
            session.close()
    
    def load_movements(self):
        """Load recent stock movements."""
        session = SessionLocal()
        try:
            movements = session.query(InventoryTransaction).order_by(
                InventoryTransaction.transaction_date.desc()
            ).limit(20).all()
            
            self.movements_table.setRowCount(len(movements))
            
            for i, movement in enumerate(movements):
                self.movements_table.setItem(i, 0, QTableWidgetItem(movement.transaction_date.strftime('%Y-%m-%d')))
                self.movements_table.setItem(i, 1, QTableWidgetItem(movement.product.name))
                
                trans_type = _('In') if movement.transaction_type == 'IN' else _('Out')
                if trans_type == 'In' and _('In') == 'In':
                    trans_type = 'دخول'
                elif trans_type == 'Out' and _('Out') == 'Out':
                    trans_type = 'خروج'
                    
                self.movements_table.setItem(i, 2, QTableWidgetItem(trans_type))
                self.movements_table.setItem(i, 3, QTableWidgetItem(str(movement.quantity)))
                self.movements_table.setItem(i, 4, QTableWidgetItem(f"{movement.unit_price:.2f}"))
                self.movements_table.setItem(i, 5, QTableWidgetItem(movement.notes or ''))
                
        finally:
            session.close()
    
    def add_product(self):
        """Add new product."""
        dialog = ProductDialog(self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            self.load_products()
    
    def edit_product(self):
        """Edit selected product."""
        current_row = self.products_table.currentRow()
        if current_row >= 0:
            product_code = self.products_table.item(current_row, 0).text()
            session = SessionLocal()
            try:
                product = session.query(Product).filter(Product.code == product_code).first()
                if product:
                    dialog = ProductDialog(self, product)
                    if dialog.exec() == QDialog.DialogCode.Accepted:
                        self.load_products()
            finally:
                session.close()
        else:
            QMessageBox.warning(self, _('Warning'), _('Please select a product to edit.'))
    
    def stock_in(self):
        """Stock in operation."""
        current_row = self.products_table.currentRow()
        if current_row >= 0:
            product_code = self.products_table.item(current_row, 0).text()
            dialog = StockMovementDialog(self, product_code, 'IN')
            if dialog.exec() == QDialog.DialogCode.Accepted:
                self.load_data()
        else:
            QMessageBox.warning(self, _('Warning'), _('Please select a product for stock in.'))
    
    def stock_out(self):
        """Stock out operation."""
        current_row = self.products_table.currentRow()
        if current_row >= 0:
            product_code = self.products_table.item(current_row, 0).text()
            dialog = StockMovementDialog(self, product_code, 'OUT')
            if dialog.exec() == QDialog.DialogCode.Accepted:
                self.load_data()
        else:
            QMessageBox.warning(self, _('Warning'), _('Please select a product for stock out.'))


class ProductDialog(QDialog):
    """Dialog for adding/editing products."""
    
    def __init__(self, parent=None, product=None):
        super().__init__(parent)
        self.product = product
        self.init_ui()
        if product:
            self.load_product_data()
    
    def init_ui(self):
        """Initialize the user interface."""
        self.setWindowTitle(_('Product Details') if _('Product Details') != 'Product Details' else 'تفاصيل المنتج')
        self.setModal(True)
        self.resize(400, 500)
        
        layout = QFormLayout()
        
        # Product fields
        self.code_edit = QLineEdit()
        self.name_edit = QLineEdit()
        self.category_edit = QLineEdit()
        self.description_edit = QTextEdit()
        self.description_edit.setMaximumHeight(80)
        self.unit_edit = QLineEdit()
        self.unit_price_edit = QDoubleSpinBox()
        self.unit_price_edit.setMaximum(999999.99)
        self.unit_price_edit.setDecimals(2)
        self.quantity_edit = QSpinBox()
        self.quantity_edit.setMaximum(999999)
        self.minimum_quantity_edit = QSpinBox()
        self.minimum_quantity_edit.setMaximum(999999)
        
        layout.addRow(_('Code') if _('Code') != 'Code' else 'الكود:', self.code_edit)
        layout.addRow(_('Name') if _('Name') != 'Name' else 'الاسم:', self.name_edit)
        layout.addRow(_('Category') if _('Category') != 'Category' else 'الفئة:', self.category_edit)
        layout.addRow(_('Description') if _('Description') != 'Description' else 'الوصف:', self.description_edit)
        layout.addRow(_('Unit') if _('Unit') != 'Unit' else 'الوحدة:', self.unit_edit)
        layout.addRow(_('Unit Price') if _('Unit Price') != 'Unit Price' else 'سعر الوحدة:', self.unit_price_edit)
        layout.addRow(_('Quantity') if _('Quantity') != 'Quantity' else 'الكمية:', self.quantity_edit)
        layout.addRow(_('Minimum Quantity') if _('Minimum Quantity') != 'Minimum Quantity' else 'الحد الأدنى:', self.minimum_quantity_edit)
        
        # Buttons
        button_layout = QHBoxLayout()
        self.save_btn = QPushButton(_('Save') if _('Save') != 'Save' else 'حفظ')
        self.save_btn.clicked.connect(self.save_product)
        self.cancel_btn = QPushButton(_('Cancel') if _('Cancel') != 'Cancel' else 'إلغاء')
        self.cancel_btn.clicked.connect(self.reject)
        
        button_layout.addWidget(self.save_btn)
        button_layout.addWidget(self.cancel_btn)
        
        layout.addRow(button_layout)
        self.setLayout(layout)
    
    def load_product_data(self):
        """Load product data for editing."""
        if self.product:
            self.code_edit.setText(self.product.code)
            self.code_edit.setReadOnly(True)  # Code shouldn't be changed
            self.name_edit.setText(self.product.name)
            self.category_edit.setText(self.product.category or '')
            self.description_edit.setPlainText(self.product.description or '')
            self.unit_edit.setText(self.product.unit or '')
            self.unit_price_edit.setValue(self.product.unit_price)
            self.quantity_edit.setValue(self.product.quantity)
            self.minimum_quantity_edit.setValue(self.product.minimum_quantity)
    
    def save_product(self):
        """Save product data."""
        if not self.code_edit.text().strip():
            QMessageBox.warning(self, _('Warning'), _('Product code is required.'))
            return
        
        if not self.name_edit.text().strip():
            QMessageBox.warning(self, _('Warning'), _('Product name is required.'))
            return
        
        session = SessionLocal()
        try:
            if self.product:
                # Update existing product
                product = session.merge(self.product)
            else:
                # Create new product
                # Check if code already exists
                existing = session.query(Product).filter(Product.code == self.code_edit.text().strip()).first()
                if existing:
                    QMessageBox.warning(self, _('Warning'), _('Product code already exists.'))
                    return
                
                product = Product()
                product.code = self.code_edit.text().strip()
            
            product.name = self.name_edit.text().strip()
            product.category = self.category_edit.text().strip() or None
            product.description = self.description_edit.toPlainText().strip() or None
            product.unit = self.unit_edit.text().strip() or None
            product.unit_price = self.unit_price_edit.value()
            product.quantity = self.quantity_edit.value()
            product.minimum_quantity = self.minimum_quantity_edit.value()
            
            if not self.product:
                session.add(product)
            
            session.commit()
            self.accept()
            
        except Exception as e:
            session.rollback()
            QMessageBox.critical(self, _('Error'), f"Failed to save product: {str(e)}")
        finally:
            session.close()


class StockMovementDialog(QDialog):
    """Dialog for stock movements."""
    
    def __init__(self, parent=None, product_code=None, movement_type='IN'):
        super().__init__(parent)
        self.product_code = product_code
        self.movement_type = movement_type
        self.init_ui()
        self.load_product_info()
    
    def init_ui(self):
        """Initialize the user interface."""
        title = _('Stock In') if self.movement_type == 'IN' else _('Stock Out')
        if title == 'Stock In' and _('Stock In') == 'Stock In':
            title = 'إدخال مخزون'
        elif title == 'Stock Out' and _('Stock Out') == 'Stock Out':
            title = 'إخراج مخزون'
            
        self.setWindowTitle(title)
        self.setModal(True)
        self.resize(400, 300)
        
        layout = QFormLayout()
        
        # Product info (read-only)
        self.product_info = QLabel()
        layout.addRow(_('Product') if _('Product') != 'Product' else 'المنتج:', self.product_info)
        
        # Movement fields
        self.date_edit = QDateEdit()
        self.date_edit.setDate(QDate.currentDate())
        self.quantity_edit = QSpinBox()
        self.quantity_edit.setMaximum(999999)
        self.quantity_edit.setMinimum(1)
        self.unit_price_edit = QDoubleSpinBox()
        self.unit_price_edit.setMaximum(999999.99)
        self.unit_price_edit.setDecimals(2)
        self.notes_edit = QTextEdit()
        self.notes_edit.setMaximumHeight(80)
        
        layout.addRow(_('Date') if _('Date') != 'Date' else 'التاريخ:', self.date_edit)
        layout.addRow(_('Quantity') if _('Quantity') != 'Quantity' else 'الكمية:', self.quantity_edit)
        layout.addRow(_('Unit Price') if _('Unit Price') != 'Unit Price' else 'سعر الوحدة:', self.unit_price_edit)
        layout.addRow(_('Notes') if _('Notes') != 'Notes' else 'ملاحظات:', self.notes_edit)
        
        # Buttons
        button_layout = QHBoxLayout()
        self.save_btn = QPushButton(_('Save') if _('Save') != 'Save' else 'حفظ')
        self.save_btn.clicked.connect(self.save_movement)
        self.cancel_btn = QPushButton(_('Cancel') if _('Cancel') != 'Cancel' else 'إلغاء')
        self.cancel_btn.clicked.connect(self.reject)
        
        button_layout.addWidget(self.save_btn)
        button_layout.addWidget(self.cancel_btn)
        
        layout.addRow(button_layout)
        self.setLayout(layout)
    
    def load_product_info(self):
        """Load product information."""
        session = SessionLocal()
        try:
            product = session.query(Product).filter(Product.code == self.product_code).first()
            if product:
                self.product = product
                self.product_info.setText(f"{product.code} - {product.name}")
                self.unit_price_edit.setValue(product.unit_price)
        finally:
            session.close()
    
    def save_movement(self):
        """Save stock movement."""
        if self.movement_type == 'OUT' and self.quantity_edit.value() > self.product.quantity:
            QMessageBox.warning(self, _('Warning'), _('Insufficient stock quantity.'))
            return
        
        session = SessionLocal()
        try:
            # Create inventory transaction
            transaction = InventoryTransaction()
            transaction.product_id = self.product.id
            transaction.transaction_type = self.movement_type
            transaction.quantity = self.quantity_edit.value()
            transaction.unit_price = self.unit_price_edit.value()
            transaction.transaction_date = self.date_edit.date().toPython()
            transaction.notes = self.notes_edit.toPlainText().strip() or None
            
            session.add(transaction)
            
            # Update product quantity
            product = session.query(Product).filter(Product.id == self.product.id).first()
            if self.movement_type == 'IN':
                product.quantity += self.quantity_edit.value()
            else:  # OUT
                product.quantity -= self.quantity_edit.value()
            
            session.commit()
            self.accept()
            
        except Exception as e:
            session.rollback()
            QMessageBox.critical(self, _('Error'), f"Failed to save movement: {str(e)}")
        finally:
            session.close()
