@echo off
echo 🚀 تثبيت PyQt6...
echo.

echo 🔍 اختبار Python...
python --version
if errorlevel 1 (
    echo ❌ Python غير متاح!
    pause
    exit /b 1
)

echo.
echo 🔧 تحديث pip...
python -m pip install --upgrade pip

echo.
echo 📦 تثبيت PyQt6 - الطريقة 1...
python -m pip install --user PyQt6
if errorlevel 1 (
    echo ⚠️ الطريقة 1 فشلت، جرب الطريقة 2...
    
    echo 📦 تثبيت PyQt6 - الطريقة 2...
    pip install --user PyQt6
    if errorlevel 1 (
        echo ⚠️ الطريقة 2 فشلت، جرب الطريقة 3...
        
        echo 📦 تثبيت PyQt6 - الطريقة 3...
        python -m pip install PyQt6
        if errorlevel 1 (
            echo ❌ جميع الطرق فشلت!
            echo 💡 جرب تشغيل هذا الملف كمدير
            pause
            exit /b 1
        )
    )
)

echo.
echo 🔍 اختبار PyQt6...
python -c "from PyQt6.QtWidgets import QApplication; print('✅ PyQt6 يعمل!')"
if errorlevel 1 (
    echo ❌ PyQt6 لا يعمل!
    pause
    exit /b 1
)

echo.
echo 🎉 تم تثبيت PyQt6 بنجاح!
echo 💡 يمكنك الآن تشغيل النظام:
echo    python final_launch.py
echo.
pause
