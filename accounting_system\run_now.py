#!/usr/bin/env python3
"""
تشغيل فوري للنظام
Immediate System Launch
"""
import sys
import os

# Add the src directory to the Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(current_dir, 'src')
sys.path.insert(0, src_dir)

print("🚀 بدء تشغيل نظام المحاسبة...")
print(f"📁 مجلد العمل: {current_dir}")

try:
    # Import PyQt6
    from PyQt6.QtWidgets import (
        QApplication, QMainWindow, QVBoxLayout, QWidget, 
        QLabel, QTabWidget, QPushButton, QMessageBox
    )
    from PyQt6.QtCore import Qt
    from PyQt6.QtGui import QFont
    print("✅ تم تحميل PyQt6 بنجاح!")
    
    # Create application
    app = QApplication(sys.argv)
    app.setApplicationName("Accounting System")
    
    # Create main window
    class MainWindow(QMainWindow):
        def __init__(self):
            super().__init__()
            self.setWindowTitle("🏢 نظام المحاسبة المتكامل - Accounting System")
            self.setGeometry(150, 150, 1200, 800)
            
            # Set style
            self.setStyleSheet("""
                QMainWindow {
                    background-color: #f5f5f5;
                }
                QTabWidget::pane {
                    border: 1px solid #c0c0c0;
                    background-color: white;
                }
                QTabBar::tab {
                    background-color: #e0e0e0;
                    padding: 8px 16px;
                    margin-right: 2px;
                }
                QTabBar::tab:selected {
                    background-color: white;
                    border-bottom: 2px solid #007acc;
                }
            """)
            
            # Create central widget
            central_widget = QWidget()
            self.setCentralWidget(central_widget)
            layout = QVBoxLayout()
            central_widget.setLayout(layout)
            
            # Add title
            title = QLabel("🎉 نظام المحاسبة المتكامل يعمل بنجاح!")
            title.setFont(QFont("Arial", 18, QFont.Weight.Bold))
            title.setAlignment(Qt.AlignmentFlag.AlignCenter)
            title.setStyleSheet("""
                color: #2c3e50; 
                margin: 20px; 
                padding: 20px; 
                background-color: #ecf0f1; 
                border-radius: 10px;
                border: 2px solid #3498db;
            """)
            layout.addWidget(title)
            
            # Create tab widget
            self.tab_widget = QTabWidget()
            layout.addWidget(self.tab_widget)
            
            # Load forms
            self.load_forms()
            
            # Status bar
            self.statusBar().showMessage("✅ النظام جاهز للاستخدام - System Ready")
        
        def load_forms(self):
            """تحميل النماذج"""
            forms_info = [
                ("product_form", "ProductForm", "🛍️ المنتجات", "Products"),
                ("invoice_form", "InvoiceForm", "🧾 الفواتير", "Invoices"),
                ("employee_form", "EmployeeForm", "👥 الموظفين", "Employees"),
                ("purchase_form", "PurchaseForm", "📦 المشتريات", "Purchases"),
                ("expense_form", "ExpenseForm", "💰 المصروفات", "Expenses"),
                ("user_management_form", "UserManagementForm", "👤 المستخدمين", "Users"),
                ("salary_form", "SalaryForm", "💵 الرواتب", "Salaries"),
            ]
            
            successful_count = 0
            
            for module_name, class_name, arabic_name, english_name in forms_info:
                try:
                    # Import the form
                    module = __import__(f'src.ui.forms.{module_name}', fromlist=[class_name])
                    form_class = getattr(module, class_name)
                    
                    # Create form instance
                    form_instance = form_class()
                    
                    # Add to tab widget
                    tab_name = f"{arabic_name} - {english_name}"
                    self.tab_widget.addTab(form_instance, tab_name)
                    
                    print(f"✅ تم تحميل: {tab_name}")
                    successful_count += 1
                    
                except Exception as e:
                    # Create error widget
                    error_widget = QWidget()
                    error_layout = QVBoxLayout()
                    error_widget.setLayout(error_layout)
                    
                    error_label = QLabel(f"❌ خطأ في تحميل النموذج:\n{str(e)}")
                    error_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
                    error_label.setStyleSheet("color: red; font-size: 14px; padding: 20px;")
                    error_layout.addWidget(error_label)
                    
                    retry_btn = QPushButton("🔄 إعادة المحاولة")
                    error_layout.addWidget(retry_btn)
                    
                    tab_name = f"❌ {arabic_name}"
                    self.tab_widget.addTab(error_widget, tab_name)
                    print(f"❌ فشل تحميل: {arabic_name} - {str(e)}")
            
            # Show summary
            total_forms = len(forms_info)
            if successful_count > 0:
                QMessageBox.information(
                    self, 
                    "نجح التحميل", 
                    f"تم تحميل {successful_count} من {total_forms} نموذج بنجاح!\n\n"
                    f"يمكنك الآن استخدام النماذج المتاحة."
                )
            else:
                QMessageBox.warning(
                    self, 
                    "فشل التحميل", 
                    "لم يتم تحميل أي نموذج بنجاح.\n"
                    "تحقق من المتطلبات والملفات."
                )
    
    # Create and show main window
    window = MainWindow()
    window.show()
    
    print("✅ تم فتح النافذة بنجاح!")
    print("💡 النظام يعمل الآن. يمكنك استخدام النماذج من التبويبات.")
    print("🔑 بيانات الدخول التجريبية: admin / admin123")
    
    # Run application
    sys.exit(app.exec())
    
except ImportError as e:
    print(f"❌ خطأ في الاستيراد: {e}")
    print("💡 تأكد من تثبيت PyQt6:")
    print("   pip install PyQt6")
    
except Exception as e:
    print(f"❌ خطأ عام: {e}")
    import traceback
    traceback.print_exc()

print("👋 انتهى التشغيل.")
