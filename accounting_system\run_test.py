#!/usr/bin/env python3
"""
ملف تشغيل سريع للنظام
Quick Run File for the System
"""
import sys
import os
import subprocess

def run_setup():
    """تشغيل إعداد البيانات"""
    print("🔧 إعداد قاعدة البيانات والبيانات التجريبية...")
    try:
        result = subprocess.run([sys.executable, "setup_test_data.py"], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ تم إعداد البيانات بنجاح!")
            print(result.stdout)
            return True
        else:
            print("❌ فشل في إعداد البيانات:")
            print(result.stderr)
            return False
    except Exception as e:
        print(f"❌ خطأ في تشغيل الإعداد: {e}")
        return False

def run_test():
    """تشغيل اختبار النظام"""
    print("🚀 تشغيل نظام الاختبار...")
    try:
        subprocess.run([sys.executable, "test_all_forms.py"])
    except Exception as e:
        print(f"❌ خطأ في تشغيل النظام: {e}")

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("🧪 مرحباً بك في نظام اختبار المحاسبة المتكامل")
    print("=" * 60)
    
    # التحقق من وجود الملفات المطلوبة
    required_files = ["setup_test_data.py", "test_all_forms.py"]
    for file in required_files:
        if not os.path.exists(file):
            print(f"❌ الملف المطلوب غير موجود: {file}")
            return
    
    print("\n📋 الخيارات المتاحة:")
    print("1. إعداد البيانات التجريبية فقط")
    print("2. تشغيل النظام مباشرة")
    print("3. إعداد البيانات ثم تشغيل النظام")
    print("4. خروج")
    
    while True:
        try:
            choice = input("\n🎯 اختر رقم الخيار (1-4): ").strip()
            
            if choice == "1":
                run_setup()
                break
            elif choice == "2":
                run_test()
                break
            elif choice == "3":
                if run_setup():
                    input("\n⏸️ اضغط Enter للمتابعة إلى تشغيل النظام...")
                    run_test()
                break
            elif choice == "4":
                print("👋 وداعاً!")
                break
            else:
                print("❌ خيار غير صحيح، يرجى اختيار رقم من 1 إلى 4")
        except KeyboardInterrupt:
            print("\n\n👋 تم إلغاء العملية. وداعاً!")
            break
        except Exception as e:
            print(f"❌ خطأ: {e}")

if __name__ == "__main__":
    main()
