from typing import Union, overload, ByteString, Optional

def _HChaCha20(key: ByteString, nonce: ByteString) -> bytearray: ...

class ChaCha20Cipher:
    block_size: int
    nonce: bytes

    def __init__(self, key: ByteString, nonce: ByteString) -> None: ...
    @overload
    def encrypt(self, plaintext: ByteString) -> bytes: ...
    @overload
    def encrypt(self, plaintext: ByteString, output: Union[bytearray, memoryview]) -> None: ...
    @overload
    def decrypt(self, plaintext: ByteString) -> bytes: ...
    @overload
    def decrypt(self, plaintext: ByteString, output: Union[bytearray, memoryview]) -> None: ...
    def seek(self, position: int) -> None: ...

def new(key: ByteString, nonce: Optional[ByteString] = ...) -> ChaCha20Cipher: ...

block_size: int
key_size: int
