"""
Automatic backup scheduling functionality.
"""
import os
import time
import threading
import schedule
from datetime import datetime, timedelta
from src.utils.backup import create_backup
from src.utils.translation import _

class BackupScheduler:
    """Handles automatic backup scheduling."""
    
    def __init__(self):
        self.scheduler_thread = None
        self.is_running = False
    
    def start(self):
        """Start the backup scheduler."""
        if not self.is_running:
            self.is_running = True
            self.scheduler_thread = threading.Thread(target=self._run_scheduler, daemon=True)
            self.scheduler_thread.start()
    
    def stop(self):
        """Stop the backup scheduler."""
        self.is_running = False
        if self.scheduler_thread:
            self.scheduler_thread.join()
    
    def _run_scheduler(self):
        """Run the scheduler loop."""
        # Schedule daily backup at 23:00
        schedule.every().day.at("23:00").do(self._scheduled_backup)
        
        # Keep running until stopped
        while self.is_running:
            schedule.run_pending()
            time.sleep(60)
    
    def _scheduled_backup(self):
        """Perform scheduled backup."""
        try:
            backup_dir = os.path.join('backups', 'automatic')
            backup_file = create_backup(backup_dir)
            
            # Keep only last 7 days of automatic backups
            self._cleanup_old_backups(backup_dir)
            
            print(f"Automatic backup created: {backup_file}")
            return True
        except Exception as e:
            print(f"Automatic backup failed: {e}")
            return False
    
    def _cleanup_old_backups(self, backup_dir):
        """Remove backups older than 7 days."""
        if not os.path.exists(backup_dir):
            return
            
        # Get current time
        now = datetime.now()
        
        # Check each file in the backup directory
        for filename in os.listdir(backup_dir):
            filepath = os.path.join(backup_dir, filename)
            
            # Skip if not a file
            if not os.path.isfile(filepath):
                continue
                
            # Get file creation time
            file_time = datetime.fromtimestamp(os.path.getctime(filepath))
            
            # Remove if older than 7 days
            if now - file_time > timedelta(days=7):
                try:
                    os.remove(filepath)
                    print(f"Removed old backup: {filepath}")
                except Exception as e:
                    print(f"Failed to remove old backup {filepath}: {e}")

# Create global scheduler instance
backup_scheduler = BackupScheduler()
