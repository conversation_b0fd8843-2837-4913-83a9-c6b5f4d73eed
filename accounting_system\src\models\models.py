"""
Base models for the accounting system.
"""
from sqlalchemy import <PERSON>umn, Integer, String, Boolean, DateTime, ForeignKey, Float, Text, Table
from sqlalchemy.orm import relationship
from datetime import datetime
from src.database.database import Base

# Association tables for many-to-many relationships
user_roles = Table(
    'user_roles',
    Base.metadata,
    Column('user_id', Integer, ForeignKey('users.id')),
    Column('role_id', Integer, ForeignKey('roles.id'))
)

class User(Base):
    """User model for authentication and authorization."""
    __tablename__ = 'users'

    id = Column(Integer, primary_key=True)
    username = Column(String(50), unique=True, nullable=False)
    email = Column(String(120), unique=True, nullable=False)
    password_hash = Column(String(128), nullable=False)
    full_name = Column(String(100), nullable=False)
    is_active = Column(Boolean, default=True)
    language = Column(String(2), default='ar')  # ar or en
    created_at = Column(DateTime, default=datetime.utcnow)
    last_login = Column(DateTime)

    # Relationships
    roles = relationship('Role', secondary=user_roles, backref='users')

    def __repr__(self):
        return f'<User {self.username}>'

class Role(Base):
    """Role model for user permissions."""
    __tablename__ = 'roles'

    id = Column(Integer, primary_key=True)
    name = Column(String(50), unique=True, nullable=False)
    description = Column(String(200))
    permissions = Column(Text)  # JSON string of permissions

    def __repr__(self):
        return f'<Role {self.name}>'

class Customer(Base):
    """Customer model for managing client information."""
    __tablename__ = 'customers'

    id = Column(Integer, primary_key=True)
    code = Column(String(20), unique=True, nullable=False)
    name = Column(String(100), nullable=False)
    contact_person = Column(String(100))
    email = Column(String(120))
    phone = Column(String(20))
    address = Column(Text)
    tax_number = Column(String(50))
    credit_limit = Column(Float, default=0.0)
    balance = Column(Float, default=0.0)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    notes = Column(Text)

    # Relationships
    invoices = relationship('Invoice', backref='customer')
    payments = relationship('Payment', backref='customer')

    def __repr__(self):
        return f'<Customer {self.name}>'

class Supplier(Base):
    """Supplier model for managing vendor information."""
    __tablename__ = 'suppliers'

    id = Column(Integer, primary_key=True)
    code = Column(String(20), unique=True, nullable=False)
    name = Column(String(100), nullable=False)
    contact_person = Column(String(100))
    email = Column(String(120))
    phone = Column(String(20))
    address = Column(Text)
    tax_number = Column(String(50))
    balance = Column(Float, default=0.0)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    payment_terms = Column(String(100))
    notes = Column(Text)

    # Relationships
    purchase_orders = relationship('PurchaseOrder', backref='supplier')
    payments = relationship('SupplierPayment', backref='supplier')

    def __repr__(self):
        return f'<Supplier {self.name}>'
