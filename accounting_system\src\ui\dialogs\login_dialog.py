#!/usr/bin/env python3
"""
نافذة تسجيل الدخول
Login Dialog
"""
import sys
import os

# Add parent directories to path
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(os.path.dirname(os.path.dirname(current_dir)))
sys.path.insert(0, parent_dir)

from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, 
    QPushButton, QFrame, QCheckBox, QMessageBox, QApplication
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont, QPixmap, QPainter, QColor, QIcon

try:
    from src.database.database import SessionLocal
    from src.models.auth import User
    import bcrypt
except ImportError as e:
    print(f"Warning: Could not import database modules: {e}")

class LoginDialog(QDialog):
    """نافذة تسجيل الدخول"""
    
    login_successful = pyqtSignal(dict)  # إشارة نجاح تسجيل الدخول
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.user_data = None
        self.setup_ui()
        self.apply_styles()
        
    def setup_ui(self):
        """إعداد واجهة تسجيل الدخول"""
        self.setWindowTitle("🔐 تسجيل الدخول - نظام المحاسبة")
        self.setFixedSize(400, 500)
        self.setModal(True)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        self.setLayout(main_layout)
        
        # إطار العنوان
        title_frame = QFrame()
        title_layout = QVBoxLayout()
        title_frame.setLayout(title_layout)
        
        # شعار النظام
        logo_label = QLabel("🏢")
        logo_label.setFont(QFont("Arial", 48))
        logo_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_layout.addWidget(logo_label)
        
        # عنوان النظام
        title_label = QLabel("نظام المحاسبة المتكامل")
        title_label.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_layout.addWidget(title_label)
        
        subtitle_label = QLabel("Comprehensive Accounting System")
        subtitle_label.setFont(QFont("Arial", 10))
        subtitle_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_layout.addWidget(subtitle_label)
        
        main_layout.addWidget(title_frame)
        
        # إطار تسجيل الدخول
        login_frame = QFrame()
        login_frame.setFrameStyle(QFrame.Shape.Box)
        login_layout = QVBoxLayout()
        login_frame.setLayout(login_layout)
        
        # عنوان تسجيل الدخول
        login_title = QLabel("🔐 تسجيل الدخول")
        login_title.setFont(QFont("Arial", 14, QFont.Weight.Bold))
        login_title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        login_layout.addWidget(login_title)
        
        # حقل اسم المستخدم
        username_label = QLabel("👤 اسم المستخدم:")
        username_label.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        login_layout.addWidget(username_label)
        
        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText("أدخل اسم المستخدم")
        self.username_input.setText("admin")  # قيمة افتراضية للاختبار
        login_layout.addWidget(self.username_input)
        
        # حقل كلمة المرور
        password_label = QLabel("🔒 كلمة المرور:")
        password_label.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        login_layout.addWidget(password_label)
        
        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText("أدخل كلمة المرور")
        self.password_input.setEchoMode(QLineEdit.EchoMode.Password)
        self.password_input.setText("admin123")  # قيمة افتراضية للاختبار
        login_layout.addWidget(self.password_input)
        
        # خيار تذكر كلمة المرور
        self.remember_checkbox = QCheckBox("تذكر كلمة المرور")
        login_layout.addWidget(self.remember_checkbox)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        
        self.login_button = QPushButton("🚀 دخول")
        self.login_button.setDefault(True)
        self.login_button.clicked.connect(self.attempt_login)
        buttons_layout.addWidget(self.login_button)
        
        self.cancel_button = QPushButton("❌ إلغاء")
        self.cancel_button.clicked.connect(self.reject)
        buttons_layout.addWidget(self.cancel_button)
        
        login_layout.addLayout(buttons_layout)
        
        main_layout.addWidget(login_frame)
        
        # معلومات إضافية
        info_label = QLabel("💡 بيانات تجريبية: admin / admin123")
        info_label.setFont(QFont("Arial", 9))
        info_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        main_layout.addWidget(info_label)
        
        # ربط Enter بتسجيل الدخول
        self.username_input.returnPressed.connect(self.attempt_login)
        self.password_input.returnPressed.connect(self.attempt_login)
        
    def apply_styles(self):
        """تطبيق الأنماط"""
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1, 
                    stop:0 #f8f9fa, stop:1 #e9ecef);
            }
            
            QFrame {
                background-color: white;
                border-radius: 10px;
                padding: 20px;
                margin: 10px;
                border: 1px solid #dee2e6;
            }
            
            QLabel {
                color: #2c3e50;
                padding: 5px;
            }
            
            QLineEdit {
                padding: 10px;
                border: 2px solid #bdc3c7;
                border-radius: 6px;
                font-size: 12px;
                background-color: white;
            }
            
            QLineEdit:focus {
                border-color: #3498db;
            }
            
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 12px;
                margin: 5px;
            }
            
            QPushButton:hover {
                background-color: #2980b9;
            }
            
            QPushButton:pressed {
                background-color: #21618c;
            }
            
            QPushButton:default {
                background-color: #27ae60;
            }
            
            QPushButton:default:hover {
                background-color: #229954;
            }
            
            QCheckBox {
                color: #2c3e50;
                font-size: 11px;
                padding: 5px;
            }
            
            QCheckBox::indicator {
                width: 16px;
                height: 16px;
                border-radius: 3px;
                border: 2px solid #bdc3c7;
                background-color: white;
            }
            
            QCheckBox::indicator:checked {
                background-color: #3498db;
                border-color: #3498db;
            }
        """)
    
    def attempt_login(self):
        """محاولة تسجيل الدخول"""
        username = self.username_input.text().strip()
        password = self.password_input.text()
        
        if not username or not password:
            QMessageBox.warning(self, "خطأ", "يرجى إدخال اسم المستخدم وكلمة المرور")
            return
        
        # تعطيل الأزرار أثناء المعالجة
        self.login_button.setEnabled(False)
        self.login_button.setText("⏳ جاري التحقق...")
        
        try:
            # محاولة التحقق من قاعدة البيانات
            if self.verify_credentials(username, password):
                self.user_data = {
                    'username': username,
                    'login_time': self.get_current_time(),
                    'remember': self.remember_checkbox.isChecked()
                }
                
                QMessageBox.information(self, "نجح تسجيل الدخول", 
                                      f"مرحباً {username}!\nتم تسجيل الدخول بنجاح")
                
                self.login_successful.emit(self.user_data)
                self.accept()
            else:
                QMessageBox.critical(self, "فشل تسجيل الدخول", 
                                   "اسم المستخدم أو كلمة المرور غير صحيحة")
        
        except Exception as e:
            # في حالة عدم توفر قاعدة البيانات، استخدم بيانات افتراضية
            if username == "admin" and password == "admin123":
                self.user_data = {
                    'username': username,
                    'login_time': self.get_current_time(),
                    'remember': self.remember_checkbox.isChecked()
                }
                
                QMessageBox.information(self, "نجح تسجيل الدخول", 
                                      f"مرحباً {username}!\nتم تسجيل الدخول بنجاح (وضع تجريبي)")
                
                self.login_successful.emit(self.user_data)
                self.accept()
            else:
                QMessageBox.critical(self, "فشل تسجيل الدخول", 
                                   "اسم المستخدم أو كلمة المرور غير صحيحة")
        
        finally:
            # إعادة تفعيل الأزرار
            self.login_button.setEnabled(True)
            self.login_button.setText("🚀 دخول")
    
    def verify_credentials(self, username, password):
        """التحقق من بيانات الاعتماد"""
        try:
            session = SessionLocal()
            user = session.query(User).filter_by(username=username, is_active=True).first()
            
            if user and bcrypt.checkpw(password.encode('utf-8'), user.password_hash.encode('utf-8')):
                session.close()
                return True
            
            session.close()
            return False
            
        except Exception as e:
            print(f"Database error: {e}")
            # في حالة عدم توفر قاعدة البيانات، استخدم التحقق الافتراضي
            return username == "admin" and password == "admin123"
    
    def get_current_time(self):
        """الحصول على الوقت الحالي"""
        from datetime import datetime
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    def get_user_data(self):
        """الحصول على بيانات المستخدم"""
        return self.user_data

# اختبار النافذة
if __name__ == "__main__":
    app = QApplication(sys.argv)
    
    dialog = LoginDialog()
    
    def on_login_success(user_data):
        print(f"تم تسجيل الدخول: {user_data}")
    
    dialog.login_successful.connect(on_login_success)
    
    if dialog.exec() == QDialog.DialogCode.Accepted:
        print("تم قبول تسجيل الدخول")
    else:
        print("تم إلغاء تسجيل الدخول")
    
    sys.exit()
