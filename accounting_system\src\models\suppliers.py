"""
Supplier related models.
"""
from sqlalchemy import Column, Integer, String, Float, DateTime, Text, Boolean
from sqlalchemy.orm import relationship
from datetime import datetime
from src.database.database import Base

class Supplier(Base):
    """Supplier model."""
    __tablename__ = 'suppliers'

    id = Column(Integer, primary_key=True)
    code = Column(String(20), unique=True, nullable=False)
    name = Column(String(100), nullable=False)
    contact_person = Column(String(100))
    phone = Column(String(20))
    mobile = Column(String(20))
    email = Column(String(120))
    address = Column(Text)
    tax_number = Column(String(50))
    credit_limit = Column(Float, default=0.0)
    current_balance = Column(Float, default=0.0)
    is_active = Column(Boolean, default=True)
    notes = Column(Text)
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # Relationships
    purchase_orders = relationship('PurchaseOrder', back_populates='supplier')
    supplier_payments = relationship('SupplierPayment', back_populates='supplier')

    def __repr__(self):
        return f'<Supplier {self.name}>'
