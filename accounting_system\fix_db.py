#!/usr/bin/env python3
"""
إصلاح قاعدة البيانات
Fix Database Script
"""
import sys
import os
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def fix_database():
    """Fix database by recreating it properly."""
    print("🔧 إصلاح قاعدة البيانات / Fixing database...")
    
    # Remove existing database
    db_path = project_root / "data" / "accounting.db"
    if db_path.exists():
        os.remove(db_path)
        print("✅ تم حذف قاعدة البيانات القديمة / Old database removed")
    
    try:
        # Import database configuration
        from src.database.database import Base, engine, SessionLocal
        
        # Import ALL models explicitly to register them with Base
        print("📦 تحميل النماذج / Loading models...")
        
        # Accounting models
        from src.models.accounting import (
            Account, AccountType, JournalEntry, JournalEntryLine,
            ExpenseCategory, Expense, FiscalYear, FinancialStatement
        )
        
        # Customer and supplier models
        from src.models.customers import Customer
        from src.models.suppliers import Supplier
        
        # Transaction models
        from src.models.transactions import (
            Invoice, InvoiceItem, Payment, PurchaseOrder, 
            PurchaseOrderItem, SupplierPayment
        )
        
        # Inventory models
        from src.models.inventory import (
            Product, ProductCategory, StockMovement, 
            Warehouse, StockLocation, InventoryTransaction
        )
        
        # HR models
        from src.models.hr import (
            Department, Employee, SalaryPayment, Leave,
            EmployeeDocument, Allowance, Deduction
        )
        
        # Auth models
        from src.models.auth import User, Role, Permission
        
        print("✅ تم تحميل جميع النماذج / All models loaded")
        
        # Create all tables
        print("🔨 إنشاء الجداول / Creating tables...")
        Base.metadata.create_all(bind=engine)
        print("✅ تم إنشاء جميع الجداول / All tables created")
        
        # Create default admin user
        print("👤 إنشاء المستخدم الافتراضي / Creating default user...")
        session = SessionLocal()
        try:
            # Create admin role
            admin_role = Role(
                name="admin",
                description="Administrator role"
            )
            session.add(admin_role)
            session.flush()
            
            # Create admin user
            admin_user = User(
                username="admin",
                password="dummy",  # Will be set properly
                full_name="System Administrator",
                email="<EMAIL>",
                role_id=admin_role.id,
                is_active=True,
                is_admin=True
            )
            admin_user.set_password("admin123")
            session.add(admin_user)
            session.commit()
            
            print("✅ تم إنشاء المستخدم الافتراضي / Default user created")
            print("👤 اسم المستخدم / Username: admin")
            print("🔑 كلمة المرور / Password: admin123")
            
        except Exception as e:
            session.rollback()
            print(f"⚠️ تحذير في إنشاء المستخدم / User creation warning: {e}")
        finally:
            session.close()
        
        # Test the database
        print("🧪 اختبار قاعدة البيانات / Testing database...")
        session = SessionLocal()
        try:
            # Test basic queries
            users_count = session.query(User).count()
            products_count = session.query(Product).count()
            customers_count = session.query(Customer).count()
            
            print(f"📊 إحصائيات / Statistics:")
            print(f"  - المستخدمون / Users: {users_count}")
            print(f"  - المنتجات / Products: {products_count}")
            print(f"  - العملاء / Customers: {customers_count}")
            
        except Exception as e:
            print(f"⚠️ تحذير في الاختبار / Test warning: {e}")
        finally:
            session.close()
        
        print("🎉 تم إصلاح قاعدة البيانات بنجاح / Database fixed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح قاعدة البيانات / Database fix error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    print("=" * 60)
    print("🔧 إصلاح قاعدة البيانات / Database Fix")
    print("=" * 60)
    
    if fix_database():
        print("\n✅ يمكنك الآن تشغيل النظام / You can now run the system:")
        print("python start.py")
    else:
        print("\n❌ فشل في الإصلاح / Fix failed")
        sys.exit(1)
