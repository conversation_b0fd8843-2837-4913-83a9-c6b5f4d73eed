from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget,
                               QTableWidgetItem, QPushButton, QComboBox, QLabel,
                               QLineEdit, QMessageBox, QCheckBox)
from PyQt6.QtCore import Qt
from src.models.auth import User, Role, Permission, PermissionType, Module
from src.database.database import SessionLocal
from src.utils.translation import _

class UserManagementForm(QWidget):
    def __init__(self):
        super().__init__()
        self.db = SessionLocal()
        self.init_ui()

    def init_ui(self):
        layout = QVBoxLayout()

        # User List
        self.user_table = QTableWidget()
        self.user_table.setColumnCount(4)
        self.user_table.setHorizontalHeaderLabels([_('Username'), _('Role'), _('Active'), _('Actions')])
        layout.addWidget(self.user_table)

        # User Form
        form_layout = QHBoxLayout()

        # Left side - User details
        user_form = QVBoxLayout()

        username_layout = QHBoxLayout()
        username_layout.addWidget(QLabel(_('Username:')))
        self.username_input = QLineEdit()
        username_layout.addWidget(self.username_input)
        user_form.addLayout(username_layout)

        password_layout = QHBoxLayout()
        password_layout.addWidget(QLabel(_('Password:')))
        self.password_input = QLineEdit()
        self.password_input.setEchoMode(QLineEdit.EchoMode.Password)
        password_layout.addWidget(self.password_input)
        user_form.addLayout(password_layout)

        role_layout = QHBoxLayout()
        role_layout.addWidget(QLabel(_('Role:')))
        self.role_combo = QComboBox()
        role_layout.addWidget(self.role_combo)
        user_form.addLayout(role_layout)

        self.active_checkbox = QCheckBox(_('Active'))
        self.active_checkbox.setChecked(True)
        user_form.addWidget(self.active_checkbox)

        form_layout.addLayout(user_form)

        # Right side - Permissions
        perm_layout = QVBoxLayout()
        perm_layout.addWidget(QLabel(_('Permissions:')))

        self.perm_table = QTableWidget()
        self.perm_table.setColumnCount(len(PermissionType))
        headers = [perm.name for perm in PermissionType]
        self.perm_table.setHorizontalHeaderLabels(headers)
        self.perm_table.setRowCount(len(Module))
        for i, module in enumerate(Module):
            self.perm_table.setVerticalHeaderItem(i, QTableWidgetItem(_(module.name)))
            for j, _ in enumerate(PermissionType):
                checkbox = QCheckBox()
                self.perm_table.setCellWidget(i, j, checkbox)

        perm_layout.addWidget(self.perm_table)
        form_layout.addLayout(perm_layout)

        layout.addLayout(form_layout)

        # Buttons
        button_layout = QHBoxLayout()
        self.save_btn = QPushButton(_('Save'))
        self.save_btn.clicked.connect(self.save_user)
        self.clear_btn = QPushButton(_('Clear'))
        self.clear_btn.clicked.connect(self.clear_form)
        button_layout.addWidget(self.save_btn)
        button_layout.addWidget(self.clear_btn)
        layout.addLayout(button_layout)

        self.setLayout(layout)
        self.load_data()

    def load_data(self):
        # Load roles
        roles = self.db.query(Role).all()
        self.role_combo.clear()
        for role in roles:
            self.role_combo.addItem(role.name, role.id)

        # Load users
        users = self.db.query(User).all()
        self.user_table.setRowCount(len(users))
        for i, user in enumerate(users):
            self.user_table.setItem(i, 0, QTableWidgetItem(user.username))
            self.user_table.setItem(i, 1, QTableWidgetItem(user.role.name))
            active_check = QCheckBox()
            active_check.setChecked(user.is_active)
            active_check.setEnabled(False)
            self.user_table.setCellWidget(i, 2, active_check)

            edit_btn = QPushButton(_('Edit'))
            edit_btn.clicked.connect(lambda checked, u=user: self.edit_user(u))
            self.user_table.setCellWidget(i, 3, edit_btn)

    def save_user(self):
        username = self.username_input.text()
        password = self.password_input.text()
        role_id = self.role_combo.currentData()
        is_active = self.active_checkbox.isChecked()

        if not username or not password:
            QMessageBox.warning(self, _('Error'), _('Username and password are required'))
            return

        # Create or update user
        user = self.db.query(User).filter_by(username=username).first()
        if not user:
            user = User(username=username)

        user.password = password  # In production, hash the password
        user.role_id = role_id
        user.is_active = is_active

        # Update permissions
        user.permissions.clear()
        for i, module in enumerate(Module):
            for j, perm_type in enumerate(PermissionType):
                checkbox = self.perm_table.cellWidget(i, j)
                if checkbox.isChecked():
                    perm = self.db.query(Permission).filter_by(
                        module=module.name,
                        permission_type=perm_type.name
                    ).first()
                    if not perm:
                        perm = Permission(
                            module=module.name,
                            permission_type=perm_type.name
                        )
                        self.db.add(perm)
                    user.permissions.append(perm)

        try:
            if not user.id:
                self.db.add(user)
            self.db.commit()
            self.load_data()
            self.clear_form()
            QMessageBox.information(self, _('Success'), _('User saved successfully'))
        except Exception as e:
            self.db.rollback()
            QMessageBox.critical(self, _('Error'), str(e))

    def edit_user(self, user):
        self.username_input.setText(user.username)
        self.password_input.clear()  # Don't show existing password
        index = self.role_combo.findData(user.role_id)
        self.role_combo.setCurrentIndex(index)
        self.active_checkbox.setChecked(user.is_active)

        # Set permissions
        for i, module in enumerate(Module):
            for j, perm_type in enumerate(PermissionType):
                checkbox = self.perm_table.cellWidget(i, j)
                has_perm = user.has_permission(module, perm_type)
                checkbox.setChecked(has_perm)

    def clear_form(self):
        self.username_input.clear()
        self.password_input.clear()
        self.role_combo.setCurrentIndex(0)
        self.active_checkbox.setChecked(True)

        # Clear permissions
        for i in range(self.perm_table.rowCount()):
            for j in range(self.perm_table.columnCount()):
                checkbox = self.perm_table.cellWidget(i, j)
                checkbox.setChecked(False)

    def closeEvent(self, event):
        self.db.close()
        super().closeEvent(event)
