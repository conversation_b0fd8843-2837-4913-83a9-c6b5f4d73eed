"""
نموذج إدارة المستخدمين المحسن
Enhanced User Management Form
"""
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
    QLabel, QLineEdit, QTextEdit, QFormLayout,
    QTableWidget, QTableWidgetItem, QMessageBox,
    QDateEdit, QDoubleSpinBox, QComboBox, QSpinBox,
    QHeaderView, QAbstractItemView, QDialog, QDialogButtonBox,
    QGroupBox, QGridLayout, QTabWidget, QCheckBox
)
from PyQt6.QtCore import Qt, QDate
from PyQt6.QtGui import QFont
from src.database.database import SessionLocal
from src.models.auth import User, Role, Permission, PermissionType, Module
from src.utils.translation import _
from datetime import datetime
import bcrypt

class UserManagementForm(QWidget):
    """Enhanced User Management form class."""

    def __init__(self):
        super().__init__()
        self.current_user = None
        self._init_ui()
        self._load_data()

    def _init_ui(self):
        """Initialize the enhanced UI."""
        layout = QVBoxLayout()
        self.setLayout(layout)

        # Title
        title = QLabel(_('User Management'))
        title.setFont(QFont('Arial', 16, QFont.Weight.Bold))
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title)

        # Search and filter section
        search_group = QGroupBox(_('Search & Filter'))
        search_layout = QGridLayout()
        search_group.setLayout(search_layout)

        # Search box
        search_layout.addWidget(QLabel(_('Search') + ':'), 0, 0)
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText(_('Search by username or email...'))
        self.search_input.textChanged.connect(self._filter_users)
        search_layout.addWidget(self.search_input, 0, 1)

        # Role filter
        search_layout.addWidget(QLabel(_('Role') + ':'), 0, 2)
        self.role_filter = QComboBox()
        self.role_filter.currentTextChanged.connect(self._filter_users)
        search_layout.addWidget(self.role_filter, 0, 3)

        # Status filter
        search_layout.addWidget(QLabel(_('Status') + ':'), 0, 4)
        self.status_filter = QComboBox()
        self.status_filter.addItems([_('All'), _('Active'), _('Inactive')])
        self.status_filter.currentTextChanged.connect(self._filter_users)
        search_layout.addWidget(self.status_filter, 0, 5)

        layout.addWidget(search_group)

        # Buttons section
        buttons_layout = QHBoxLayout()

        self.new_btn = QPushButton(_('New User'))
        self.new_btn.clicked.connect(self._new_user)
        buttons_layout.addWidget(self.new_btn)

        self.edit_btn = QPushButton(_('Edit User'))
        self.edit_btn.clicked.connect(self._edit_user)
        self.edit_btn.setEnabled(False)
        buttons_layout.addWidget(self.edit_btn)

        self.delete_btn = QPushButton(_('Delete User'))
        self.delete_btn.clicked.connect(self._delete_user)
        self.delete_btn.setEnabled(False)
        buttons_layout.addWidget(self.delete_btn)

        self.permissions_btn = QPushButton(_('Manage Permissions'))
        self.permissions_btn.clicked.connect(self._manage_permissions)
        self.permissions_btn.setEnabled(False)
        buttons_layout.addWidget(self.permissions_btn)

        self.refresh_btn = QPushButton(_('Refresh'))
        self.refresh_btn.clicked.connect(self._load_data)
        buttons_layout.addWidget(self.refresh_btn)

        buttons_layout.addStretch()
        layout.addLayout(buttons_layout)

        # Users table
        self.table = QTableWidget()
        self.table.setColumnCount(6)
        self.table.setHorizontalHeaderLabels([
            _('Username'), _('Email'), _('Role'), _('Last Login'),
            _('Status'), _('Created Date')
        ])

        # Configure table
        header = self.table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        self.table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.table.setAlternatingRowColors(True)
        self.table.itemSelectionChanged.connect(self._on_selection_changed)
        self.table.itemDoubleClicked.connect(self._edit_user)

        layout.addWidget(self.table)

    def _on_selection_changed(self):
        """Handle selection change."""
        has_selection = len(self.table.selectedItems()) > 0
        self.edit_btn.setEnabled(has_selection)
        self.delete_btn.setEnabled(has_selection)
        self.permissions_btn.setEnabled(has_selection)

    def _load_data(self):
        """Load users data."""
        self._load_roles_filter()
        self._load_users()

    def _load_roles_filter(self):
        """Load roles for filter."""
        session = SessionLocal()
        try:
            self.role_filter.clear()
            self.role_filter.addItem(_('All Roles'))

            roles = session.query(Role).all()
            for role in roles:
                self.role_filter.addItem(role.name)
        finally:
            session.close()

    def _load_users(self):
        """Load users into table."""
        session = SessionLocal()
        try:
            users = session.query(User).all()
            self.table.setRowCount(len(users))

            for i, user in enumerate(users):
                # Username
                self.table.setItem(i, 0, QTableWidgetItem(user.username))

                # Email
                self.table.setItem(i, 1, QTableWidgetItem(user.email or ''))

                # Role
                role_name = user.role.name if user.role else _('No Role')
                self.table.setItem(i, 2, QTableWidgetItem(role_name))

                # Last Login
                last_login = user.last_login.strftime('%Y-%m-%d %H:%M') if user.last_login else _('Never')
                self.table.setItem(i, 3, QTableWidgetItem(last_login))

                # Status
                status_text = _('Active') if user.is_active else _('Inactive')
                self.table.setItem(i, 4, QTableWidgetItem(status_text))

                # Created Date
                created_date = user.created_at.strftime('%Y-%m-%d') if user.created_at else ''
                self.table.setItem(i, 5, QTableWidgetItem(created_date))

                # Store user ID in first column
                self.table.item(i, 0).setData(Qt.ItemDataRole.UserRole, user.id)

        finally:
            session.close()

    def _filter_users(self):
        """Filter users based on search criteria."""
        search_text = self.search_input.text().lower()
        role_filter = self.role_filter.currentText()
        status_filter = self.status_filter.currentText()

        for row in range(self.table.rowCount()):
            show_row = True

            # Search filter
            if search_text:
                username = self.table.item(row, 0).text().lower()
                email = self.table.item(row, 1).text().lower()
                if search_text not in username and search_text not in email:
                    show_row = False

            # Role filter
            if role_filter != _('All Roles'):
                role = self.table.item(row, 2).text()
                if role != role_filter:
                    show_row = False

            # Status filter
            if status_filter != _('All'):
                status = self.table.item(row, 4).text()
                if status_filter == _('Active') and status != _('Active'):
                    show_row = False
                elif status_filter == _('Inactive') and status != _('Inactive'):
                    show_row = False

            self.table.setRowHidden(row, not show_row)

    def _new_user(self):
        """Create new user."""
        dialog = UserDialog(parent=self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            if dialog.validate_data():
                self._save_user_data(dialog.get_user_data())

    def _edit_user(self):
        """Edit selected user."""
        current_row = self.table.currentRow()
        if current_row < 0:
            return

        user_id = self.table.item(current_row, 0).data(Qt.ItemDataRole.UserRole)

        session = SessionLocal()
        try:
            user = session.query(User).get(user_id)
            if user:
                dialog = UserDialog(user, parent=self)
                if dialog.exec() == QDialog.DialogCode.Accepted:
                    if dialog.validate_data():
                        self._update_user_data(user, dialog.get_user_data())
        finally:
            session.close()

    def _delete_user(self):
        """Delete selected user."""
        current_row = self.table.currentRow()
        if current_row < 0:
            return

        username = self.table.item(current_row, 0).text()

        reply = QMessageBox.question(
            self, _('Confirm Delete'),
            _('Are you sure you want to delete user: ') + username + '?',
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            user_id = self.table.item(current_row, 0).data(Qt.ItemDataRole.UserRole)

            session = SessionLocal()
            try:
                user = session.query(User).get(user_id)
                if user:
                    session.delete(user)
                    session.commit()
                    QMessageBox.information(self, _('Success'), _('User deleted successfully'))
                    self._load_data()
            except Exception as e:
                session.rollback()
                QMessageBox.critical(self, _('Error'), _('Failed to delete user: ') + str(e))
            finally:
                session.close()

    def _manage_permissions(self):
        """Manage user permissions."""
        current_row = self.table.currentRow()
        if current_row < 0:
            return

        user_id = self.table.item(current_row, 0).data(Qt.ItemDataRole.UserRole)

        session = SessionLocal()
        try:
            user = session.query(User).get(user_id)
            if user:
                dialog = PermissionsDialog(user, parent=self)
                if dialog.exec() == QDialog.DialogCode.Accepted:
                    self._update_user_permissions(user, dialog.get_permissions())
        finally:
            session.close()

    def _save_user_data(self, data):
        """Save new user."""
        session = SessionLocal()
        try:
            # Check if username already exists
            existing = session.query(User).filter_by(username=data['username']).first()
            if existing:
                QMessageBox.warning(self, _('Error'), _('Username already exists'))
                return

            # Hash password
            hashed_password = bcrypt.hashpw(data['password'].encode('utf-8'), bcrypt.gensalt())

            user = User(
                username=data['username'],
                email=data['email'],
                password_hash=hashed_password.decode('utf-8'),
                role_id=data['role_id'],
                is_active=data['is_active'],
                created_at=datetime.now()
            )

            session.add(user)
            session.commit()
            QMessageBox.information(self, _('Success'), _('User created successfully'))
            self._load_data()

        except Exception as e:
            session.rollback()
            QMessageBox.critical(self, _('Error'), _('Failed to create user: ') + str(e))
        finally:
            session.close()

    def _update_user_data(self, user, data):
        """Update existing user."""
        session = SessionLocal()
        try:
            # Check if username already exists (excluding current user)
            existing = session.query(User).filter(
                User.username == data['username'],
                User.id != user.id
            ).first()
            if existing:
                QMessageBox.warning(self, _('Error'), _('Username already exists'))
                return

            # Update user fields
            user.username = data['username']
            user.email = data['email']
            user.role_id = data['role_id']
            user.is_active = data['is_active']

            # Update password if provided
            if data['password']:
                hashed_password = bcrypt.hashpw(data['password'].encode('utf-8'), bcrypt.gensalt())
                user.password_hash = hashed_password.decode('utf-8')

            session.commit()
            QMessageBox.information(self, _('Success'), _('User updated successfully'))
            self._load_data()

        except Exception as e:
            session.rollback()
            QMessageBox.critical(self, _('Error'), _('Failed to update user: ') + str(e))
        finally:
            session.close()

    def _update_user_permissions(self, user, permissions):
        """Update user permissions."""
        session = SessionLocal()
        try:
            # Clear existing permissions
            user.permissions.clear()

            # Add new permissions
            for perm_data in permissions:
                permission = session.query(Permission).filter_by(
                    module=perm_data['module'],
                    permission_type=perm_data['permission_type']
                ).first()

                if not permission:
                    permission = Permission(
                        module=perm_data['module'],
                        permission_type=perm_data['permission_type']
                    )
                    session.add(permission)

                user.permissions.append(permission)

            session.commit()
            QMessageBox.information(self, _('Success'), _('Permissions updated successfully'))

        except Exception as e:
            session.rollback()
            QMessageBox.critical(self, _('Error'), _('Failed to update permissions: ') + str(e))
        finally:
            session.close()


class UserDialog(QDialog):
    """Dialog for adding/editing users."""

    def __init__(self, user=None, parent=None):
        super().__init__(parent)
        self.user = user
        self.setWindowTitle(_('New User') if user is None else _('Edit User'))
        self.setModal(True)
        self.setMinimumSize(400, 300)

        self.setup_ui()
        if user:
            self.load_user_data()

    def setup_ui(self):
        """Setup the dialog UI."""
        layout = QVBoxLayout()

        # Form layout
        form_layout = QFormLayout()

        # Username
        self.username_edit = QLineEdit()
        self.username_edit.setMaxLength(50)
        form_layout.addRow(_('Username') + '*:', self.username_edit)

        # Email
        self.email_edit = QLineEdit()
        self.email_edit.setMaxLength(100)
        form_layout.addRow(_('Email') + ':', self.email_edit)

        # Password
        self.password_edit = QLineEdit()
        self.password_edit.setEchoMode(QLineEdit.EchoMode.Password)
        self.password_edit.setMaxLength(100)
        password_label = _('Password') + ('*:' if not self.user else ':')
        form_layout.addRow(password_label, self.password_edit)

        # Confirm Password
        self.confirm_password_edit = QLineEdit()
        self.confirm_password_edit.setEchoMode(QLineEdit.EchoMode.Password)
        self.confirm_password_edit.setMaxLength(100)
        confirm_label = _('Confirm Password') + ('*:' if not self.user else ':')
        form_layout.addRow(confirm_label, self.confirm_password_edit)

        # Role
        self.role_combo = QComboBox()
        self.load_roles()
        form_layout.addRow(_('Role') + '*:', self.role_combo)

        # Is active
        self.is_active_check = QCheckBox()
        self.is_active_check.setChecked(True)
        form_layout.addRow(_('Active') + ':', self.is_active_check)

        layout.addLayout(form_layout)

        # Buttons
        button_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok |
                                     QDialogButtonBox.StandardButton.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)

        self.setLayout(layout)

    def load_roles(self):
        """Load roles."""
        session = SessionLocal()
        try:
            roles = session.query(Role).all()
            self.role_combo.addItem(_('Select Role'), None)
            for role in roles:
                self.role_combo.addItem(role.name, role.id)
        finally:
            session.close()

    def load_user_data(self):
        """Load user data for editing."""
        if not self.user:
            return

        self.username_edit.setText(self.user.username)
        self.email_edit.setText(self.user.email or '')
        self.is_active_check.setChecked(self.user.is_active)

        # Set role
        if self.user.role_id:
            for i in range(self.role_combo.count()):
                if self.role_combo.itemData(i) == self.user.role_id:
                    self.role_combo.setCurrentIndex(i)
                    break

    def get_user_data(self):
        """Get user data from form."""
        return {
            'username': self.username_edit.text().strip(),
            'email': self.email_edit.text().strip(),
            'password': self.password_edit.text(),
            'role_id': self.role_combo.currentData(),
            'is_active': self.is_active_check.isChecked()
        }

    def validate_data(self):
        """Validate form data."""
        data = self.get_user_data()

        if not data['username']:
            QMessageBox.warning(self, _('Validation Error'), _('Username is required'))
            return False

        if not data['role_id']:
            QMessageBox.warning(self, _('Validation Error'), _('Please select a role'))
            return False

        # Password validation for new users
        if not self.user:
            if not data['password']:
                QMessageBox.warning(self, _('Validation Error'), _('Password is required'))
                return False

            if data['password'] != self.confirm_password_edit.text():
                QMessageBox.warning(self, _('Validation Error'), _('Passwords do not match'))
                return False
        else:
            # For existing users, only validate if password is provided
            if data['password'] and data['password'] != self.confirm_password_edit.text():
                QMessageBox.warning(self, _('Validation Error'), _('Passwords do not match'))
                return False

        return True


class PermissionsDialog(QDialog):
    """Dialog for managing user permissions."""

    def __init__(self, user, parent=None):
        super().__init__(parent)
        self.user = user
        self.setWindowTitle(_('Manage Permissions') + f' - {user.username}')
        self.setModal(True)
        self.setMinimumSize(600, 400)

        self.setup_ui()
        self.load_permissions()

    def setup_ui(self):
        """Setup the dialog UI."""
        layout = QVBoxLayout()

        # Info label
        info_label = QLabel(_('Select permissions for user: ') + self.user.username)
        layout.addWidget(info_label)

        # Permissions table
        self.permissions_table = QTableWidget()

        # Get modules and permission types
        modules = [module for module in Module]
        perm_types = [perm_type for perm_type in PermissionType]

        self.permissions_table.setRowCount(len(modules))
        self.permissions_table.setColumnCount(len(perm_types))

        # Set headers
        self.permissions_table.setVerticalHeaderLabels([_(module.value) for module in modules])
        self.permissions_table.setHorizontalHeaderLabels([_(perm_type.value) for perm_type in perm_types])

        # Add checkboxes
        for i, module in enumerate(modules):
            for j, perm_type in enumerate(perm_types):
                checkbox = QCheckBox()
                self.permissions_table.setCellWidget(i, j, checkbox)

        # Configure table
        header = self.permissions_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeMode.Stretch)

        layout.addWidget(self.permissions_table)

        # Buttons
        button_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok |
                                     QDialogButtonBox.StandardButton.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)

        self.setLayout(layout)

    def load_permissions(self):
        """Load current user permissions."""
        session = SessionLocal()
        try:
            modules = [module for module in Module]
            perm_types = [perm_type for perm_type in PermissionType]

            for i, module in enumerate(modules):
                for j, perm_type in enumerate(perm_types):
                    checkbox = self.permissions_table.cellWidget(i, j)

                    # Check if user has this permission
                    has_permission = any(
                        perm.module == module.value and perm.permission_type == perm_type.value
                        for perm in self.user.permissions
                    )

                    checkbox.setChecked(has_permission)
        finally:
            session.close()

    def get_permissions(self):
        """Get selected permissions."""
        permissions = []
        modules = [module for module in Module]
        perm_types = [perm_type for perm_type in PermissionType]

        for i, module in enumerate(modules):
            for j, perm_type in enumerate(perm_types):
                checkbox = self.permissions_table.cellWidget(i, j)
                if checkbox.isChecked():
                    permissions.append({
                        'module': module.value,
                        'permission_type': perm_type.value
                    })

        return permissions
