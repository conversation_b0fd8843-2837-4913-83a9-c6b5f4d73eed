#!/usr/bin/env python3
"""
ملف اختبار شامل لجميع النماذج
Comprehensive Test File for All Forms
"""
import sys
import os
from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QVBoxLayout, QHBoxLayout,
    QPushButton, QWidget, QLabel, QTabWidget, QMessageBox
)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QFont, QIcon

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Import all forms
try:
    from src.ui.forms.product_form import ProductForm
    from src.ui.forms.invoice_form import InvoiceForm
    from src.ui.forms.employee_form import EmployeeForm
    from src.ui.forms.purchase_form import PurchaseForm
    from src.ui.forms.expense_form import ExpenseForm
    from src.ui.forms.user_management_form import UserManagementForm
    from src.ui.forms.salary_form import SalaryForm
    from src.utils.translation import _
    print("✅ تم تحميل جميع النماذج بنجاح!")
except ImportError as e:
    print(f"❌ خطأ في تحميل النماذج: {e}")
    sys.exit(1)

class TestMainWindow(QMainWindow):
    """نافذة اختبار رئيسية لجميع النماذج"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("🧪 اختبار نظام المحاسبة - Accounting System Test")
        self.setGeometry(100, 100, 1200, 800)
        
        # Set application style
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f5f5f5;
            }
            QTabWidget::pane {
                border: 1px solid #c0c0c0;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #e0e0e0;
                padding: 8px 16px;
                margin-right: 2px;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
            }
            QTabBar::tab:selected {
                background-color: white;
                border-bottom: 2px solid #007acc;
            }
            QPushButton {
                background-color: #007acc;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #005a9e;
            }
            QPushButton:pressed {
                background-color: #004080;
            }
        """)
        
        self.init_ui()
    
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout()
        central_widget.setLayout(layout)
        
        # Title
        title = QLabel("🧪 اختبار نظام المحاسبة المتكامل")
        title.setFont(QFont("Arial", 18, QFont.Weight.Bold))
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title.setStyleSheet("color: #2c3e50; margin: 10px; padding: 10px;")
        layout.addWidget(title)
        
        # Subtitle
        subtitle = QLabel("جميع النماذج جاهزة للاختبار - All Forms Ready for Testing")
        subtitle.setFont(QFont("Arial", 12))
        subtitle.setAlignment(Qt.AlignmentFlag.AlignCenter)
        subtitle.setStyleSheet("color: #7f8c8d; margin-bottom: 20px;")
        layout.addWidget(subtitle)
        
        # Create tab widget for all forms
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)
        
        # Initialize all forms
        self.init_forms()
        
        # Status bar
        self.statusBar().showMessage("✅ النظام جاهز للاستخدام - System Ready")
    
    def init_forms(self):
        """تهيئة جميع النماذج"""
        forms_info = [
            ("🛍️ المنتجات", "Products", ProductForm),
            ("🧾 الفواتير", "Invoices", InvoiceForm),
            ("👥 الموظفين", "Employees", EmployeeForm),
            ("📦 المشتريات", "Purchases", PurchaseForm),
            ("💰 المصروفات", "Expenses", ExpenseForm),
            ("👤 المستخدمين", "Users", UserManagementForm),
            ("💵 الرواتب", "Salaries", SalaryForm),
        ]
        
        for arabic_name, english_name, form_class in forms_info:
            try:
                form_instance = form_class()
                tab_name = f"{arabic_name} - {english_name}"
                self.tab_widget.addTab(form_instance, tab_name)
                print(f"✅ تم تحميل نموذج: {tab_name}")
            except Exception as e:
                error_widget = QWidget()
                error_layout = QVBoxLayout()
                error_widget.setLayout(error_layout)
                
                error_label = QLabel(f"❌ خطأ في تحميل النموذج:\n{str(e)}")
                error_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
                error_label.setStyleSheet("color: red; font-size: 14px; padding: 20px;")
                error_layout.addWidget(error_label)
                
                retry_btn = QPushButton("🔄 إعادة المحاولة")
                retry_btn.clicked.connect(lambda: self.retry_form(form_class))
                error_layout.addWidget(retry_btn)
                
                tab_name = f"❌ {arabic_name}"
                self.tab_widget.addTab(error_widget, tab_name)
                print(f"❌ فشل تحميل نموذج: {arabic_name} - {str(e)}")
    
    def retry_form(self, form_class):
        """إعادة محاولة تحميل النموذج"""
        try:
            form_instance = form_class()
            current_index = self.tab_widget.currentIndex()
            self.tab_widget.removeTab(current_index)
            self.tab_widget.insertTab(current_index, form_instance, "✅ تم الإصلاح")
            QMessageBox.information(self, "نجح", "تم تحميل النموذج بنجاح!")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل النموذج:\n{str(e)}")

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء تشغيل نظام اختبار المحاسبة...")
    
    # Create application
    app = QApplication(sys.argv)
    app.setApplicationName("Accounting System Test")
    app.setApplicationVersion("1.0")
    
    # Set application style
    app.setStyle('Fusion')
    
    try:
        # Create and show main window
        window = TestMainWindow()
        window.show()
        
        print("✅ تم تشغيل النظام بنجاح!")
        print("📋 النماذج المتاحة:")
        print("   1. 🛍️ نموذج المنتجات - Product Management")
        print("   2. 🧾 نموذج الفواتير - Invoice Management")
        print("   3. 👥 نموذج الموظفين - Employee Management")
        print("   4. 📦 نموذج المشتريات - Purchase Management")
        print("   5. 💰 نموذج المصروفات - Expense Management")
        print("   6. 👤 نموذج المستخدمين - User Management")
        print("   7. 💵 نموذج الرواتب - Salary Management")
        print("\n🎯 يمكنك الآن اختبار جميع النماذج من خلال التبويبات!")
        
        # Run application
        sys.exit(app.exec())
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل النظام: {e}")
        QMessageBox.critical(None, "خطأ", f"فشل في تشغيل النظام:\n{str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
