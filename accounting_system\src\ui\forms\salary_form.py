"""
Employee salary management form.
"""
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QPushButton, 
    QLabel, QLineEdit, QTextEdit, QFormLayout,
    QTableWidget, QTableWidgetItem, QMessageBox,
    QSpinBox, QDoubleSpinBox, QComboBox, QDateEdit
)
from PyQt6.QtCore import Qt, QDate
from src.database.database import SessionLocal
from src.models.hr import Employee, SalaryPayment
from datetime import datetime

class SalaryForm(QWidget):
    """Salary form class."""
    
    def __init__(self):
        super().__init__()
        self._init_ui()
        self._load_data()
    
    def _init_ui(self):
        """Initialize the UI."""
        layout = QHBoxLayout()
        self.setLayout(layout)
        
        # Left side - Salary list
        list_widget = QWidget()
        list_layout = QVBoxLayout()
        list_widget.setLayout(list_layout)
        
        # Add search box
        search_layout = QHBoxLayout()
        search_label = QLabel('بحث:')
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText('ابحث باسم الموظف أو الرقم...')
        self.search_input.textChanged.connect(self._filter_salaries)
        search_layout.addWidget(search_label)
        search_layout.addWidget(self.search_input)
        list_layout.addLayout(search_layout)
        
        # Add salary table
        self.table = QTableWidget()
        self.table.setColumnCount(5)
        self.table.setHorizontalHeaderLabels(['الرقم', 'الموظف', 'الشهر', 'السنة', 'الصافي'])
        self.table.horizontalHeader().setStretchLastSection(True)
        self.table.itemClicked.connect(self._load_salary)
        list_layout.addWidget(self.table)
        
        # Right side - Salary details
        details_widget = QWidget()
        details_layout = QFormLayout()
        details_widget.setLayout(details_layout)
        
        # Add salary fields
        self.employee_input = QComboBox()
        self._load_employees()
        details_layout.addRow('الموظف:', self.employee_input)
        
        self.payment_date_input = QDateEdit()
        self.payment_date_input.setCalendarPopup(True)
        self.payment_date_input.setDate(QDate.currentDate())
        details_layout.addRow('تاريخ الدفع:', self.payment_date_input)
        
        self.month_input = QSpinBox()
        self.month_input.setMinimum(1)
        self.month_input.setMaximum(12)
        self.month_input.setValue(datetime.now().month)
        details_layout.addRow('الشهر:', self.month_input)
        
        self.year_input = QSpinBox()
        self.year_input.setMinimum(2000)
        self.year_input.setMaximum(2100)
        self.year_input.setValue(datetime.now().year)
        details_layout.addRow('السنة:', self.year_input)
        
        self.basic_salary_input = QDoubleSpinBox()
        self.basic_salary_input.setMaximum(1000000)
        details_layout.addRow('الراتب الأساسي:', self.basic_salary_input)
        
        self.allowances_input = QDoubleSpinBox()
        self.allowances_input.setMaximum(1000000)
        details_layout.addRow('البدلات:', self.allowances_input)
        
        self.deductions_input = QDoubleSpinBox()
        self.deductions_input.setMaximum(1000000)
        details_layout.addRow('الخصومات:', self.deductions_input)
        
        self.tax_amount_input = QDoubleSpinBox()
        self.tax_amount_input.setMaximum(1000000)
        details_layout.addRow('مبلغ الضريبة:', self.tax_amount_input)
        
        self.social_security_input = QDoubleSpinBox()
        self.social_security_input.setMaximum(1000000)
        details_layout.addRow('التأمينات:', self.social_security_input)
        
        self.net_salary_input = QLineEdit()
        self.net_salary_input.setReadOnly(True)
        details_layout.addRow('صافي الراتب:', self.net_salary_input)
        
        self.payment_method_input = QComboBox()
        self.payment_method_input.addItems(['تحويل بنكي', 'نقدي', 'شيك'])
        details_layout.addRow('طريقة الدفع:', self.payment_method_input)
        
        self.reference_input = QLineEdit()
        details_layout.addRow('رقم المرجع:', self.reference_input)
        
        self.notes_input = QTextEdit()
        self.notes_input.setMaximumHeight(100)
        details_layout.addRow('ملاحظات:', self.notes_input)
        
        # Connect salary components to calculate net salary
        for input_field in [self.basic_salary_input, self.allowances_input, 
                          self.deductions_input, self.tax_amount_input, 
                          self.social_security_input]:
            input_field.valueChanged.connect(self._calculate_net_salary)
        
        # Add buttons
        buttons_layout = QHBoxLayout()
        
        self.new_btn = QPushButton('جديد')
        self.new_btn.clicked.connect(self._clear_form)
        buttons_layout.addWidget(self.new_btn)
        
        self.save_btn = QPushButton('حفظ')
        self.save_btn.clicked.connect(self._save_salary)
        buttons_layout.addWidget(self.save_btn)
        
        self.print_btn = QPushButton('طباعة')
        self.print_btn.clicked.connect(self._print_salary)
        buttons_layout.addWidget(self.print_btn)
        
        details_layout.addRow('', buttons_layout)
        
        # Add widgets to main layout
        layout.addWidget(list_widget, stretch=1)
        layout.addWidget(details_widget, stretch=1)
        
        # Initialize member variables
        self.current_salary = None
    
    def _load_employees(self):
        """Load employees into combo box."""
        try:
            db = SessionLocal()
            employees = db.query(Employee).filter(Employee.is_active == True).all()
            self.employee_input.clear()
            self.employee_input.addItem('-- اختر الموظف --', None)
            for employee in employees:
                self.employee_input.addItem(
                    f'{employee.first_name} {employee.last_name}', 
                    employee.id
                )
        except Exception as e:
            QMessageBox.critical(self, 'خطأ', f'حدث خطأ أثناء تحميل الموظفين: {str(e)}')
        finally:
            db.close()
    
    def _load_data(self):
        """Load salaries data into table."""
        try:
            db = SessionLocal()
            salaries = db.query(SalaryPayment).join(Employee).all()
            self.table.setRowCount(len(salaries))
            
            for i, salary in enumerate(salaries):
                self.table.setItem(i, 0, QTableWidgetItem(str(salary.id)))
                self.table.setItem(
                    i, 1, 
                    QTableWidgetItem(f'{salary.employee.first_name} {salary.employee.last_name}')
                )
                self.table.setItem(i, 2, QTableWidgetItem(str(salary.month)))
                self.table.setItem(i, 3, QTableWidgetItem(str(salary.year)))
                self.table.setItem(i, 4, QTableWidgetItem(str(salary.net_salary)))
        
        except Exception as e:
            QMessageBox.critical(self, 'خطأ', f'حدث خطأ أثناء تحميل البيانات: {str(e)}')
        finally:
            db.close()
    
    def _filter_salaries(self):
        """Filter salaries based on search text."""
        search_text = self.search_input.text().lower()
        for row in range(self.table.rowCount()):
            match = False
            for col in range(self.table.columnCount()):
                item = self.table.item(row, col)
                if item and search_text in item.text().lower():
                    match = True
                    break
            self.table.setRowHidden(row, not match)
    
    def _load_salary(self, item):
        """Load salary details when selected from table."""
        try:
            db = SessionLocal()
            salary_id = int(self.table.item(item.row(), 0).text())
            salary = db.query(SalaryPayment).get(salary_id)
            
            if salary:
                self.current_salary = salary
                
                # Set employee
                employee_index = self.employee_input.findData(salary.employee_id)
                if employee_index >= 0:
                    self.employee_input.setCurrentIndex(employee_index)
                
                self.payment_date_input.setDate(QDate(salary.payment_date))
                self.month_input.setValue(salary.month)
                self.year_input.setValue(salary.year)
                self.basic_salary_input.setValue(salary.basic_salary)
                self.allowances_input.setValue(salary.allowances)
                self.deductions_input.setValue(salary.deductions)
                self.tax_amount_input.setValue(salary.tax_amount)
                self.social_security_input.setValue(salary.social_security)
                
                # Set payment method
                method_index = self.payment_method_input.findText(salary.payment_method)
                if method_index >= 0:
                    self.payment_method_input.setCurrentIndex(method_index)
                
                self.reference_input.setText(salary.reference or '')
                self.notes_input.setText(salary.notes or '')
                
                self._calculate_net_salary()
        
        except Exception as e:
            QMessageBox.critical(self, 'خطأ', f'حدث خطأ أثناء تحميل بيانات الراتب: {str(e)}')
        finally:
            db.close()
    
    def _calculate_net_salary(self):
        """Calculate net salary based on components."""
        basic = self.basic_salary_input.value()
        allowances = self.allowances_input.value()
        deductions = self.deductions_input.value()
        tax = self.tax_amount_input.value()
        social = self.social_security_input.value()
        
        net = basic + allowances - deductions - tax - social
        self.net_salary_input.setText(str(net))
    
    def _clear_form(self):
        """Clear form fields."""
        self.current_salary = None
        self.employee_input.setCurrentIndex(0)
        self.payment_date_input.setDate(QDate.currentDate())
        self.month_input.setValue(datetime.now().month)
        self.year_input.setValue(datetime.now().year)
        self.basic_salary_input.setValue(0)
        self.allowances_input.setValue(0)
        self.deductions_input.setValue(0)
        self.tax_amount_input.setValue(0)
        self.social_security_input.setValue(0)
        self.payment_method_input.setCurrentIndex(0)
        self.reference_input.clear()
        self.notes_input.clear()
        self._calculate_net_salary()
    
    def _save_salary(self):
        """Save salary data."""
        try:
            if not self.employee_input.currentData():
                QMessageBox.warning(self, 'تنبيه', 'الرجاء اختيار الموظف')
                return
            
            db = SessionLocal()
            
            if self.current_salary:
                salary = self.current_salary
            else:
                # Check if salary exists for this month/year
                existing = db.query(SalaryPayment).filter(
                    SalaryPayment.employee_id == self.employee_input.currentData(),
                    SalaryPayment.month == self.month_input.value(),
                    SalaryPayment.year == self.year_input.value()
                ).first()
                
                if existing:
                    QMessageBox.warning(
                        self, 'تنبيه', 'تم صرف راتب هذا الشهر مسبقاً'
                    )
                    return
                salary = SalaryPayment()
            
            salary.employee_id = self.employee_input.currentData()
            salary.payment_date = self.payment_date_input.date().toPyDate()
            salary.month = self.month_input.value()
            salary.year = self.year_input.value()
            salary.basic_salary = self.basic_salary_input.value()
            salary.allowances = self.allowances_input.value()
            salary.deductions = self.deductions_input.value()
            salary.tax_amount = self.tax_amount_input.value()
            salary.social_security = self.social_security_input.value()
            salary.net_salary = float(self.net_salary_input.text())
            salary.payment_method = self.payment_method_input.currentText()
            salary.reference = self.reference_input.text()
            salary.notes = self.notes_input.toPlainText()
            
            if not self.current_salary:
                db.add(salary)
            
            db.commit()
            
            self._load_data()
            self._clear_form()
            
            QMessageBox.information(
                self, 'نجاح', 'تم حفظ بيانات الراتب بنجاح'
            )
        
        except Exception as e:
            db.rollback()
            QMessageBox.critical(
                self, 'خطأ', f'حدث خطأ أثناء حفظ بيانات الراتب: {str(e)}'
            )
        finally:
            db.close()
    
    def _print_salary(self):
        """Print salary slip."""
        QMessageBox.information(self, 'طباعة', 'سيتم تنفيذ طباعة مسير الراتب قريباً')
