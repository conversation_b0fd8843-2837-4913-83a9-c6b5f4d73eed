"""
نموذج إدارة الرواتب المحسن
Enhanced Salary Management Form
"""
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
    QLabel, QLineEdit, QTextEdit, QFormLayout,
    QTableWidget, QTableWidgetItem, QMessageBox,
    QSpinBox, QDoubleSpinBox, QComboBox, QDateEdit,
    QHeaderView, QAbstractItemView, QDialog, QDialogButtonBox,
    QGroupBox, QGridLayout, QTabWidget, QCheckBox
)
from PyQt6.QtCore import Qt, QDate
from PyQt6.QtGui import QFont
from src.database.database import SessionLocal
from src.models.hr import Employee, SalaryPayment
from src.utils.translation import _
from datetime import datetime

class SalaryForm(QWidget):
    """Enhanced Salary form class."""

    def __init__(self):
        super().__init__()
        self.current_salary = None
        self._init_ui()
        self._load_data()

    def _init_ui(self):
        """Initialize the enhanced UI."""
        layout = QVBoxLayout()
        self.setLayout(layout)

        # Title
        title = QLabel(_('Salary Management'))
        title.setFont(QFont('Arial', 16, QFont.Weight.Bold))
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title)

        # Search and filter section
        search_group = QGroupBox(_('Search & Filter'))
        search_layout = QGridLayout()
        search_group.setLayout(search_layout)

        # Search box
        search_layout.addWidget(QLabel(_('Search') + ':'), 0, 0)
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText(_('Search by employee name...'))
        self.search_input.textChanged.connect(self._filter_salaries)
        search_layout.addWidget(self.search_input, 0, 1)

        # Month filter
        search_layout.addWidget(QLabel(_('Month') + ':'), 0, 2)
        self.month_filter = QComboBox()
        self.month_filter.addItem(_('All Months'))
        for i in range(1, 13):
            self.month_filter.addItem(f"{i:02d}")
        self.month_filter.currentTextChanged.connect(self._filter_salaries)
        search_layout.addWidget(self.month_filter, 0, 3)

        # Year filter
        search_layout.addWidget(QLabel(_('Year') + ':'), 0, 4)
        self.year_filter = QComboBox()
        self.year_filter.addItem(_('All Years'))
        current_year = datetime.now().year
        for year in range(current_year - 5, current_year + 2):
            self.year_filter.addItem(str(year))
        self.year_filter.currentTextChanged.connect(self._filter_salaries)
        search_layout.addWidget(self.year_filter, 0, 5)

        layout.addWidget(search_group)

        # Buttons section
        buttons_layout = QHBoxLayout()

        self.new_btn = QPushButton(_('New Salary Payment'))
        self.new_btn.clicked.connect(self._new_salary)
        buttons_layout.addWidget(self.new_btn)

        self.edit_btn = QPushButton(_('Edit Salary Payment'))
        self.edit_btn.clicked.connect(self._edit_salary)
        self.edit_btn.setEnabled(False)
        buttons_layout.addWidget(self.edit_btn)

        self.delete_btn = QPushButton(_('Delete Salary Payment'))
        self.delete_btn.clicked.connect(self._delete_salary)
        self.delete_btn.setEnabled(False)
        buttons_layout.addWidget(self.delete_btn)

        self.print_btn = QPushButton(_('Print Salary Slip'))
        self.print_btn.clicked.connect(self._print_salary)
        self.print_btn.setEnabled(False)
        buttons_layout.addWidget(self.print_btn)

        self.refresh_btn = QPushButton(_('Refresh'))
        self.refresh_btn.clicked.connect(self._load_data)
        buttons_layout.addWidget(self.refresh_btn)

        buttons_layout.addStretch()
        layout.addLayout(buttons_layout)

        # Salary payments table
        self.table = QTableWidget()
        self.table.setColumnCount(8)
        self.table.setHorizontalHeaderLabels([
            _('Employee'), _('Month'), _('Year'), _('Basic Salary'),
            _('Allowances'), _('Deductions'), _('Net Salary'), _('Payment Date')
        ])

        # Configure table
        header = self.table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        self.table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.table.setAlternatingRowColors(True)
        self.table.itemSelectionChanged.connect(self._on_selection_changed)
        self.table.itemDoubleClicked.connect(self._edit_salary)

        layout.addWidget(self.table)

    def _on_selection_changed(self):
        """Handle selection change."""
        has_selection = len(self.table.selectedItems()) > 0
        self.edit_btn.setEnabled(has_selection)
        self.delete_btn.setEnabled(has_selection)
        self.print_btn.setEnabled(has_selection)

    def _load_data(self):
        """Load salary payments data."""
        self._load_salaries()

    def _load_salaries(self):
        """Load salary payments into table."""
        session = SessionLocal()
        try:
            salaries = session.query(SalaryPayment).join(Employee).all()
            self.table.setRowCount(len(salaries))

            for i, salary in enumerate(salaries):
                # Employee
                employee_name = f"{salary.employee.first_name} {salary.employee.last_name}"
                self.table.setItem(i, 0, QTableWidgetItem(employee_name))

                # Month
                self.table.setItem(i, 1, QTableWidgetItem(f"{salary.month:02d}"))

                # Year
                self.table.setItem(i, 2, QTableWidgetItem(str(salary.year)))

                # Basic Salary
                self.table.setItem(i, 3, QTableWidgetItem(f"{salary.basic_salary:.2f}"))

                # Allowances
                self.table.setItem(i, 4, QTableWidgetItem(f"{salary.allowances:.2f}"))

                # Deductions
                total_deductions = salary.deductions + salary.tax_amount + salary.social_security
                self.table.setItem(i, 5, QTableWidgetItem(f"{total_deductions:.2f}"))

                # Net Salary
                self.table.setItem(i, 6, QTableWidgetItem(f"{salary.net_salary:.2f}"))

                # Payment Date
                payment_date = salary.payment_date.strftime('%Y-%m-%d') if salary.payment_date else ''
                self.table.setItem(i, 7, QTableWidgetItem(payment_date))

                # Store salary ID in first column
                self.table.item(i, 0).setData(Qt.ItemDataRole.UserRole, salary.id)

        finally:
            session.close()

    def _filter_salaries(self):
        """Filter salary payments based on search criteria."""
        search_text = self.search_input.text().lower()
        month_filter = self.month_filter.currentText()
        year_filter = self.year_filter.currentText()

        for row in range(self.table.rowCount()):
            show_row = True

            # Search filter
            if search_text:
                employee_name = self.table.item(row, 0).text().lower()
                if search_text not in employee_name:
                    show_row = False

            # Month filter
            if month_filter != _('All Months'):
                month = self.table.item(row, 1).text()
                if month != month_filter:
                    show_row = False

            # Year filter
            if year_filter != _('All Years'):
                year = self.table.item(row, 2).text()
                if year != year_filter:
                    show_row = False

            self.table.setRowHidden(row, not show_row)

    def _new_salary(self):
        """Create new salary payment."""
        dialog = SalaryDialog(parent=self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            if dialog.validate_data():
                self._save_salary_data(dialog.get_salary_data())

    def _edit_salary(self):
        """Edit selected salary payment."""
        current_row = self.table.currentRow()
        if current_row < 0:
            return

        salary_id = self.table.item(current_row, 0).data(Qt.ItemDataRole.UserRole)

        session = SessionLocal()
        try:
            salary = session.query(SalaryPayment).get(salary_id)
            if salary:
                dialog = SalaryDialog(salary, parent=self)
                if dialog.exec() == QDialog.DialogCode.Accepted:
                    if dialog.validate_data():
                        self._update_salary_data(salary, dialog.get_salary_data())
        finally:
            session.close()

    def _delete_salary(self):
        """Delete selected salary payment."""
        current_row = self.table.currentRow()
        if current_row < 0:
            return

        employee_name = self.table.item(current_row, 0).text()
        month = self.table.item(current_row, 1).text()
        year = self.table.item(current_row, 2).text()

        reply = QMessageBox.question(
            self, _('Confirm Delete'),
            _('Are you sure you want to delete salary payment for: ') + f"{employee_name} ({month}/{year})?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            salary_id = self.table.item(current_row, 0).data(Qt.ItemDataRole.UserRole)

            session = SessionLocal()
            try:
                salary = session.query(SalaryPayment).get(salary_id)
                if salary:
                    session.delete(salary)
                    session.commit()
                    QMessageBox.information(self, _('Success'), _('Salary payment deleted successfully'))
                    self._load_data()
            except Exception as e:
                session.rollback()
                QMessageBox.critical(self, _('Error'), _('Failed to delete salary payment: ') + str(e))
            finally:
                session.close()

    def _print_salary(self):
        """Print salary slip."""
        current_row = self.table.currentRow()
        if current_row < 0:
            return

        employee_name = self.table.item(current_row, 0).text()
        month = self.table.item(current_row, 1).text()
        year = self.table.item(current_row, 2).text()

        QMessageBox.information(self, _('Print'), _('Printing salary slip for: ') + f"{employee_name} ({month}/{year})")
        # TODO: Implement actual printing functionality

    def _save_salary_data(self, data):
        """Save new salary payment."""
        session = SessionLocal()
        try:
            # Check if salary payment already exists for this employee/month/year
            existing = session.query(SalaryPayment).filter_by(
                employee_id=data['employee_id'],
                month=data['month'],
                year=data['year']
            ).first()

            if existing:
                QMessageBox.warning(self, _('Error'), _('Salary payment already exists for this month'))
                return

            salary = SalaryPayment(
                employee_id=data['employee_id'],
                payment_date=data['payment_date'],
                month=data['month'],
                year=data['year'],
                basic_salary=data['basic_salary'],
                allowances=data['allowances'],
                deductions=data['deductions'],
                tax_amount=data['tax_amount'],
                social_security=data['social_security'],
                net_salary=data['net_salary'],
                payment_method=data['payment_method'],
                reference=data['reference'],
                notes=data['notes']
            )

            session.add(salary)
            session.commit()
            QMessageBox.information(self, _('Success'), _('Salary payment saved successfully'))
            self._load_data()

        except Exception as e:
            session.rollback()
            QMessageBox.critical(self, _('Error'), _('Failed to save salary payment: ') + str(e))
        finally:
            session.close()

    def _update_salary_data(self, salary, data):
        """Update existing salary payment."""
        session = SessionLocal()
        try:
            # Check if salary payment already exists for this employee/month/year (excluding current)
            existing = session.query(SalaryPayment).filter(
                SalaryPayment.employee_id == data['employee_id'],
                SalaryPayment.month == data['month'],
                SalaryPayment.year == data['year'],
                SalaryPayment.id != salary.id
            ).first()

            if existing:
                QMessageBox.warning(self, _('Error'), _('Salary payment already exists for this month'))
                return

            # Update salary fields
            salary.employee_id = data['employee_id']
            salary.payment_date = data['payment_date']
            salary.month = data['month']
            salary.year = data['year']
            salary.basic_salary = data['basic_salary']
            salary.allowances = data['allowances']
            salary.deductions = data['deductions']
            salary.tax_amount = data['tax_amount']
            salary.social_security = data['social_security']
            salary.net_salary = data['net_salary']
            salary.payment_method = data['payment_method']
            salary.reference = data['reference']
            salary.notes = data['notes']

            session.commit()
            QMessageBox.information(self, _('Success'), _('Salary payment updated successfully'))
            self._load_data()

        except Exception as e:
            session.rollback()
            QMessageBox.critical(self, _('Error'), _('Failed to update salary payment: ') + str(e))
        finally:
            session.close()


class SalaryDialog(QDialog):
    """Dialog for adding/editing salary payments."""

    def __init__(self, salary=None, parent=None):
        super().__init__(parent)
        self.salary = salary
        self.setWindowTitle(_('New Salary Payment') if salary is None else _('Edit Salary Payment'))
        self.setModal(True)
        self.setMinimumSize(500, 600)

        self.setup_ui()
        if salary:
            self.load_salary_data()
        else:
            self.set_defaults()

    def setup_ui(self):
        """Setup the dialog UI."""
        layout = QVBoxLayout()

        # Create tabs
        tab_widget = QTabWidget()

        # Basic Information Tab
        basic_tab = QWidget()
        basic_layout = QFormLayout()
        basic_tab.setLayout(basic_layout)

        # Employee
        self.employee_combo = QComboBox()
        self.load_employees()
        self.employee_combo.currentTextChanged.connect(self.on_employee_changed)
        basic_layout.addRow(_('Employee') + '*:', self.employee_combo)

        # Payment date
        self.payment_date_edit = QDateEdit()
        self.payment_date_edit.setCalendarPopup(True)
        self.payment_date_edit.setDate(QDate.currentDate())
        basic_layout.addRow(_('Payment Date') + '*:', self.payment_date_edit)

        # Month
        self.month_spin = QSpinBox()
        self.month_spin.setMinimum(1)
        self.month_spin.setMaximum(12)
        self.month_spin.setValue(datetime.now().month)
        basic_layout.addRow(_('Month') + '*:', self.month_spin)

        # Year
        self.year_spin = QSpinBox()
        self.year_spin.setMinimum(2000)
        self.year_spin.setMaximum(2100)
        self.year_spin.setValue(datetime.now().year)
        basic_layout.addRow(_('Year') + '*:', self.year_spin)

        tab_widget.addTab(basic_tab, _('Basic Information'))

        # Salary Components Tab
        salary_tab = QWidget()
        salary_layout = QFormLayout()
        salary_tab.setLayout(salary_layout)

        # Basic salary
        self.basic_salary_spin = QDoubleSpinBox()
        self.basic_salary_spin.setMaximum(999999.99)
        self.basic_salary_spin.setDecimals(2)
        self.basic_salary_spin.valueChanged.connect(self.calculate_net_salary)
        salary_layout.addRow(_('Basic Salary') + '*:', self.basic_salary_spin)

        # Allowances
        self.allowances_spin = QDoubleSpinBox()
        self.allowances_spin.setMaximum(999999.99)
        self.allowances_spin.setDecimals(2)
        self.allowances_spin.valueChanged.connect(self.calculate_net_salary)
        salary_layout.addRow(_('Allowances') + ':', self.allowances_spin)

        # Deductions
        self.deductions_spin = QDoubleSpinBox()
        self.deductions_spin.setMaximum(999999.99)
        self.deductions_spin.setDecimals(2)
        self.deductions_spin.valueChanged.connect(self.calculate_net_salary)
        salary_layout.addRow(_('Deductions') + ':', self.deductions_spin)

        # Tax amount
        self.tax_amount_spin = QDoubleSpinBox()
        self.tax_amount_spin.setMaximum(999999.99)
        self.tax_amount_spin.setDecimals(2)
        self.tax_amount_spin.valueChanged.connect(self.calculate_net_salary)
        salary_layout.addRow(_('Tax Amount') + ':', self.tax_amount_spin)

        # Social security
        self.social_security_spin = QDoubleSpinBox()
        self.social_security_spin.setMaximum(999999.99)
        self.social_security_spin.setDecimals(2)
        self.social_security_spin.valueChanged.connect(self.calculate_net_salary)
        salary_layout.addRow(_('Social Security') + ':', self.social_security_spin)

        # Net salary (calculated)
        self.net_salary_edit = QLineEdit()
        self.net_salary_edit.setReadOnly(True)
        salary_layout.addRow(_('Net Salary') + ':', self.net_salary_edit)

        tab_widget.addTab(salary_tab, _('Salary Components'))

        # Payment Information Tab
        payment_tab = QWidget()
        payment_layout = QFormLayout()
        payment_tab.setLayout(payment_layout)

        # Payment method
        self.payment_method_combo = QComboBox()
        self.payment_method_combo.addItems([_('Bank Transfer'), _('Cash'), _('Check')])
        payment_layout.addRow(_('Payment Method') + ':', self.payment_method_combo)

        # Reference
        self.reference_edit = QLineEdit()
        self.reference_edit.setMaxLength(50)
        payment_layout.addRow(_('Reference') + ':', self.reference_edit)

        # Notes
        self.notes_edit = QTextEdit()
        self.notes_edit.setMaximumHeight(100)
        payment_layout.addRow(_('Notes') + ':', self.notes_edit)

        tab_widget.addTab(payment_tab, _('Payment Information'))

        layout.addWidget(tab_widget)

        # Buttons
        button_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok |
                                     QDialogButtonBox.StandardButton.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)

        self.setLayout(layout)

    def load_employees(self):
        """Load employees."""
        session = SessionLocal()
        try:
            employees = session.query(Employee).filter(Employee.is_active == True).all()
            self.employee_combo.addItem(_('Select Employee'), None)
            for employee in employees:
                full_name = f"{employee.first_name} {employee.last_name}"
                self.employee_combo.addItem(full_name, employee.id)
        finally:
            session.close()

    def on_employee_changed(self):
        """Handle employee selection change."""
        employee_id = self.employee_combo.currentData()
        if employee_id:
            session = SessionLocal()
            try:
                employee = session.query(Employee).get(employee_id)
                if employee:
                    self.basic_salary_spin.setValue(employee.basic_salary)
                    self.calculate_net_salary()
            finally:
                session.close()

    def calculate_net_salary(self):
        """Calculate net salary."""
        basic = self.basic_salary_spin.value()
        allowances = self.allowances_spin.value()
        deductions = self.deductions_spin.value()
        tax = self.tax_amount_spin.value()
        social = self.social_security_spin.value()

        net = basic + allowances - deductions - tax - social
        self.net_salary_edit.setText(f"{net:.2f}")

    def set_defaults(self):
        """Set default values for new salary payment."""
        self.calculate_net_salary()

    def load_salary_data(self):
        """Load salary data for editing."""
        if not self.salary:
            return

        self.payment_date_edit.setDate(QDate.fromString(self.salary.payment_date.strftime('%Y-%m-%d'), 'yyyy-MM-dd'))
        self.month_spin.setValue(self.salary.month)
        self.year_spin.setValue(self.salary.year)
        self.basic_salary_spin.setValue(self.salary.basic_salary)
        self.allowances_spin.setValue(self.salary.allowances)
        self.deductions_spin.setValue(self.salary.deductions)
        self.tax_amount_spin.setValue(self.salary.tax_amount)
        self.social_security_spin.setValue(self.salary.social_security)
        self.reference_edit.setText(self.salary.reference or '')
        self.notes_edit.setPlainText(self.salary.notes or '')

        # Set employee
        if self.salary.employee_id:
            for i in range(self.employee_combo.count()):
                if self.employee_combo.itemData(i) == self.salary.employee_id:
                    self.employee_combo.setCurrentIndex(i)
                    break

        # Set payment method
        if self.salary.payment_method:
            method_index = self.payment_method_combo.findText(self.salary.payment_method)
            if method_index >= 0:
                self.payment_method_combo.setCurrentIndex(method_index)

        self.calculate_net_salary()

    def get_salary_data(self):
        """Get salary data from form."""
        return {
            'employee_id': self.employee_combo.currentData(),
            'payment_date': self.payment_date_edit.date().toPython(),
            'month': self.month_spin.value(),
            'year': self.year_spin.value(),
            'basic_salary': self.basic_salary_spin.value(),
            'allowances': self.allowances_spin.value(),
            'deductions': self.deductions_spin.value(),
            'tax_amount': self.tax_amount_spin.value(),
            'social_security': self.social_security_spin.value(),
            'net_salary': float(self.net_salary_edit.text() or 0),
            'payment_method': self.payment_method_combo.currentText(),
            'reference': self.reference_edit.text().strip(),
            'notes': self.notes_edit.toPlainText().strip()
        }

    def validate_data(self):
        """Validate form data."""
        data = self.get_salary_data()

        if not data['employee_id']:
            QMessageBox.warning(self, _('Validation Error'), _('Please select an employee'))
            return False

        if data['basic_salary'] <= 0:
            QMessageBox.warning(self, _('Validation Error'), _('Basic salary must be greater than zero'))
            return False

        return True
