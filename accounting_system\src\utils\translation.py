import os
import gettext
from typing import Optional
from src.config import BASE_DIR

def setup_translation(language: Optional[str] = None) -> gettext.GNUTranslations:
    """
    Set up translation for the application.
    
    Args:
        language (str, optional): Language code ('ar' or 'en'). Defaults to None.
    
    Returns:
        gettext.GNUTranslations: Translation object
    """
    if language not in ['ar', 'en']:
        language = 'ar'  # Default to Arabic
    
    locale_dir = os.path.join(BASE_DIR, 'src', 'locales')
    translation = gettext.translation(
        'messages',
        localedir=locale_dir,
        languages=[language],
        fallback=True
    )
    translation.install()
    return translation

def change_language(language: str) -> None:
    """
    Change the application language.
    
    Args:
        language (str): Language code ('ar' or 'en')
    """
    translation = setup_translation(language)
    return translation

# Create a global _ function for translation
_ = gettext.gettext
