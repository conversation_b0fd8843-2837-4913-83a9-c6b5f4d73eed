"""
Inventory and product related models.
"""
from sqlalchemy import Column, Integer, String, Float, DateTime, ForeignKey, Enum, Text, Boolean
from sqlalchemy.orm import relationship, backref
from datetime import datetime
from src.database.database import Base
from src.models.transactions import InvoiceItem, PurchaseOrderItem
import enum

class UnitType(enum.Enum):
    """Unit type enumeration."""
    PCS = "piece"
    KG = "kilogram"
    LTR = "liter"
    MTR = "meter"
    BOX = "box"
    UNIT = "unit"

class Product(Base):
    """Product model."""
    __tablename__ = 'products'

    id = Column(Integer, primary_key=True)
    code = Column(String(20), unique=True, nullable=False)
    name = Column(String(100), nullable=False)
    description = Column(Text)
    unit = Column(Enum(UnitType), default=UnitType.PCS)
    purchase_price = Column(Float, default=0.0)
    sale_price = Column(Float, default=0.0)
    tax_rate = Column(Float, default=0.0)
    min_stock = Column(Float, default=0.0)
    max_stock = Column(Float, default=0.0)
    current_stock = Column(Float, default=0.0)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    category_id = Column(Integer, ForeignKey('product_categories.id'))
    
    # Relationships
    category = relationship('ProductCategory', backref='products')
    stock_movements = relationship('StockMovement', backref='product')
    invoice_items = relationship('InvoiceItem', back_populates='product')
    purchase_order_items = relationship('PurchaseOrderItem', back_populates='product')
    
    def __repr__(self):
        return f'<Product {self.name}>'

class ProductCategory(Base):
    """Product category model."""
    __tablename__ = 'product_categories'

    id = Column(Integer, primary_key=True)
    name = Column(String(50), nullable=False)
    description = Column(String(200))
    parent_id = Column(Integer, ForeignKey('product_categories.id'))
    
    # Self-referential relationship for hierarchical categories
    children = relationship('ProductCategory', 
                          backref=backref('parent', remote_side=[id]))

    def __repr__(self):
        return f'<ProductCategory {self.name}>'

class StockMovement(Base):
    """Stock movement model for inventory tracking."""
    __tablename__ = 'stock_movements'

    id = Column(Integer, primary_key=True)
    product_id = Column(Integer, ForeignKey('products.id'), nullable=False)
    date = Column(DateTime, nullable=False, default=datetime.utcnow)
    quantity = Column(Float, nullable=False)
    unit_price = Column(Float, default=0.0)
    reference_type = Column(String(50))  # 'purchase', 'sale', 'adjustment'
    reference_id = Column(Integer)  # ID of the related document
    notes = Column(Text)
    created_at = Column(DateTime, default=datetime.utcnow)
    created_by = Column(Integer, ForeignKey('users.id'))

    def __repr__(self):
        return f'<StockMovement {self.id}>'

class Warehouse(Base):
    """Warehouse model."""
    __tablename__ = 'warehouses'

    id = Column(Integer, primary_key=True)
    code = Column(String(20), unique=True, nullable=False)
    name = Column(String(100), nullable=False)
    address = Column(Text)
    manager_id = Column(Integer, ForeignKey('users.id'))
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)

    # Relationships
    stock_locations = relationship('StockLocation', backref='warehouse')

    def __repr__(self):
        return f'<Warehouse {self.name}>'

class StockLocation(Base):
    """Stock location model for warehouse organization."""
    __tablename__ = 'stock_locations'

    id = Column(Integer, primary_key=True)
    warehouse_id = Column(Integer, ForeignKey('warehouses.id'), nullable=False)
    name = Column(String(50), nullable=False)
    description = Column(String(200))
    is_active = Column(Boolean, default=True)

    def __repr__(self):
        return f'<StockLocation {self.name}>'
