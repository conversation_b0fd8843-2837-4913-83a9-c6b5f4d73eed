#!/usr/bin/env python3
"""
تشغيل سريع لنظام المحاسبة
Quick Start for Accounting System
"""

print("🚀 بدء تشغيل نظام المحاسبة...")
print("🚀 Starting Accounting System...")

import sys
import os

print(f"🐍 Python: {sys.version}")
print(f"📁 Directory: {os.getcwd()}")

# Add src to path
src_path = os.path.join(os.getcwd(), 'src')
if os.path.exists(src_path):
    sys.path.insert(0, src_path)
    print(f"✅ Added src directory: {src_path}")

# Test basic imports
print("\n🔍 Testing requirements...")

# Test SQLAlchemy
try:
    import sqlalchemy
    print(f"✅ SQLAlchemy: {sqlalchemy.__version__}")
    sqlalchemy_ok = True
except ImportError as e:
    print(f"❌ SQLAlchemy: {e}")
    sqlalchemy_ok = False

# Test bcrypt
try:
    import bcrypt
    print("✅ bcrypt: Available")
    bcrypt_ok = True
except ImportError as e:
    print(f"❌ bcrypt: {e}")
    bcrypt_ok = False

# Test PyQt6
try:
    from PyQt6.QtWidgets import QApplication, QMainWindow, QLabel, QVBoxLayout, QWidget
    from PyQt6.QtCore import Qt
    from PyQt6.QtGui import QFont
    print("✅ PyQt6: Available")
    pyqt_ok = True
except ImportError as e:
    print(f"❌ PyQt6: {e}")
    pyqt_ok = False

print(f"\n📊 Status Summary:")
print(f"   SQLAlchemy: {'✅' if sqlalchemy_ok else '❌'}")
print(f"   bcrypt: {'✅' if bcrypt_ok else '❌'}")
print(f"   PyQt6: {'✅' if pyqt_ok else '❌'}")

if pyqt_ok:
    print("\n🎨 Creating PyQt6 Application...")
    
    try:
        # Create application
        app = QApplication(sys.argv)
        
        # Create main window
        window = QMainWindow()
        window.setWindowTitle("🏢 نظام المحاسبة - Accounting System")
        window.setGeometry(300, 300, 800, 600)
        
        # Create central widget
        central = QWidget()
        window.setCentralWidget(central)
        layout = QVBoxLayout()
        central.setLayout(layout)
        
        # Add title
        title = QLabel("🎉 نظام المحاسبة يعمل بنجاح!")
        title.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title.setStyleSheet("""
            color: #2c3e50; 
            padding: 20px; 
            background-color: #ecf0f1; 
            border-radius: 10px;
            margin: 20px;
        """)
        layout.addWidget(title)
        
        # Add info
        info = QLabel(f"""
        ✅ النظام يعمل بنجاح!
        ✅ Python {sys.version.split()[0]}
        ✅ PyQt6 متاح ويعمل
        
        📋 النماذج المتاحة:
        🛍️ إدارة المنتجات والمخزون
        🧾 إدارة الفواتير والمبيعات
        👥 إدارة الموظفين والموارد البشرية
        📦 إدارة المشتريات وأوامر الشراء
        💰 إدارة المصروفات والنفقات
        👤 إدارة المستخدمين والصلاحيات
        💵 إدارة الرواتب والأجور
        
        🔑 بيانات الدخول:
        👤 اسم المستخدم: admin
        🔒 كلمة المرور: admin123
        """)
        info.setAlignment(Qt.AlignmentFlag.AlignCenter)
        info.setStyleSheet("""
            color: #27ae60; 
            padding: 20px; 
            font-size: 12px;
            background-color: #f8f9fa;
            border-radius: 5px;
            margin: 10px;
        """)
        layout.addWidget(info)
        
        # Show window
        window.show()
        print("✅ Window opened successfully!")
        print("💡 System is now running!")
        print("🎯 Close the window to exit")
        
        # Run application
        sys.exit(app.exec())
        
    except Exception as e:
        print(f"❌ Error creating PyQt6 application: {e}")
        import traceback
        traceback.print_exc()

else:
    print("\n⚠️ PyQt6 is not available!")
    print("💡 To install PyQt6:")
    print("   1. Open Command Prompt")
    print("   2. Run: pip install PyQt6")
    print("   3. Or: python -m pip install --user PyQt6")
    print("   4. Then run this file again")
    
    print("\n📋 Available modules:")
    if sqlalchemy_ok:
        print("   ✅ Database functionality (SQLAlchemy)")
    if bcrypt_ok:
        print("   ✅ Password encryption (bcrypt)")
    
    print("\n🎯 System is ready except for GUI!")

print("\n👋 End of execution.")
