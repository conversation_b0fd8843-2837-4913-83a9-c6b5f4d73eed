"""
Backup and restore functionality for the accounting system.
"""
import os
import shutil
import zipfile
from datetime import datetime
from src.config import DB_NAME
from src.utils.translation import _

def create_backup(backup_dir: str = None) -> str:
    """
    Create a backup of the database and other important files.
    
    Args:
        backup_dir (str, optional): Directory to store backups. Defaults to 'backups'.
    
    Returns:
        str: Path to the created backup file.
    """
    if not backup_dir:
        backup_dir = 'backups'
    
    # Ensure backup directory exists
    os.makedirs(backup_dir, exist_ok=True)
    
    # Create backup filename with timestamp
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    backup_filename = os.path.join(backup_dir, f'backup_{timestamp}.zip')
    
    # Create zip file
    with zipfile.ZipFile(backup_filename, 'w') as backup_zip:
        # Add database file
        if os.path.exists(DB_NAME):
            backup_zip.write(DB_NAME, os.path.basename(DB_NAME))
        
        # Add other important files (config, etc.)
        important_files = [
            '.env',
            'config.py',
            'requirements.txt'
        ]
        
        for file in important_files:
            if os.path.exists(file):
                backup_zip.write(file)
    
    return backup_filename

def restore_backup(backup_file: str) -> bool:
    """
    Restore system from a backup file.
    
    Args:
        backup_file (str): Path to the backup file to restore from.
    
    Returns:
        bool: True if restore was successful, False otherwise.
    """
    try:
        # Create temporary directory for restoration
        temp_dir = 'temp_restore'
        os.makedirs(temp_dir, exist_ok=True)
        
        # Extract backup
        with zipfile.ZipFile(backup_file, 'r') as backup_zip:
            backup_zip.extractall(temp_dir)
        
        # Stop any database connections
        # TODO: Implement proper database connection handling
        
        # Restore database file
        db_backup = os.path.join(temp_dir, os.path.basename(DB_NAME))
        if os.path.exists(db_backup):
            shutil.copy2(db_backup, DB_NAME)
        
        # Restore other files
        for file in os.listdir(temp_dir):
            if file != os.path.basename(DB_NAME):
                src = os.path.join(temp_dir, file)
                shutil.copy2(src, file)
        
        # Clean up
        shutil.rmtree(temp_dir)
        
        return True
        
    except Exception as e:
        print(f"Error during restore: {e}")
        return False
