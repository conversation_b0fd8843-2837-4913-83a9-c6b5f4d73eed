# تثبيت PyQt6 باستخدام PowerShell
Write-Host "🚀 تثبيت PyQt6..." -ForegroundColor Green

# اختبار Python
Write-Host "🔍 اختبار Python..." -ForegroundColor Yellow
try {
    $pythonVersion = python --version 2>&1
    Write-Host "✅ Python متاح: $pythonVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Python غير متاح!" -ForegroundColor Red
    Read-Host "اضغط Enter للخروج"
    exit 1
}

# تحديث pip
Write-Host "🔧 تحديث pip..." -ForegroundColor Yellow
python -m pip install --upgrade pip

# تثبيت PyQt6 - الطريقة 1
Write-Host "📦 تثبيت PyQt6 - الطريقة 1..." -ForegroundColor Yellow
$result1 = python -m pip install --user PyQt6 2>&1
if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ الطريقة 1 نجحت!" -ForegroundColor Green
} else {
    Write-Host "⚠️ الطريقة 1 فشلت، جرب الطريقة 2..." -ForegroundColor Yellow
    
    # تثبيت PyQt6 - الطريقة 2
    Write-Host "📦 تثبيت PyQt6 - الطريقة 2..." -ForegroundColor Yellow
    $result2 = pip install --user PyQt6 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ الطريقة 2 نجحت!" -ForegroundColor Green
    } else {
        Write-Host "⚠️ الطريقة 2 فشلت، جرب الطريقة 3..." -ForegroundColor Yellow
        
        # تثبيت PyQt6 - الطريقة 3
        Write-Host "📦 تثبيت PyQt6 - الطريقة 3..." -ForegroundColor Yellow
        $result3 = python -m pip install PyQt6 2>&1
        if ($LASTEXITCODE -ne 0) {
            Write-Host "❌ جميع الطرق فشلت!" -ForegroundColor Red
            Write-Host "💡 جرب تشغيل PowerShell كمدير" -ForegroundColor Cyan
            Read-Host "اضغط Enter للخروج"
            exit 1
        } else {
            Write-Host "✅ الطريقة 3 نجحت!" -ForegroundColor Green
        }
    }
}

# اختبار PyQt6
Write-Host "🔍 اختبار PyQt6..." -ForegroundColor Yellow
$testResult = python -c "from PyQt6.QtWidgets import QApplication; print('✅ PyQt6 يعمل!')" 2>&1
if ($LASTEXITCODE -eq 0) {
    Write-Host "🎉 تم تثبيت PyQt6 بنجاح!" -ForegroundColor Green
    Write-Host "💡 يمكنك الآن تشغيل النظام:" -ForegroundColor Cyan
    Write-Host "   python final_launch.py" -ForegroundColor White
} else {
    Write-Host "❌ PyQt6 لا يعمل!" -ForegroundColor Red
    Write-Host "الخطأ: $testResult" -ForegroundColor Red
}

Read-Host "اضغط Enter للخروج"
