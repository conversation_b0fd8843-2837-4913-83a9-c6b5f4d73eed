"""
Inventory report generation.
"""
from datetime import datetime
import pandas as pd
from reportlab.lib import colors
from reportlab.lib.units import inch
from reportlab.platypus import Paragraph, Spacer, Table
from src.database.database import SessionLocal
from src.models.inventory import Product
from src.utils.translation import _
from .base_report import BaseReport
from .excel_generator import ExcelReportGenerator

class InventoryReport(BaseReport):
    """Inventory report generator."""

    def get_report_name(self) -> str:
        return _("Inventory Report")

    def get_content(self) -> list:
        session = SessionLocal()
        story = []
        try:
            # Add report description
            story.append(Spacer(1, 12))
            story.append(Paragraph(_("Current Inventory Status"), self.styles['Heading1']))
            story.append(Spacer(1, 12))

            # Get products
            products = session.query(Product).all()

            # Create table data
            data = [
                [_("Code"), _("Name"), _("Category"), _("Quantity"), _("Unit Price"), _("Total Value")]
            ]

            total_value = 0
            for product in products:
                row = [
                    product.code,
                    product.name,
                    product.category,
                    str(product.quantity),
                    f"{product.unit_price:,.2f}",
                    f"{(product.quantity * product.unit_price):,.2f}"
                ]
                data.append(row)
                total_value += product.quantity * product.unit_price

            # Add total row
            data.append([
                "", "", "",
                _("Total:"),
                "",
                f"{total_value:,.2f}"
            ])

            # Create table
            table = self.create_table(data)
            story.append(table)

            # Add low stock warning section
            story.append(Spacer(1, 20))
            story.append(Paragraph(_("Low Stock Warning"), self.styles['Heading2']))
            story.append(Spacer(1, 12))

            low_stock = [p for p in products if p.quantity <= p.minimum_quantity]
            if low_stock:
                warning_data = [
                    [_("Code"), _("Name"), _("Current Quantity"), _("Minimum Quantity")]
                ]
                for product in low_stock:
                    warning_data.append([
                        product.code,
                        product.name,
                        str(product.quantity),
                        str(product.minimum_quantity)
                    ])
                warning_table = self.create_table(warning_data)
                story.append(warning_table)
            else:
                story.append(Paragraph(_("No products below minimum quantity."), self.styles['Normal']))

        finally:
            session.close()

        return story

    def generate_excel(self) -> str:
        """Generate Excel version of the report."""
        session = SessionLocal()
        try:
            # Get products
            products = session.query(Product).all()

            # Create DataFrames
            inventory_data = {
                'code': [],
                'name': [],
                'category': [],
                'quantity': [],
                'unit_price': [],
                'total_value': []
            }

            low_stock_data = {
                'code': [],
                'name': [],
                'current_quantity': [],
                'minimum_quantity': []
            }

            for product in products:
                # Add to inventory sheet
                inventory_data['code'].append(product.code)
                inventory_data['name'].append(product.name)
                inventory_data['category'].append(product.category)
                inventory_data['quantity'].append(product.quantity)
                inventory_data['unit_price'].append(product.unit_price)
                inventory_data['total_value'].append(product.quantity * product.unit_price)

                # Check for low stock
                if product.quantity <= product.minimum_quantity:
                    low_stock_data['code'].append(product.code)
                    low_stock_data['name'].append(product.name)
                    low_stock_data['current_quantity'].append(product.quantity)
                    low_stock_data['minimum_quantity'].append(product.minimum_quantity)

            # Create DataFrame
            inventory_df = pd.DataFrame(inventory_data)
            low_stock_df = pd.DataFrame(low_stock_data)

            # Generate Excel file
            generator = ExcelReportGenerator()
            return generator.generate({
                _('Inventory'): inventory_df,
                _('Low Stock'): low_stock_df
            })
        finally:
            session.close()
