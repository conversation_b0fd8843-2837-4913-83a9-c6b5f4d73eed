"""
HR and Payroll related models.
"""
from sqlalchemy import Column, Integer, String, Float, DateTime, ForeignKey, Enum, Text, Boolean, Date
from sqlalchemy.orm import relationship, backref
from datetime import datetime
from src.database.database import Base
import enum

class EmploymentType(enum.Enum):
    """Employment type enumeration."""
    FULL_TIME = "full_time"
    PART_TIME = "part_time"
    CONTRACT = "contract"
    TEMPORARY = "temporary"

class LeaveType(enum.Enum):
    """Leave type enumeration."""
    ANNUAL = "annual"
    SICK = "sick"
    UNPAID = "unpaid"
    EMERGENCY = "emergency"
    OTHER = "other"

class Department(Base):
    """Department model."""
    __tablename__ = 'departments'

    id = Column(Integer, primary_key=True)
    name = Column(String(100), nullable=False)
    description = Column(Text)
    manager_id = Column(Integer, ForeignKey('employees.id'))
    parent_id = Column(Integer, ForeignKey('departments.id'))
    
    # Self-referential relationship for hierarchical departments
    children = relationship('Department', 
                          backref=backref('parent', remote_side=[id]))
    # Relationship with employees in this department
    employees = relationship('Employee', 
                           foreign_keys='[Employee.department_id]',
                           back_populates='department')
    # Relationship with manager
    manager = relationship('Employee', 
                         foreign_keys='[Department.manager_id]',
                         back_populates='managed_departments')

    def __repr__(self):
        return f'<Department {self.name}>'

class Employee(Base):
    """Employee model."""
    __tablename__ = 'employees'

    id = Column(Integer, primary_key=True)
    code = Column(String(20), unique=True, nullable=False)
    first_name = Column(String(50), nullable=False)
    last_name = Column(String(50), nullable=False)
    email = Column(String(120), unique=True)
    phone = Column(String(20))
    mobile = Column(String(20))
    address = Column(Text)
    birth_date = Column(Date)
    hire_date = Column(Date, nullable=False)
    employment_type = Column(Enum(EmploymentType), default=EmploymentType.FULL_TIME)
    department_id = Column(Integer, ForeignKey('departments.id'))
    position = Column(String(100))
    basic_salary = Column(Float, default=0.0)
    is_active = Column(Boolean, default=True)
    notes = Column(Text)
    created_at = Column(DateTime, default=datetime.utcnow)

    # Relationship with department
    department = relationship('Department', 
                            foreign_keys=[department_id],
                            back_populates='employees')
    # Departments managed by this employee
    managed_departments = relationship('Department',
                                   foreign_keys='[Department.manager_id]',
                                   back_populates='manager')
    # Salary payments
    salary_payments = relationship('SalaryPayment', back_populates='employee')
    # Leaves
    leaves = relationship('Leave', back_populates='employee')
    # Documents
    documents = relationship('EmployeeDocument', back_populates='employee')
    # Allowances
    allowances = relationship('Allowance', back_populates='employee')
    # Deductions
    deductions = relationship('Deduction', back_populates='employee')

    def __str__(self):
        return f'{self.first_name} {self.last_name}'

    def __repr__(self):
        return f'<Employee {self.code}>'

class SalaryPayment(Base):
    """Salary payment model."""
    __tablename__ = 'salary_payments'

    id = Column(Integer, primary_key=True)
    employee_id = Column(Integer, ForeignKey('employees.id'), nullable=False)
    payment_date = Column(Date, nullable=False)
    month = Column(Integer, nullable=False)
    year = Column(Integer, nullable=False)
    basic_salary = Column(Float, default=0.0)
    allowances = Column(Float, default=0.0)
    deductions = Column(Float, default=0.0)
    tax_amount = Column(Float, default=0.0)
    social_security = Column(Float, default=0.0)
    net_salary = Column(Float, default=0.0)
    payment_method = Column(String(50))
    reference = Column(String(100))
    notes = Column(Text)
    created_at = Column(DateTime, default=datetime.utcnow)
    created_by = Column(Integer, ForeignKey('users.id'))

    # Relationships
    employee = relationship('Employee', back_populates='salary_payments')

    def __repr__(self):
        return f'<SalaryPayment {self.id}>'

class Leave(Base):
    """Employee leave model."""
    __tablename__ = 'leaves'

    id = Column(Integer, primary_key=True)
    employee_id = Column(Integer, ForeignKey('employees.id'), nullable=False)
    leave_type = Column(Enum(LeaveType), nullable=False)
    start_date = Column(Date, nullable=False)
    end_date = Column(Date, nullable=False)
    days = Column(Integer, default=1)
    reason = Column(Text)
    status = Column(String(20), default='pending')  # pending, approved, rejected
    approved_by = Column(Integer, ForeignKey('users.id'))
    approved_at = Column(DateTime)
    created_at = Column(DateTime, default=datetime.utcnow)

    def __repr__(self):
        return f'<Leave {self.id}>'

class EmployeeDocument(Base):
    """Employee document model for storing employee-related files."""
    __tablename__ = 'employee_documents'

    id = Column(Integer, primary_key=True)
    employee_id = Column(Integer, ForeignKey('employees.id'), nullable=False)
    document_type = Column(String(50), nullable=False)
    title = Column(String(200), nullable=False)
    file_path = Column(String(500), nullable=False)
    upload_date = Column(DateTime, default=datetime.utcnow)
    expiry_date = Column(Date)
    notes = Column(Text)
    created_by = Column(Integer, ForeignKey('users.id'))

    def __repr__(self):
        return f'<EmployeeDocument {self.title}>'

class Allowance(Base):
    """Allowance type model."""
    __tablename__ = 'allowances'

    id = Column(Integer, primary_key=True)
    name = Column(String(100), nullable=False)
    description = Column(Text)
    amount = Column(Float, default=0.0)
    is_percentage = Column(Boolean, default=False)
    is_taxable = Column(Boolean, default=True)
    is_active = Column(Boolean, default=True)

    def __repr__(self):
        return f'<Allowance {self.name}>'

class Deduction(Base):
    """Deduction type model."""
    __tablename__ = 'deductions'

    id = Column(Integer, primary_key=True)
    name = Column(String(100), nullable=False)
    description = Column(Text)
    amount = Column(Float, default=0.0)
    is_percentage = Column(Boolean, default=False)
    is_tax_deductible = Column(Boolean, default=False)
    is_active = Column(Boolean, default=True)

    def __repr__(self):
        return f'<Deduction {self.name}>'
