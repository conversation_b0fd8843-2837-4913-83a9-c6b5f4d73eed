#!/usr/bin/env python3
"""
نسخة وحدة التحكم من نظام المحاسبة
Console Version of Accounting System
"""
import sys
import os

# Add src to path
src_path = os.path.join(os.getcwd(), 'src')
if os.path.exists(src_path):
    sys.path.insert(0, src_path)

def print_header():
    """طباعة رأس النظام"""
    print("=" * 60)
    print("🏢 نظام المحاسبة المتكامل - Accounting System")
    print("=" * 60)
    print("📋 النماذج المتاحة:")
    print("   1. 🛍️ إدارة المنتجات والمخزون")
    print("   2. 🧾 إدارة الفواتير والمبيعات")
    print("   3. 👥 إدارة الموظفين والموارد البشرية")
    print("   4. 📦 إدارة المشتريات وأوامر الشراء")
    print("   5. 💰 إدارة المصروفات والنفقات")
    print("   6. 👤 إدارة المستخدمين والصلاحيات")
    print("   7. 💵 إدارة الرواتب والأجور")
    print("=" * 60)

def check_requirements():
    """فحص المتطلبات"""
    print("🔍 فحص المتطلبات...")
    
    requirements = {
        "Python": True,
        "SQLAlchemy": False,
        "bcrypt": False,
        "PyQt6": False
    }
    
    # Test SQLAlchemy
    try:
        import sqlalchemy
        requirements["SQLAlchemy"] = True
        print(f"✅ SQLAlchemy: {sqlalchemy.__version__}")
    except ImportError:
        print("❌ SQLAlchemy: غير متاح")
    
    # Test bcrypt
    try:
        import bcrypt
        requirements["bcrypt"] = True
        print("✅ bcrypt: متاح")
    except ImportError:
        print("❌ bcrypt: غير متاح")
    
    # Test PyQt6
    try:
        from PyQt6.QtWidgets import QApplication
        requirements["PyQt6"] = True
        print("✅ PyQt6: متاح")
    except ImportError:
        print("❌ PyQt6: غير متاح")
    
    return requirements

def test_database():
    """اختبار قاعدة البيانات"""
    print("\n🗄️ اختبار قاعدة البيانات...")
    
    try:
        from src.database.database import SessionLocal, engine
        session = SessionLocal()
        
        # Test connection
        result = session.execute("SELECT 1").fetchone()
        session.close()
        
        print("✅ قاعدة البيانات تعمل بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في قاعدة البيانات: {e}")
        return False

def test_models():
    """اختبار النماذج"""
    print("\n📊 اختبار النماذج...")
    
    models_to_test = [
        ("Product", "src.models.inventory"),
        ("Customer", "src.models.customers"),
        ("Employee", "src.models.hr"),
        ("Invoice", "src.models.sales"),
        ("Expense", "src.models.accounting"),
        ("User", "src.models.auth"),
    ]
    
    successful_models = 0
    
    for model_name, module_path in models_to_test:
        try:
            module = __import__(module_path, fromlist=[model_name])
            model_class = getattr(module, model_name)
            print(f"✅ {model_name}: متاح")
            successful_models += 1
        except Exception as e:
            print(f"❌ {model_name}: {str(e)[:50]}...")
    
    print(f"📊 النماذج المتاحة: {successful_models}/{len(models_to_test)}")
    return successful_models > 0

def show_install_instructions():
    """عرض تعليمات التثبيت"""
    print("\n🔧 تعليمات التثبيت:")
    print("=" * 40)
    print("لتشغيل النظام بالكامل، تحتاج إلى تثبيت PyQt6:")
    print()
    print("🪟 Windows:")
    print("   pip install PyQt6")
    print("   أو")
    print("   python -m pip install --user PyQt6")
    print()
    print("🐧 Linux:")
    print("   pip3 install --user PyQt6")
    print("   أو")
    print("   sudo apt install python3-pyqt6")
    print()
    print("🍎 macOS:")
    print("   pip3 install PyQt6")
    print("   أو")
    print("   brew install pyqt6")
    print()
    print("📁 الملفات المساعدة:")
    print("   - install_pyqt6.bat (Windows)")
    print("   - install_pyqt6.ps1 (PowerShell)")
    print("   - PYQT6_INSTALL_GUIDE.md (دليل شامل)")
    print("=" * 40)

def main():
    """الدالة الرئيسية"""
    print_header()
    
    # Check requirements
    requirements = check_requirements()
    
    # Test database
    db_works = test_database()
    
    # Test models
    models_work = test_models()
    
    # Summary
    print("\n📊 ملخص النظام:")
    print("=" * 30)
    print(f"🐍 Python: ✅ {sys.version.split()[0]}")
    print(f"🗄️ قاعدة البيانات: {'✅' if db_works else '❌'}")
    print(f"📊 النماذج: {'✅' if models_work else '❌'}")
    print(f"🎨 PyQt6: {'✅' if requirements['PyQt6'] else '❌'}")
    print("=" * 30)
    
    if requirements['PyQt6']:
        print("\n🎉 جميع المتطلبات متوفرة!")
        print("💡 يمكنك تشغيل النظام الكامل:")
        print("   python final_launch.py")
        
        choice = input("\n🤔 هل تريد تشغيل النظام الآن؟ (y/n): ").lower()
        if choice in ['y', 'yes', 'نعم', 'ن']:
            print("🚀 تشغيل النظام...")
            try:
                import subprocess
                subprocess.run([sys.executable, "final_launch.py"])
            except Exception as e:
                print(f"❌ خطأ في التشغيل: {e}")
    else:
        print("\n⚠️ PyQt6 غير مثبت!")
        show_install_instructions()
        
        print("\n💡 بعد تثبيت PyQt6، شغل:")
        print("   python final_launch.py")
    
    print("\n👋 شكراً لاستخدام نظام المحاسبة!")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 تم إلغاء العملية. وداعاً!")
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        import traceback
        traceback.print_exc()
