#!/usr/bin/env python3
"""
إعداد بيانات اختبار للنظام
Setup Test Data for the System
"""
import sys
import os
from datetime import datetime, date

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

try:
    from src.database.database import SessionLocal, engine
    from src.models import Base
    from src.models.inventory import Product, Category, Unit
    from src.models.customers import Customer
    from src.models.suppliers import Supplier
    from src.models.hr import Employee, Department, EmploymentType
    from src.models.accounting import ExpenseCategory
    from src.models.auth import User, Role
    import bcrypt
    print("✅ تم تحميل جميع النماذج بنجاح!")
except ImportError as e:
    print(f"❌ خطأ في تحميل النماذج: {e}")
    sys.exit(1)

def create_tables():
    """إنشاء جداول قاعدة البيانات"""
    try:
        print("🔧 إنشاء جداول قاعدة البيانات...")
        Base.metadata.create_all(bind=engine)
        print("✅ تم إنشاء الجداول بنجاح!")
        return True
    except Exception as e:
        print(f"❌ خطأ في إنشاء الجداول: {e}")
        return False

def setup_basic_data():
    """إعداد البيانات الأساسية"""
    session = SessionLocal()
    try:
        print("📊 إعداد البيانات الأساسية...")
        
        # 1. إنشاء الوحدات
        units = [
            Unit(name="قطعة", symbol="قطعة"),
            Unit(name="كيلو", symbol="كجم"),
            Unit(name="متر", symbol="م"),
            Unit(name="لتر", symbol="ل"),
        ]
        
        for unit in units:
            existing = session.query(Unit).filter_by(name=unit.name).first()
            if not existing:
                session.add(unit)
        
        # 2. إنشاء التصنيفات
        categories = [
            Category(name="إلكترونيات", description="أجهزة إلكترونية"),
            Category(name="ملابس", description="ملابس وأزياء"),
            Category(name="طعام", description="مواد غذائية"),
            Category(name="كتب", description="كتب ومطبوعات"),
        ]
        
        for category in categories:
            existing = session.query(Category).filter_by(name=category.name).first()
            if not existing:
                session.add(category)
        
        session.commit()
        
        # 3. إنشاء منتجات تجريبية
        products = [
            Product(
                code="PROD001",
                name="لابتوب ديل",
                description="لابتوب ديل انسبايرون 15",
                category_id=1,
                unit_id=1,
                purchase_price=800.00,
                sale_price=1000.00,
                current_stock=10,
                minimum_stock=2,
                tax_rate=15.0,
                is_active=True
            ),
            Product(
                code="PROD002", 
                name="قميص قطني",
                description="قميص قطني أزرق",
                category_id=2,
                unit_id=1,
                purchase_price=25.00,
                sale_price=40.00,
                current_stock=50,
                minimum_stock=10,
                tax_rate=5.0,
                is_active=True
            ),
        ]
        
        for product in products:
            existing = session.query(Product).filter_by(code=product.code).first()
            if not existing:
                session.add(product)
        
        # 4. إنشاء العملاء
        customers = [
            Customer(
                name="أحمد محمد",
                email="<EMAIL>",
                phone="*********",
                address="الرياض، السعودية",
                is_active=True
            ),
            Customer(
                name="فاطمة علي",
                email="<EMAIL>", 
                phone="*********",
                address="جدة، السعودية",
                is_active=True
            ),
        ]
        
        for customer in customers:
            existing = session.query(Customer).filter_by(email=customer.email).first()
            if not existing:
                session.add(customer)
        
        # 5. إنشاء الموردين
        suppliers = [
            Supplier(
                name="شركة التقنية المتقدمة",
                email="<EMAIL>",
                phone="111222333",
                address="الدمام، السعودية",
                is_active=True
            ),
            Supplier(
                name="مؤسسة الملابس الحديثة",
                email="<EMAIL>",
                phone="444555666", 
                address="الرياض، السعودية",
                is_active=True
            ),
        ]
        
        for supplier in suppliers:
            existing = session.query(Supplier).filter_by(email=supplier.email).first()
            if not existing:
                session.add(supplier)
        
        # 6. إنشاء الأقسام
        departments = [
            Department(name="المبيعات", description="قسم المبيعات"),
            Department(name="المحاسبة", description="قسم المحاسبة"),
            Department(name="الموارد البشرية", description="قسم الموارد البشرية"),
        ]
        
        for dept in departments:
            existing = session.query(Department).filter_by(name=dept.name).first()
            if not existing:
                session.add(dept)
        
        session.commit()
        
        # 7. إنشاء الموظفين
        employees = [
            Employee(
                code="EMP001",
                first_name="محمد",
                last_name="أحمد",
                email="<EMAIL>",
                phone="555111222",
                hire_date=date(2023, 1, 15),
                department_id=1,
                position="مندوب مبيعات",
                employment_type=EmploymentType.FULL_TIME,
                basic_salary=5000.00,
                is_active=True
            ),
            Employee(
                code="EMP002",
                first_name="سارة",
                last_name="محمد",
                email="<EMAIL>",
                phone="555333444",
                hire_date=date(2023, 2, 1),
                department_id=2,
                position="محاسبة",
                employment_type=EmploymentType.FULL_TIME,
                basic_salary=6000.00,
                is_active=True
            ),
        ]
        
        for employee in employees:
            existing = session.query(Employee).filter_by(code=employee.code).first()
            if not existing:
                session.add(employee)
        
        # 8. إنشاء فئات المصروفات
        expense_categories = [
            ExpenseCategory(name="مصاريف مكتبية", description="أدوات مكتبية وقرطاسية", is_active=True),
            ExpenseCategory(name="مصاريف سفر", description="تذاكر طيران وإقامة", is_active=True),
            ExpenseCategory(name="مصاريف صيانة", description="صيانة المعدات والأجهزة", is_active=True),
        ]
        
        for category in expense_categories:
            existing = session.query(ExpenseCategory).filter_by(name=category.name).first()
            if not existing:
                session.add(category)
        
        # 9. إنشاء الأدوار
        roles = [
            Role(name="مدير النظام", description="صلاحيات كاملة"),
            Role(name="محاسب", description="صلاحيات المحاسبة"),
            Role(name="مندوب مبيعات", description="صلاحيات المبيعات"),
        ]
        
        for role in roles:
            existing = session.query(Role).filter_by(name=role.name).first()
            if not existing:
                session.add(role)
        
        session.commit()
        
        # 10. إنشاء مستخدم تجريبي
        admin_password = bcrypt.hashpw("admin123".encode('utf-8'), bcrypt.gensalt())
        admin_user = User(
            username="admin",
            email="<EMAIL>",
            password_hash=admin_password.decode('utf-8'),
            role_id=1,
            is_active=True,
            created_at=datetime.now()
        )
        
        existing_user = session.query(User).filter_by(username="admin").first()
        if not existing_user:
            session.add(admin_user)
        
        session.commit()
        print("✅ تم إعداد البيانات الأساسية بنجاح!")
        return True
        
    except Exception as e:
        session.rollback()
        print(f"❌ خطأ في إعداد البيانات: {e}")
        return False
    finally:
        session.close()

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء إعداد قاعدة البيانات والبيانات التجريبية...")
    
    # إنشاء الجداول
    if not create_tables():
        print("❌ فشل في إنشاء الجداول!")
        return False
    
    # إعداد البيانات الأساسية
    if not setup_basic_data():
        print("❌ فشل في إعداد البيانات الأساسية!")
        return False
    
    print("\n🎉 تم إعداد النظام بنجاح!")
    print("📋 البيانات التجريبية المتاحة:")
    print("   👤 مستخدم تجريبي: admin / admin123")
    print("   🛍️ منتجات تجريبية: لابتوب، قميص")
    print("   👥 موظفين تجريبيين: محمد، سارة")
    print("   🏢 عملاء وموردين تجريبيين")
    print("   📊 تصنيفات ووحدات أساسية")
    print("\n✅ النظام جاهز للاختبار!")
    return True

if __name__ == "__main__":
    main()
