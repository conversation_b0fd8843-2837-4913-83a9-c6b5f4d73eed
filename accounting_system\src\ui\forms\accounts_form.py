"""
Accounts Management Form
نموذج إدارة الحسابات
"""
from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
                            QTableWidget, QTableWidgetItem, QHeaderView,
                            QMessageBox, QDialog, QFormLayout, QLineEdit,
                            QComboBox, QTextEdit, QLabel, QGroupBox, QTreeWidget,
                            QTreeWidgetItem, QSplitter)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QFont
from sqlalchemy.orm import sessionmaker
from src.database.database import engine
from src.models.accounting import Account, AccountType
from src.utils.translation import _

SessionLocal = sessionmaker(bind=engine)

class AccountsForm(QWidget):
    """Accounts management form."""

    def __init__(self):
        super().__init__()
        self.init_ui()
        self.load_data()

    def init_ui(self):
        """Initialize the user interface."""
        layout = QVBoxLayout()

        # Title
        title = QLabel(_('Chart of Accounts') if _('Chart of Accounts') != 'Chart of Accounts' else 'دليل الحسابات')
        title.setFont(QFont('Arial', 16, QFont.Weight.Bold))
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title)

        # Create splitter for tree and details
        splitter = QSplitter(Qt.Orientation.Horizontal)

        # Left side - Account tree
        self.create_account_tree(splitter)

        # Right side - Account details and operations
        self.create_account_details(splitter)

        layout.addWidget(splitter)
        self.setLayout(layout)

    def create_account_tree(self, parent):
        """Create account tree widget."""
        tree_widget = QWidget()
        tree_layout = QVBoxLayout()

        # Tree controls
        tree_controls = QHBoxLayout()

        self.add_account_btn = QPushButton(_('Add Account') if _('Add Account') != 'Add Account' else 'إضافة حساب')
        self.add_account_btn.clicked.connect(self.add_account)
        tree_controls.addWidget(self.add_account_btn)

        self.edit_account_btn = QPushButton(_('Edit Account') if _('Edit Account') != 'Edit Account' else 'تعديل حساب')
        self.edit_account_btn.clicked.connect(self.edit_account)
        tree_controls.addWidget(self.edit_account_btn)

        self.delete_account_btn = QPushButton(_('Delete Account') if _('Delete Account') != 'Delete Account' else 'حذف حساب')
        self.delete_account_btn.clicked.connect(self.delete_account)
        tree_controls.addWidget(self.delete_account_btn)

        tree_controls.addStretch()
        tree_layout.addLayout(tree_controls)

        # Account tree
        self.account_tree = QTreeWidget()
        self.account_tree.setHeaderLabels([
            _('Account') if _('Account') != 'Account' else 'الحساب',
            _('Code') if _('Code') != 'Code' else 'الكود',
            _('Type') if _('Type') != 'Type' else 'النوع',
            _('Balance') if _('Balance') != 'Balance' else 'الرصيد'
        ])
        self.account_tree.itemSelectionChanged.connect(self.on_account_selected)
        tree_layout.addWidget(self.account_tree)

        tree_widget.setLayout(tree_layout)
        parent.addWidget(tree_widget)

    def create_account_details(self, parent):
        """Create account details widget."""
        details_widget = QWidget()
        details_layout = QVBoxLayout()

        # Account details group
        details_group = QGroupBox(_('Account Details') if _('Account Details') != 'Account Details' else 'تفاصيل الحساب')
        details_form = QFormLayout()

        self.account_name_label = QLabel('-')
        self.account_code_label = QLabel('-')
        self.account_type_label = QLabel('-')
        self.account_balance_label = QLabel('-')
        self.account_description_label = QLabel('-')

        details_form.addRow(_('Name') if _('Name') != 'Name' else 'الاسم:', self.account_name_label)
        details_form.addRow(_('Code') if _('Code') != 'Code' else 'الكود:', self.account_code_label)
        details_form.addRow(_('Type') if _('Type') != 'Type' else 'النوع:', self.account_type_label)
        details_form.addRow(_('Balance') if _('Balance') != 'Balance' else 'الرصيد:', self.account_balance_label)
        details_form.addRow(_('Description') if _('Description') != 'Description' else 'الوصف:', self.account_description_label)

        details_group.setLayout(details_form)
        details_layout.addWidget(details_group)

        # Recent transactions group
        transactions_group = QGroupBox(_('Recent Transactions') if _('Recent Transactions') != 'Recent Transactions' else 'المعاملات الأخيرة')
        transactions_layout = QVBoxLayout()

        self.transactions_table = QTableWidget()
        self.transactions_table.setColumnCount(5)
        headers = [
            _('Date') if _('Date') != 'Date' else 'التاريخ',
            _('Description') if _('Description') != 'Description' else 'الوصف',
            _('Reference') if _('Reference') != 'Reference' else 'المرجع',
            _('Debit') if _('Debit') != 'Debit' else 'مدين',
            _('Credit') if _('Credit') != 'Credit' else 'دائن'
        ]
        self.transactions_table.setHorizontalHeaderLabels(headers)
        self.transactions_table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        self.transactions_table.setAlternatingRowColors(True)
        self.transactions_table.setMaximumHeight(200)

        transactions_layout.addWidget(self.transactions_table)
        transactions_group.setLayout(transactions_layout)
        details_layout.addWidget(transactions_group)

        details_layout.addStretch()
        details_widget.setLayout(details_layout)
        parent.addWidget(details_widget)

    def load_data(self):
        """Load accounts data."""
        session = SessionLocal()
        try:
            accounts = session.query(Account).order_by(Account.code).all()
            self.populate_tree(accounts)
        finally:
            session.close()

    def populate_tree(self, accounts):
        """Populate the account tree."""
        self.account_tree.clear()

        # Group accounts by type
        account_groups = {}
        for account in accounts:
            account_type = account.account_type
            if account_type not in account_groups:
                account_groups[account_type] = []
            account_groups[account_type].append(account)

        # Create tree items
        for account_type, type_accounts in account_groups.items():
            # Create type header
            type_name = self.get_account_type_name(account_type)
            type_item = QTreeWidgetItem([type_name, '', account_type, ''])
            type_item.setFont(0, QFont('Arial', 10, QFont.Weight.Bold))
            self.account_tree.addTopLevelItem(type_item)

            # Add accounts under type
            for account in type_accounts:
                balance = self.calculate_account_balance(account)
                account_item = QTreeWidgetItem([
                    account.name,
                    account.code,
                    account_type,
                    f"{balance:,.2f}"
                ])
                account_item.setData(0, Qt.ItemDataRole.UserRole, account.id)
                type_item.addChild(account_item)

        # Expand all items
        self.account_tree.expandAll()

    def get_account_type_name(self, account_type):
        """Get localized account type name."""
        type_names = {
            'ASSET': _('Assets') if _('Assets') != 'Assets' else 'الأصول',
            'LIABILITY': _('Liabilities') if _('Liabilities') != 'Liabilities' else 'الخصوم',
            'EQUITY': _('Equity') if _('Equity') != 'Equity' else 'حقوق الملكية',
            'REVENUE': _('Revenue') if _('Revenue') != 'Revenue' else 'الإيرادات',
            'EXPENSE': _('Expenses') if _('Expenses') != 'Expenses' else 'المصروفات'
        }
        return type_names.get(account_type, account_type)

    def calculate_account_balance(self, account):
        """Calculate account balance from journal entries."""
        balance = 0
        for line in account.journal_entries:
            if account.account_type in ['ASSET', 'EXPENSE']:
                balance += line.debit - line.credit
            else:  # LIABILITY, EQUITY, REVENUE
                balance += line.credit - line.debit
        return balance

    def on_account_selected(self):
        """Handle account selection."""
        current_item = self.account_tree.currentItem()
        if current_item and current_item.data(0, Qt.ItemDataRole.UserRole):
            account_id = current_item.data(0, Qt.ItemDataRole.UserRole)
            self.load_account_details(account_id)

    def load_account_details(self, account_id):
        """Load details for selected account."""
        session = SessionLocal()
        try:
            account = session.query(Account).filter(Account.id == account_id).first()
            if account:
                self.account_name_label.setText(account.name)
                self.account_code_label.setText(account.code)
                self.account_type_label.setText(self.get_account_type_name(account.account_type))

                balance = self.calculate_account_balance(account)
                self.account_balance_label.setText(f"{balance:,.2f}")

                self.account_description_label.setText(account.description or '-')

                # Load recent transactions
                self.load_account_transactions(account)
        finally:
            session.close()

    def load_account_transactions(self, account):
        """Load recent transactions for account."""
        lines = account.journal_entries[-10:]  # Last 10 transactions

        self.transactions_table.setRowCount(len(lines))

        for i, line in enumerate(lines):
            self.transactions_table.setItem(i, 0, QTableWidgetItem(line.journal_entry.entry_date.strftime('%Y-%m-%d')))
            self.transactions_table.setItem(i, 1, QTableWidgetItem(line.description or line.journal_entry.description))
            self.transactions_table.setItem(i, 2, QTableWidgetItem(line.journal_entry.reference or ''))
            self.transactions_table.setItem(i, 3, QTableWidgetItem(f"{line.debit:.2f}" if line.debit > 0 else ''))
            self.transactions_table.setItem(i, 4, QTableWidgetItem(f"{line.credit:.2f}" if line.credit > 0 else ''))

    def add_account(self):
        """Add new account."""
        dialog = AccountDialog(self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            self.load_data()

    def edit_account(self):
        """Edit selected account."""
        current_item = self.account_tree.currentItem()
        if current_item and current_item.data(0, Qt.ItemDataRole.UserRole):
            account_id = current_item.data(0, Qt.ItemDataRole.UserRole)
            session = SessionLocal()
            try:
                account = session.query(Account).filter(Account.id == account_id).first()
                if account:
                    dialog = AccountDialog(self, account)
                    if dialog.exec() == QDialog.DialogCode.Accepted:
                        self.load_data()
            finally:
                session.close()
        else:
            QMessageBox.warning(self, _('Warning'), _('Please select an account to edit.'))

    def delete_account(self):
        """Delete selected account."""
        current_item = self.account_tree.currentItem()
        if current_item and current_item.data(0, Qt.ItemDataRole.UserRole):
            account_id = current_item.data(0, Qt.ItemDataRole.UserRole)

            reply = QMessageBox.question(
                self,
                _('Confirm Delete'),
                _('Are you sure you want to delete this account?'),
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )

            if reply == QMessageBox.StandardButton.Yes:
                session = SessionLocal()
                try:
                    account = session.query(Account).filter(Account.id == account_id).first()
                    if account:
                        if account.journal_entries:
                            QMessageBox.warning(
                                self,
                                _('Cannot Delete'),
                                _('Cannot delete account with existing transactions.')
                            )
                            return

                        session.delete(account)
                        session.commit()
                        self.load_data()
                        QMessageBox.information(self, _('Success'), _('Account deleted successfully.'))
                except Exception as e:
                    session.rollback()
                    QMessageBox.critical(self, _('Error'), f"Failed to delete account: {str(e)}")
                finally:
                    session.close()
        else:
            QMessageBox.warning(self, _('Warning'), _('Please select an account to delete.'))


class AccountDialog(QDialog):
    """Dialog for adding/editing accounts."""

    def __init__(self, parent=None, account=None):
        super().__init__(parent)
        self.account = account
        self.init_ui()
        if account:
            self.load_account_data()

    def init_ui(self):
        """Initialize the user interface."""
        self.setWindowTitle(_('Account Details') if _('Account Details') != 'Account Details' else 'تفاصيل الحساب')
        self.setModal(True)
        self.resize(400, 300)

        layout = QFormLayout()

        # Account fields
        self.code_edit = QLineEdit()
        self.name_edit = QLineEdit()
        self.type_combo = QComboBox()

        # Populate account types
        account_types = [
            ('ASSET', _('Asset') if _('Asset') != 'Asset' else 'أصل'),
            ('LIABILITY', _('Liability') if _('Liability') != 'Liability' else 'خصم'),
            ('EQUITY', _('Equity') if _('Equity') != 'Equity' else 'حقوق ملكية'),
            ('REVENUE', _('Revenue') if _('Revenue') != 'Revenue' else 'إيراد'),
            ('EXPENSE', _('Expense') if _('Expense') != 'Expense' else 'مصروف')
        ]

        for type_code, type_name in account_types:
            self.type_combo.addItem(type_name, type_code)

        self.description_edit = QTextEdit()
        self.description_edit.setMaximumHeight(80)

        layout.addRow(_('Code') if _('Code') != 'Code' else 'الكود:', self.code_edit)
        layout.addRow(_('Name') if _('Name') != 'Name' else 'الاسم:', self.name_edit)
        layout.addRow(_('Type') if _('Type') != 'Type' else 'النوع:', self.type_combo)
        layout.addRow(_('Description') if _('Description') != 'Description' else 'الوصف:', self.description_edit)

        # Buttons
        button_layout = QHBoxLayout()
        self.save_btn = QPushButton(_('Save') if _('Save') != 'Save' else 'حفظ')
        self.save_btn.clicked.connect(self.save_account)
        self.cancel_btn = QPushButton(_('Cancel') if _('Cancel') != 'Cancel' else 'إلغاء')
        self.cancel_btn.clicked.connect(self.reject)

        button_layout.addWidget(self.save_btn)
        button_layout.addWidget(self.cancel_btn)

        layout.addRow(button_layout)
        self.setLayout(layout)

    def load_account_data(self):
        """Load account data for editing."""
        if self.account:
            self.code_edit.setText(self.account.code)
            self.code_edit.setReadOnly(True)
            self.name_edit.setText(self.account.name)

            index = self.type_combo.findData(self.account.account_type)
            if index >= 0:
                self.type_combo.setCurrentIndex(index)

            self.description_edit.setPlainText(self.account.description or '')

    def save_account(self):
        """Save account data."""
        if not self.code_edit.text().strip():
            QMessageBox.warning(self, _('Warning'), _('Account code is required.'))
            return

        if not self.name_edit.text().strip():
            QMessageBox.warning(self, _('Warning'), _('Account name is required.'))
            return

        session = SessionLocal()
        try:
            if self.account:
                account = session.merge(self.account)
            else:
                existing = session.query(Account).filter(Account.code == self.code_edit.text().strip()).first()
                if existing:
                    QMessageBox.warning(self, _('Warning'), _('Account code already exists.'))
                    return

                account = Account()
                account.code = self.code_edit.text().strip()

            account.name = self.name_edit.text().strip()
            account.account_type = self.type_combo.currentData()
            account.description = self.description_edit.toPlainText().strip() or None

            if not self.account:
                session.add(account)

            session.commit()
            self.accept()

        except Exception as e:
            session.rollback()
            QMessageBox.critical(self, _('Error'), f"Failed to save account: {str(e)}")
        finally:
            session.close()
