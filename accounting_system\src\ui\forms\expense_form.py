"""
نموذج إدارة المصروفات المحسن
Enhanced Expense Management Form
"""
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
    QLabel, QLineEdit, QTextEdit, QFormLayout,
    QTableWidget, QTableWidgetItem, QMessageBox,
    QDateEdit, QDoubleSpinBox, QComboBox, QSpinBox,
    QHeaderView, QAbstractItemView, QDialog, QDialogButtonBox,
    QGroupBox, QGridLayout, QTabWidget, QCheckBox
)
from PyQt6.QtCore import Qt, QDate
from PyQt6.QtGui import QFont
from src.database.database import SessionLocal
from src.models.accounting import Expense, ExpenseCategory
from src.models.hr import Employee
from src.utils.translation import _
from datetime import datetime

class ExpenseForm(QWidget):
    """Enhanced Expense form class."""

    def __init__(self):
        super().__init__()
        self.current_expense = None
        self._init_ui()
        self._load_data()

    def _init_ui(self):
        """Initialize the enhanced UI."""
        layout = QVBoxLayout()
        self.setLayout(layout)

        # Title
        title = QLabel(_('Expense Management'))
        title.setFont(QFont('Arial', 16, QFont.Weight.Bold))
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title)

        # Search and filter section
        search_group = QGroupBox(_('Search & Filter'))
        search_layout = QGridLayout()
        search_group.setLayout(search_layout)

        # Search box
        search_layout.addWidget(QLabel(_('Search') + ':'), 0, 0)
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText(_('Search by expense number or payee...'))
        self.search_input.textChanged.connect(self._filter_expenses)
        search_layout.addWidget(self.search_input, 0, 1)

        # Date filter
        search_layout.addWidget(QLabel(_('From Date') + ':'), 0, 2)
        self.from_date = QDateEdit()
        self.from_date.setCalendarPopup(True)
        self.from_date.setDate(QDate.currentDate().addDays(-30))
        search_layout.addWidget(self.from_date, 0, 3)

        search_layout.addWidget(QLabel(_('To Date') + ':'), 0, 4)
        self.to_date = QDateEdit()
        self.to_date.setCalendarPopup(True)
        self.to_date.setDate(QDate.currentDate())
        search_layout.addWidget(self.to_date, 0, 5)

        # Category filter
        search_layout.addWidget(QLabel(_('Category') + ':'), 1, 0)
        self.category_filter = QComboBox()
        self.category_filter.currentTextChanged.connect(self._filter_expenses)
        search_layout.addWidget(self.category_filter, 1, 1)

        layout.addWidget(search_group)

        # Buttons section
        buttons_layout = QHBoxLayout()

        self.new_btn = QPushButton(_('New Expense'))
        self.new_btn.clicked.connect(self._new_expense)
        buttons_layout.addWidget(self.new_btn)

        self.edit_btn = QPushButton(_('Edit Expense'))
        self.edit_btn.clicked.connect(self._edit_expense)
        self.edit_btn.setEnabled(False)
        buttons_layout.addWidget(self.edit_btn)

        self.delete_btn = QPushButton(_('Delete Expense'))
        self.delete_btn.clicked.connect(self._delete_expense)
        self.delete_btn.setEnabled(False)
        buttons_layout.addWidget(self.delete_btn)

        self.refresh_btn = QPushButton(_('Refresh'))
        self.refresh_btn.clicked.connect(self._load_data)
        buttons_layout.addWidget(self.refresh_btn)

        buttons_layout.addStretch()
        layout.addLayout(buttons_layout)

        # Expenses table
        self.table = QTableWidget()
        self.table.setColumnCount(8)
        self.table.setHorizontalHeaderLabels([
            _('Expense Number'), _('Date'), _('Category'), _('Payee'),
            _('Amount'), _('Tax'), _('Total'), _('Status')
        ])

        # Configure table
        header = self.table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        self.table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.table.setAlternatingRowColors(True)
        self.table.itemSelectionChanged.connect(self._on_selection_changed)
        self.table.itemDoubleClicked.connect(self._edit_expense)

        layout.addWidget(self.table)

    def _on_selection_changed(self):
        """Handle selection change."""
        has_selection = len(self.table.selectedItems()) > 0
        self.edit_btn.setEnabled(has_selection)
        self.delete_btn.setEnabled(has_selection)

    def _load_data(self):
        """Load expenses data."""
        self._load_categories_filter()
        self._load_expenses()

    def _load_categories_filter(self):
        """Load categories for filter."""
        session = SessionLocal()
        try:
            self.category_filter.clear()
            self.category_filter.addItem(_('All Categories'))

            categories = session.query(ExpenseCategory).filter(ExpenseCategory.is_active == True).all()
            for category in categories:
                self.category_filter.addItem(category.name)
        finally:
            session.close()

    def _load_expenses(self):
        """Load expenses into table."""
        session = SessionLocal()
        try:
            expenses = session.query(Expense).order_by(Expense.expense_date.desc()).all()
            self.table.setRowCount(len(expenses))

            for i, expense in enumerate(expenses):
                # Expense Number
                self.table.setItem(i, 0, QTableWidgetItem(expense.expense_number))

                # Date
                date_str = expense.expense_date.strftime('%Y-%m-%d') if expense.expense_date else ''
                self.table.setItem(i, 1, QTableWidgetItem(date_str))

                # Category
                category_name = expense.category.name if expense.category else _('No Category')
                self.table.setItem(i, 2, QTableWidgetItem(category_name))

                # Payee
                self.table.setItem(i, 3, QTableWidgetItem(expense.payee or ''))

                # Amount
                self.table.setItem(i, 4, QTableWidgetItem(f"{expense.amount:.2f}"))

                # Tax
                self.table.setItem(i, 5, QTableWidgetItem(f"{expense.tax_amount:.2f}"))

                # Total
                total = expense.amount + expense.tax_amount
                self.table.setItem(i, 6, QTableWidgetItem(f"{total:.2f}"))

                # Status
                status_text = _('Approved') if expense.is_approved else _('Pending')
                self.table.setItem(i, 7, QTableWidgetItem(status_text))

                # Store expense ID in first column
                self.table.item(i, 0).setData(Qt.ItemDataRole.UserRole, expense.id)

        finally:
            session.close()

    def _filter_expenses(self):
        """Filter expenses based on search criteria."""
        search_text = self.search_input.text().lower()
        category_filter = self.category_filter.currentText()

        for row in range(self.table.rowCount()):
            show_row = True

            # Search filter
            if search_text:
                expense_no = self.table.item(row, 0).text().lower()
                payee = self.table.item(row, 3).text().lower()
                if search_text not in expense_no and search_text not in payee:
                    show_row = False

            # Category filter
            if category_filter != _('All Categories'):
                category = self.table.item(row, 2).text()
                if category != category_filter:
                    show_row = False

            self.table.setRowHidden(row, not show_row)

    def _new_expense(self):
        """Create new expense."""
        dialog = ExpenseDialog(parent=self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            if dialog.validate_data():
                self._save_expense_data(dialog.get_expense_data())

    def _edit_expense(self):
        """Edit selected expense."""
        current_row = self.table.currentRow()
        if current_row < 0:
            return

        expense_id = self.table.item(current_row, 0).data(Qt.ItemDataRole.UserRole)

        session = SessionLocal()
        try:
            expense = session.query(Expense).get(expense_id)
            if expense:
                dialog = ExpenseDialog(expense, parent=self)
                if dialog.exec() == QDialog.DialogCode.Accepted:
                    if dialog.validate_data():
                        self._update_expense_data(expense, dialog.get_expense_data())
        finally:
            session.close()

    def _delete_expense(self):
        """Delete selected expense."""
        current_row = self.table.currentRow()
        if current_row < 0:
            return

        expense_number = self.table.item(current_row, 0).text()

        reply = QMessageBox.question(
            self, _('Confirm Delete'),
            _('Are you sure you want to delete expense: ') + expense_number + '?',
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            expense_id = self.table.item(current_row, 0).data(Qt.ItemDataRole.UserRole)

            session = SessionLocal()
            try:
                expense = session.query(Expense).get(expense_id)
                if expense:
                    session.delete(expense)
                    session.commit()
                    QMessageBox.information(self, _('Success'), _('Expense deleted successfully'))
                    self._load_data()
            except Exception as e:
                session.rollback()
                QMessageBox.critical(self, _('Error'), _('Failed to delete expense: ') + str(e))
            finally:
                session.close()

    def _save_expense_data(self, data):
        """Save new expense."""
        session = SessionLocal()
        try:
            # Generate expense number if not provided
            if not data['expense_number']:
                last_expense = session.query(Expense).order_by(Expense.id.desc()).first()
                if last_expense:
                    last_num = int(last_expense.expense_number.split('-')[-1]) if '-' in last_expense.expense_number else 0
                    data['expense_number'] = f"EXP-{last_num + 1:06d}"
                else:
                    data['expense_number'] = "EXP-000001"

            expense = Expense(
                expense_number=data['expense_number'],
                expense_date=data['expense_date'],
                category_id=data['category_id'],
                payee=data['payee'],
                amount=data['amount'],
                tax_amount=data['tax_amount'],
                description=data['description'],
                employee_id=data['employee_id'],
                is_approved=data['is_approved']
            )

            session.add(expense)
            session.commit()
            QMessageBox.information(self, _('Success'), _('Expense saved successfully'))
            self._load_data()

        except Exception as e:
            session.rollback()
            QMessageBox.critical(self, _('Error'), _('Failed to save expense: ') + str(e))
        finally:
            session.close()

    def _update_expense_data(self, expense, data):
        """Update existing expense."""
        session = SessionLocal()
        try:
            # Update expense fields
            expense.expense_number = data['expense_number']
            expense.expense_date = data['expense_date']
            expense.category_id = data['category_id']
            expense.payee = data['payee']
            expense.amount = data['amount']
            expense.tax_amount = data['tax_amount']
            expense.description = data['description']
            expense.employee_id = data['employee_id']
            expense.is_approved = data['is_approved']

            session.commit()
            QMessageBox.information(self, _('Success'), _('Expense updated successfully'))
            self._load_data()

        except Exception as e:
            session.rollback()
            QMessageBox.critical(self, _('Error'), _('Failed to update expense: ') + str(e))
        finally:
            session.close()


class ExpenseDialog(QDialog):
    """Dialog for adding/editing expenses."""

    def __init__(self, expense=None, parent=None):
        super().__init__(parent)
        self.expense = expense
        self.setWindowTitle(_('New Expense') if expense is None else _('Edit Expense'))
        self.setModal(True)
        self.setMinimumSize(500, 600)

        self.setup_ui()
        if expense:
            self.load_expense_data()
        else:
            self.generate_expense_number()

    def setup_ui(self):
        """Setup the dialog UI."""
        layout = QVBoxLayout()

        # Form layout
        form_layout = QFormLayout()

        # Expense number
        self.expense_number_edit = QLineEdit()
        self.expense_number_edit.setReadOnly(True)
        form_layout.addRow(_('Expense Number') + ':', self.expense_number_edit)

        # Date
        self.date_edit = QDateEdit()
        self.date_edit.setCalendarPopup(True)
        self.date_edit.setDate(QDate.currentDate())
        form_layout.addRow(_('Date') + '*:', self.date_edit)

        # Category
        self.category_combo = QComboBox()
        self.load_categories()
        form_layout.addRow(_('Category') + '*:', self.category_combo)

        # Employee
        self.employee_combo = QComboBox()
        self.load_employees()
        form_layout.addRow(_('Employee') + ':', self.employee_combo)

        # Payee
        self.payee_edit = QLineEdit()
        self.payee_edit.setMaxLength(100)
        form_layout.addRow(_('Payee') + '*:', self.payee_edit)

        # Amount
        self.amount_spin = QDoubleSpinBox()
        self.amount_spin.setMaximum(999999.99)
        self.amount_spin.setDecimals(2)
        form_layout.addRow(_('Amount') + '*:', self.amount_spin)

        # Tax amount
        self.tax_amount_spin = QDoubleSpinBox()
        self.tax_amount_spin.setMaximum(999999.99)
        self.tax_amount_spin.setDecimals(2)
        form_layout.addRow(_('Tax Amount') + ':', self.tax_amount_spin)

        # Description
        self.description_edit = QTextEdit()
        self.description_edit.setMaximumHeight(100)
        form_layout.addRow(_('Description') + ':', self.description_edit)

        # Is approved
        self.is_approved_check = QCheckBox()
        form_layout.addRow(_('Approved') + ':', self.is_approved_check)

        layout.addLayout(form_layout)

        # Buttons
        button_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok |
                                     QDialogButtonBox.StandardButton.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)

        self.setLayout(layout)

    def load_categories(self):
        """Load expense categories."""
        session = SessionLocal()
        try:
            categories = session.query(ExpenseCategory).filter(ExpenseCategory.is_active == True).all()
            self.category_combo.addItem(_('Select Category'), None)
            for category in categories:
                self.category_combo.addItem(category.name, category.id)
        finally:
            session.close()

    def load_employees(self):
        """Load employees."""
        session = SessionLocal()
        try:
            employees = session.query(Employee).filter(Employee.is_active == True).all()
            self.employee_combo.addItem(_('No Employee'), None)
            for employee in employees:
                full_name = f"{employee.first_name} {employee.last_name}"
                self.employee_combo.addItem(full_name, employee.id)
        finally:
            session.close()

    def generate_expense_number(self):
        """Generate new expense number."""
        session = SessionLocal()
        try:
            last_expense = session.query(Expense).order_by(Expense.id.desc()).first()
            if last_expense:
                last_num = int(last_expense.expense_number.split('-')[-1]) if '-' in last_expense.expense_number else 0
                new_number = f"EXP-{last_num + 1:06d}"
            else:
                new_number = "EXP-000001"
            self.expense_number_edit.setText(new_number)
        finally:
            session.close()

    def load_expense_data(self):
        """Load expense data for editing."""
        if not self.expense:
            return

        self.expense_number_edit.setText(self.expense.expense_number)
        self.date_edit.setDate(QDate.fromString(self.expense.expense_date.strftime('%Y-%m-%d'), 'yyyy-MM-dd'))
        self.payee_edit.setText(self.expense.payee or '')
        self.amount_spin.setValue(self.expense.amount)
        self.tax_amount_spin.setValue(self.expense.tax_amount)
        self.description_edit.setPlainText(self.expense.description or '')
        self.is_approved_check.setChecked(self.expense.is_approved)

        # Set category
        if self.expense.category_id:
            for i in range(self.category_combo.count()):
                if self.category_combo.itemData(i) == self.expense.category_id:
                    self.category_combo.setCurrentIndex(i)
                    break

        # Set employee
        if self.expense.employee_id:
            for i in range(self.employee_combo.count()):
                if self.employee_combo.itemData(i) == self.expense.employee_id:
                    self.employee_combo.setCurrentIndex(i)
                    break

    def get_expense_data(self):
        """Get expense data from form."""
        return {
            'expense_number': self.expense_number_edit.text().strip(),
            'expense_date': self.date_edit.date().toPython(),
            'category_id': self.category_combo.currentData(),
            'employee_id': self.employee_combo.currentData(),
            'payee': self.payee_edit.text().strip(),
            'amount': self.amount_spin.value(),
            'tax_amount': self.tax_amount_spin.value(),
            'description': self.description_edit.toPlainText().strip(),
            'is_approved': self.is_approved_check.isChecked()
        }

    def validate_data(self):
        """Validate form data."""
        data = self.get_expense_data()

        if not data['category_id']:
            QMessageBox.warning(self, _('Validation Error'), _('Please select a category'))
            return False

        if not data['payee']:
            QMessageBox.warning(self, _('Validation Error'), _('Payee is required'))
            return False

        if data['amount'] <= 0:
            QMessageBox.warning(self, _('Validation Error'), _('Amount must be greater than zero'))
            return False

        return True
