"""
Expense management form.
"""
from PyQt6.QtWidgets import (QWidget, QLabel, QVBoxLayout, QHBoxLayout,
                          QLineEdit, QDateEdit, QComboBox, QSpinBox,
                          QDoubleSpinBox, QTextEdit, QPushButton,
                          QMessageBox, QTableWidget, QTableWidgetItem)
from PyQt6.QtCore import Qt, QDate
from sqlalchemy.exc import SQLAlchemyError
from src.models.accounting import Expense, ExpenseCategory, JournalEntry, JournalEntryLine
from src.database.database import Session
from datetime import datetime, date

class ExpenseForm(QWidget):
    """Expense form class."""
    
    def __init__(self):
        super().__init__()
        self.session = Session()
        self._init_ui()
        self._load_categories()
        self._load_expenses()
    
    def _init_ui(self):
        """Initialize the UI."""
        main_layout = QVBoxLayout()
        self.setLayout(main_layout)
        
        # Form Layout
        form_layout = QVBoxLayout()
        
        # Expense Number
        number_layout = QHBoxLayout()
        number_label = QLabel('رقم المصروف:')
        self.number_edit = QLineEdit()
        self.number_edit.setPlaceholderText('EXP-001')
        number_layout.addWidget(number_label)
        number_layout.addWidget(self.number_edit)
        form_layout.addLayout(number_layout)
        
        # Date
        date_layout = QHBoxLayout()
        date_label = QLabel('التاريخ:')
        self.date_edit = QDateEdit()
        self.date_edit.setCalendarPopup(True)
        self.date_edit.setDate(QDate.currentDate())
        date_layout.addWidget(date_label)
        date_layout.addWidget(self.date_edit)
        form_layout.addLayout(date_layout)
        
        # Category
        category_layout = QHBoxLayout()
        category_label = QLabel('الفئة:')
        self.category_combo = QComboBox()
        category_layout.addWidget(category_label)
        category_layout.addWidget(self.category_combo)
        form_layout.addLayout(category_layout)
        
        # Payee
        payee_layout = QHBoxLayout()
        payee_label = QLabel('المستفيد:')
        self.payee_edit = QLineEdit()
        payee_layout.addWidget(payee_label)
        payee_layout.addWidget(self.payee_edit)
        form_layout.addLayout(payee_layout)
        
        # Amount
        amount_layout = QHBoxLayout()
        amount_label = QLabel('المبلغ:')
        self.amount_spin = QDoubleSpinBox()
        self.amount_spin.setMaximum(*********.99)
        self.amount_spin.setDecimals(2)
        amount_layout.addWidget(amount_label)
        amount_layout.addWidget(self.amount_spin)
        form_layout.addLayout(amount_layout)
        
        # Tax Amount
        tax_layout = QHBoxLayout()
        tax_label = QLabel('مبلغ الضريبة:')
        self.tax_spin = QDoubleSpinBox()
        self.tax_spin.setMaximum(*********.99)
        self.tax_spin.setDecimals(2)
        tax_layout.addWidget(tax_label)
        tax_layout.addWidget(self.tax_spin)
        form_layout.addLayout(tax_layout)
        
        # Description
        desc_label = QLabel('الوصف:')
        self.desc_edit = QTextEdit()
        form_layout.addWidget(desc_label)
        form_layout.addWidget(self.desc_edit)
        
        # Buttons
        btn_layout = QHBoxLayout()
        self.save_btn = QPushButton('حفظ')
        self.clear_btn = QPushButton('مسح')
        self.delete_btn = QPushButton('حذف')
        self.delete_btn.setEnabled(False)
        
        self.save_btn.clicked.connect(self._save_expense)
        self.clear_btn.clicked.connect(self._clear_form)
        self.delete_btn.clicked.connect(self._delete_expense)
        
        btn_layout.addWidget(self.save_btn)
        btn_layout.addWidget(self.clear_btn)
        btn_layout.addWidget(self.delete_btn)
        form_layout.addLayout(btn_layout)
        
        # Expenses Table
        self.expenses_table = QTableWidget()
        self.expenses_table.setColumnCount(7)
        self.expenses_table.setHorizontalHeaderLabels([
            'الرقم', 'التاريخ', 'الفئة', 'المستفيد', 'المبلغ', 'الضريبة', 'الوصف'
        ])
        self.expenses_table.itemClicked.connect(self._load_expense)
        
        main_layout.addLayout(form_layout)
        main_layout.addWidget(self.expenses_table)
    
    def _load_categories(self):
        """Load expense categories into combo box."""
        try:
            categories = self.session.query(ExpenseCategory).filter_by(is_active=True).all()
            self.category_combo.clear()
            for category in categories:
                self.category_combo.addItem(category.name, category.id)
        except SQLAlchemyError as e:
            QMessageBox.critical(self, 'خطأ', f'خطأ في تحميل فئات المصروفات: {str(e)}')
    
    def _load_expenses(self):
        """Load expenses into table."""
        try:
            expenses = self.session.query(Expense).order_by(Expense.date.desc()).all()
            self.expenses_table.setRowCount(len(expenses))
            for i, expense in enumerate(expenses):
                self.expenses_table.setItem(i, 0, QTableWidgetItem(expense.number))
                self.expenses_table.setItem(i, 1, QTableWidgetItem(expense.date.strftime('%Y-%m-%d')))
                self.expenses_table.setItem(i, 2, QTableWidgetItem(expense.category.name if expense.category else ''))
                self.expenses_table.setItem(i, 3, QTableWidgetItem(expense.payee))
                self.expenses_table.setItem(i, 4, QTableWidgetItem(f'{expense.amount:.2f}'))
                self.expenses_table.setItem(i, 5, QTableWidgetItem(f'{expense.tax_amount:.2f}'))
                self.expenses_table.setItem(i, 6, QTableWidgetItem(expense.description))
        except SQLAlchemyError as e:
            QMessageBox.critical(self, 'خطأ', f'خطأ في تحميل المصروفات: {str(e)}')
    
    def _save_expense(self):
        """Save expense and create journal entry."""
        try:
            # Validate inputs
            number = self.number_edit.text().strip()
            if not number:
                QMessageBox.warning(self, 'تنبيه', 'الرجاء إدخال رقم المصروف')
                return
            
            # Create or update expense
            expense_date = self.date_edit.date().toPyDate()
            category_id = self.category_combo.currentData()
            amount = self.amount_spin.value()
            tax_amount = self.tax_spin.value()
            
            expense = Expense(
                number=number,
                date=expense_date,
                expense_category_id=category_id,
                payee=self.payee_edit.text().strip(),
                amount=amount,
                tax_amount=tax_amount,
                description=self.desc_edit.toPlainText().strip()
            )
            
            self.session.add(expense)
            self.session.flush()  # Get the expense ID
            
            # Create journal entry
            journal_entry = JournalEntry(
                number=f'JE-EXP-{expense.id}',
                date=expense_date,
                reference_type='expense',
                reference_id=expense.id,
                description=f'مصروف: {expense.payee}'
            )
            
            self.session.add(journal_entry)
            self.session.flush()  # Get the journal entry ID
            
            # Create journal entry lines
            lines = []
            # Debit expense account
            lines.append(JournalEntryLine(
                journal_entry_id=journal_entry.id,
                account_id=expense.category.account_id,
                debit=amount,
                credit=0,
                description=f'مصروف - {expense.category.name}'
            ))
            
            # Credit cash/bank account (assuming ID 1 is cash account)
            lines.append(JournalEntryLine(
                journal_entry_id=journal_entry.id,
                account_id=1,  # Cash account ID
                debit=0,
                credit=amount + tax_amount,
                description='دفع مصروف'
            ))
            
            # If there's tax, debit tax account (assuming ID 2 is tax account)
            if tax_amount > 0:
                lines.append(JournalEntryLine(
                    journal_entry_id=journal_entry.id,
                    account_id=2,  # Tax account ID
                    debit=tax_amount,
                    credit=0,
                    description='ضريبة مصروف'
                ))
            
            self.session.bulk_save_objects(lines)
            self.session.commit()
            
            QMessageBox.information(self, 'تم', 'تم حفظ المصروف وإنشاء القيود المحاسبية بنجاح')
            self._clear_form()
            self._load_expenses()
            
        except SQLAlchemyError as e:
            self.session.rollback()
            QMessageBox.critical(self, 'خطأ', f'خطأ في حفظ المصروف: {str(e)}')
    
    def _load_expense(self, item):
        """Load selected expense into form."""
        row = item.row()
        self.number_edit.setText(self.expenses_table.item(row, 0).text())
        self.date_edit.setDate(QDate.fromString(self.expenses_table.item(row, 1).text(), 'yyyy-MM-dd'))
        category_name = self.expenses_table.item(row, 2).text()
        category_index = self.category_combo.findText(category_name)
        if category_index >= 0:
            self.category_combo.setCurrentIndex(category_index)
        self.payee_edit.setText(self.expenses_table.item(row, 3).text())
        self.amount_spin.setValue(float(self.expenses_table.item(row, 4).text()))
        self.tax_spin.setValue(float(self.expenses_table.item(row, 5).text()))
        self.desc_edit.setPlainText(self.expenses_table.item(row, 6).text())
        self.delete_btn.setEnabled(True)
    
    def _clear_form(self):
        """Clear form fields."""
        self.number_edit.clear()
        self.date_edit.setDate(QDate.currentDate())
        self.category_combo.setCurrentIndex(0)
        self.payee_edit.clear()
        self.amount_spin.setValue(0)
        self.tax_spin.setValue(0)
        self.desc_edit.clear()
        self.delete_btn.setEnabled(False)
    
    def _delete_expense(self):
        """Delete selected expense and its journal entries."""
        try:
            number = self.number_edit.text().strip()
            if not number:
                return
            
            reply = QMessageBox.question(
                self, 'تأكيد الحذف',
                'هل أنت متأكد من حذف هذا المصروف وقيوده المحاسبية؟',
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )
            
            if reply == QMessageBox.StandardButton.Yes:
                expense = self.session.query(Expense).filter_by(number=number).first()
                if expense:
                    # Delete associated journal entries
                    journal_entries = self.session.query(JournalEntry).filter_by(
                        reference_type='expense',
                        reference_id=expense.id
                    ).all()
                    
                    for entry in journal_entries:
                        self.session.query(JournalEntryLine).filter_by(
                            journal_entry_id=entry.id
                        ).delete()
                        self.session.delete(entry)
                    
                    self.session.delete(expense)
                    self.session.commit()
                    
                    QMessageBox.information(self, 'تم', 'تم حذف المصروف وقيوده المحاسبية بنجاح')
                    self._clear_form()
                    self._load_expenses()
        
        except SQLAlchemyError as e:
            self.session.rollback()
            QMessageBox.critical(self, 'خطأ', f'خطأ في حذف المصروف: {str(e)}')
    
    def closeEvent(self, event):
        """Clean up database session before closing."""
        self.session.close()
        super().closeEvent(event)
