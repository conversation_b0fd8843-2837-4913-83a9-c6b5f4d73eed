"""
Main entry point for the accounting system.
"""
import sys
from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import QTranslator, QLocale
from src.ui.login_window import <PERSON>ginWindow
from src.ui.main_window import <PERSON>Window
from src.database.database import init_db, SessionLocal
from src.config import DEFAULT_LANGUAGE
from src.models.auth import User, Role, Permission, PermissionType, Module
from src.utils.translation import setup_translation

def initialize_permissions():
    """Initialize default permissions and admin role."""
    session = SessionLocal()
    try:
        # Create admin role if it doesn't exist
        admin_role = session.query(Role).filter_by(name='admin').first()
        if not admin_role:
            admin_role = Role(name='admin', description='Administrator role with all permissions')
            session.add(admin_role)
            
        # Create default permissions for all modules
        for module in Module:
            for perm_type in PermissionType:
                perm = session.query(Permission).filter_by(
                    module=module.name,
                    permission_type=perm_type.name
                ).first()
                
                if not perm:
                    perm = Permission(
                        module=module.name,
                        permission_type=perm_type.name,
                        description=f'{perm_type.name} permission for {module.name}'
                    )
                    session.add(perm)
                    if admin_role:
                        admin_role.permissions.append(perm)
        
        # Create admin user if it doesn't exist
        admin_user = session.query(User).filter_by(username='admin').first()
        if not admin_user:
            admin_user = User(
                username='admin',
                password='admin123',  # in production, this should be hashed
                role=admin_role
            )
            session.add(admin_user)
        
        session.commit()
    except Exception as e:
        session.rollback()
        print(f"Error initializing permissions: {e}")
    finally:
        session.close()

def main():
    """Main entry point."""
    # Create the application
    app = QApplication(sys.argv)
    
    # Set up translation
    translation = setup_translation(DEFAULT_LANGUAGE)
    
    # Initialize database
    init_db()
    
    # Initialize permissions and admin user
    initialize_permissions()
    
    # Start with login window
    login = LoginWindow()
    login.show()
    
    # Execute the application
    sys.exit(app.exec())

if __name__ == '__main__':
    main()
