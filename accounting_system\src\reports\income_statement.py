"""
Income statement report generation.
"""
from datetime import datetime
from reportlab.lib import colors
from reportlab.lib.units import inch
from reportlab.platypus import Paragraph, Spacer
from src.database.database import SessionLocal
from src.models.accounting import Account, Transaction
from src.utils.translation import _
from .base_report import BaseReport

class IncomeStatementReport(BaseReport):
    """Income statement report generator."""
    
    def get_report_name(self) -> str:
        return _("Income Statement")
    
    def get_content(self) -> list:
        session = SessionLocal()
        story = []
        try:
            # Add report description
            story.append(Spacer(1, 12))
            story.append(Paragraph(_("Statement of Profit and Loss"), self.styles['Heading1']))
            story.append(Spacer(1, 12))
            
            # Get revenues
            revenue_data = [
                [_("Revenues"), _("Amount")],
            ]
            revenues = session.query(Account).filter(Account.type == 'revenue').all()
            total_revenue = 0
            for revenue in revenues:
                balance = sum(t.amount for t in revenue.transactions)
                total_revenue += balance
                revenue_data.append([revenue.name, f"{balance:,.2f}"])
            revenue_data.append([_("Total Revenue"), f"{total_revenue:,.2f}"])
            
            # Create revenue table
            revenue_table = self.create_table(revenue_data)
            story.append(revenue_table)
            story.append(Spacer(1, 20))
            
            # Get expenses
            expense_data = [
                [_("Expenses"), _("Amount")],
            ]
            expenses = session.query(Account).filter(Account.type == 'expense').all()
            total_expenses = 0
            for expense in expenses:
                balance = sum(t.amount for t in expense.transactions)
                total_expenses += balance
                expense_data.append([expense.name, f"{balance:,.2f}"])
            expense_data.append([_("Total Expenses"), f"{total_expenses:,.2f}"])
            
            # Create expenses table
            expense_table = self.create_table(expense_data)
            story.append(expense_table)
            story.append(Spacer(1, 20))
            
            # Add net income
            net_income = total_revenue - total_expenses
            net_income_data = [
                [_("Net Income"), f"{net_income:,.2f}"],
            ]
            net_income_table = self.create_table(net_income_data)
            story.append(net_income_table)
            
        finally:
            session.close()
            
        return story
