#!/usr/bin/env python3
"""
اختبار النظام - System Test
"""
import sys
import os
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_imports():
    """Test all imports."""
    print("🔍 اختبار الاستيرادات / Testing imports...")
    
    try:
        # Test database
        from src.database.database import engine, SessionLocal, init_db
        print("✅ Database imports")
        
        # Test models
        from src.models.accounting import Account, JournalEntry, JournalEntryLine
        from src.models.customers import Customer
        from src.models.suppliers import Supplier
        from src.models.inventory import Product, InventoryTransaction
        from src.models.hr import Employee, Salary
        from src.models.auth import User, Role
        print("✅ Model imports")
        
        # Test UI forms
        from src.ui.forms.customer_form import CustomerForm
        from src.ui.forms.supplier_form import SupplierForm
        from src.ui.forms.product_form import ProductForm
        from src.ui.forms.inventory_form import InventoryForm
        from src.ui.forms.accounts_form import AccountsForm
        from src.ui.forms.employee_form import EmployeeForm
        from src.ui.forms.expense_form import ExpenseForm
        from src.ui.forms.salary_form import SalaryForm
        from src.ui.forms.invoice_form import InvoiceForm
        from src.ui.forms.purchase_form import PurchaseForm
        from src.ui.forms.user_management_form import UserManagementForm
        print("✅ UI Form imports")
        
        # Test reports
        from src.reports.balance_sheet import BalanceSheetReport
        from src.reports.income_statement import IncomeStatementReport
        from src.reports.inventory_report import InventoryReport
        print("✅ Report imports")
        
        # Test utilities
        from src.utils.translations import _
        from src.utils.backup import create_backup, restore_backup
        print("✅ Utility imports")
        
        return True
        
    except Exception as e:
        print(f"❌ Import error: {e}")
        return False

def test_database():
    """Test database connection."""
    print("\n🔍 اختبار قاعدة البيانات / Testing database...")
    
    try:
        from src.database.database import engine, SessionLocal, init_db
        
        # Initialize database
        init_db()
        print("✅ Database initialization")
        
        # Test session
        session = SessionLocal()
        session.close()
        print("✅ Database session")
        
        return True
        
    except Exception as e:
        print(f"❌ Database error: {e}")
        return False

def test_models():
    """Test model creation."""
    print("\n🔍 اختبار النماذج / Testing models...")
    
    try:
        from src.database.database import SessionLocal
        from src.models.customers import Customer
        from src.models.suppliers import Supplier
        from src.models.inventory import Product
        from src.models.accounting import Account
        
        session = SessionLocal()
        
        # Test creating models (without saving)
        customer = Customer(
            code="TEST001",
            name="Test Customer",
            email="<EMAIL>"
        )
        
        supplier = Supplier(
            code="SUP001", 
            name="Test Supplier",
            email="<EMAIL>"
        )
        
        product = Product(
            code="PROD001",
            name="Test Product",
            unit_price=100.0,
            quantity=10,
            minimum_quantity=5
        )
        
        account = Account(
            code="1001",
            name="Test Account",
            account_type="ASSET"
        )
        
        session.close()
        print("✅ Model creation")
        
        return True
        
    except Exception as e:
        print(f"❌ Model error: {e}")
        return False

def test_ui_components():
    """Test UI components without showing them."""
    print("\n🔍 اختبار مكونات الواجهة / Testing UI components...")
    
    try:
        from PyQt6.QtWidgets import QApplication
        from src.ui.forms.customer_form import CustomerForm
        from src.ui.forms.inventory_form import InventoryForm
        from src.ui.forms.accounts_form import AccountsForm
        
        # Create QApplication if it doesn't exist
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # Test creating forms (without showing)
        customer_form = CustomerForm()
        inventory_form = InventoryForm()
        accounts_form = AccountsForm()
        
        print("✅ UI component creation")
        
        return True
        
    except Exception as e:
        print(f"❌ UI error: {e}")
        return False

def test_reports():
    """Test report generation."""
    print("\n🔍 اختبار التقارير / Testing reports...")
    
    try:
        from src.reports.balance_sheet import BalanceSheetReport
        from src.reports.income_statement import IncomeStatementReport
        from src.reports.inventory_report import InventoryReport
        
        # Test creating report objects (without generating)
        balance_sheet = BalanceSheetReport()
        income_statement = IncomeStatementReport()
        inventory_report = InventoryReport()
        
        print("✅ Report object creation")
        
        return True
        
    except Exception as e:
        print(f"❌ Report error: {e}")
        return False

def main():
    """Run all tests."""
    print("=" * 60)
    print("🧪 اختبار نظام المحاسبة المتكامل / Testing Accounting System")
    print("=" * 60)
    
    tests = [
        ("الاستيرادات / Imports", test_imports),
        ("قاعدة البيانات / Database", test_database),
        ("النماذج / Models", test_models),
        ("الواجهة / UI Components", test_ui_components),
        ("التقارير / Reports", test_reports),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 40)
        
        if test_func():
            passed += 1
            print(f"✅ {test_name} - نجح / PASSED")
        else:
            print(f"❌ {test_name} - فشل / FAILED")
    
    print("\n" + "=" * 60)
    print(f"📊 النتائج / Results: {passed}/{total} اختبارات نجحت / tests passed")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت! النظام جاهز للاستخدام")
        print("🎉 All tests passed! System is ready to use")
        return 0
    else:
        print("⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء")
        print("⚠️ Some tests failed. Please review the errors")
        return 1

if __name__ == '__main__':
    sys.exit(main())
