"""
Balance sheet report generation.
"""
from datetime import datetime
from reportlab.lib import colors
from reportlab.lib.units import inch
from reportlab.platypus import Paragraph, Spacer
from src.database.database import SessionLocal
from src.models.accounting import Account, Transaction
from src.utils.translation import _
from .base_report import BaseReport

class BalanceSheetReport(BaseReport):
    """Balance sheet report generator."""
    
    def get_report_name(self) -> str:
        return _("Balance Sheet")
    
    def get_content(self) -> list:
        session = SessionLocal()
        story = []
        try:
            # Add report description
            story.append(Spacer(1, 12))
            story.append(Paragraph(_("Statement of Financial Position"), self.styles['Heading1']))
            story.append(Spacer(1, 12))
            
            # Get assets
            assets_data = [
                [_("Assets"), _("Amount")],
            ]
            assets = session.query(Account).filter(Account.type == 'asset').all()
            total_assets = 0
            for asset in assets:
                balance = sum(t.amount for t in asset.transactions)
                total_assets += balance
                assets_data.append([asset.name, f"{balance:,.2f}"])
            assets_data.append([_("Total Assets"), f"{total_assets:,.2f}"])
            
            # Create assets table
            assets_table = self.create_table(assets_data)
            story.append(assets_table)
            story.append(Spacer(1, 20))
            
            # Get liabilities
            liabilities_data = [
                [_("Liabilities"), _("Amount")],
            ]
            liabilities = session.query(Account).filter(Account.type == 'liability').all()
            total_liabilities = 0
            for liability in liabilities:
                balance = sum(t.amount for t in liability.transactions)
                total_liabilities += balance
                liabilities_data.append([liability.name, f"{balance:,.2f}"])
            liabilities_data.append([_("Total Liabilities"), f"{total_liabilities:,.2f}"])
            
            # Create liabilities table
            liabilities_table = self.create_table(liabilities_data)
            story.append(liabilities_table)
            story.append(Spacer(1, 20))
            
            # Get equity
            equity_data = [
                [_("Equity"), _("Amount")],
            ]
            equity = session.query(Account).filter(Account.type == 'equity').all()
            total_equity = 0
            for eq in equity:
                balance = sum(t.amount for t in eq.transactions)
                total_equity += balance
                equity_data.append([eq.name, f"{balance:,.2f}"])
            equity_data.append([_("Total Equity"), f"{total_equity:,.2f}"])
            
            # Create equity table
            equity_table = self.create_table(equity_data)
            story.append(equity_table)
            story.append(Spacer(1, 20))
            
            # Add total liabilities and equity
            total_data = [
                [_("Total Liabilities and Equity"), f"{(total_liabilities + total_equity):,.2f}"],
            ]
            total_table = self.create_table(total_data)
            story.append(total_table)
            
        finally:
            session.close()
            
        return story
