from enum import Enum, auto
from typing import List, Dict, Set
from sqlalchemy import <PERSON><PERSON><PERSON>, Inte<PERSON>, String, Foreign<PERSON>ey, Table, Boolean
from sqlalchemy.orm import relationship
from src.database.database import Base
import bcrypt

# Many-to-Many relationship tables
user_permissions = Table(
    'user_permissions',
    Base.metadata,
    Column('user_id', Integer, Foreign<PERSON>ey('users.id')),
    Column('permission_id', Integer, ForeignKey('permissions.id')),
    extend_existing=True
)

role_permissions = Table(
    'role_permissions',
    Base.metadata,
    Column('role_id', Integer, ForeignKey('roles.id')),
    Column('permission_id', Integer, ForeignKey('permissions.id')),
    extend_existing=True
)

class PermissionType(Enum):
    """Enum for different types of permissions"""
    VIEW = auto()
    CREATE = auto()
    EDIT = auto()
    DELETE = auto()
    ADMIN = auto()

class Module(Enum):
    """Enum for different modules in the system"""
    CUSTOMERS = auto()
    SUPPLIERS = auto()
    INVENTORY = auto()
    SALES = auto()
    PURCHASES = auto()
    EMPLOYEES = auto()
    PAYROLL = auto()
    EXPENSES = auto()
    ACCOUNTS = auto()
    REPORTS = auto()
    SETTINGS = auto()

class Permission(Base):
    """Permission model for the system"""
    __tablename__ = 'permissions'
    __table_args__ = {'extend_existing': True}

    id = Column(Integer, primary_key=True)
    module = Column(String(50), nullable=False)
    permission_type = Column(String(20), nullable=False)
    description = Column(String(200))

    users = relationship('User', secondary=user_permissions, back_populates='permissions')
    roles = relationship('Role', secondary=role_permissions, back_populates='role_permissions')

    def __repr__(self):
        return f"Permission({self.module}.{self.permission_type})"

class Role(Base):
    """Role model for user roles"""
    __tablename__ = 'roles'
    __table_args__ = {'extend_existing': True}

    id = Column(Integer, primary_key=True)
    name = Column(String(50), unique=True, nullable=False)
    description = Column(String(200))
    role_permissions = relationship('Permission', secondary=role_permissions, back_populates='roles')

    users = relationship('User', back_populates='role')

    def __repr__(self):
        return f"Role({self.name})"

class User(Base):
    """User model with role-based permissions"""
    __tablename__ = 'users'
    __table_args__ = {'extend_existing': True}

    id = Column(Integer, primary_key=True)
    username = Column(String(50), unique=True, nullable=False)
    password = Column(String(100), nullable=False)
    full_name = Column(String(100), nullable=False)
    email = Column(String(100), unique=True, nullable=False)
    role_id = Column(Integer, ForeignKey('roles.id'))
    is_active = Column(Boolean, default=True)
    is_admin = Column(Boolean, default=False)
    
    role = relationship('Role', back_populates='users')
    permissions = relationship('Permission', secondary=user_permissions, back_populates='users')

    def set_password(self, password: str):
        """Hash and set the password."""
        salt = bcrypt.gensalt()
        self.password = bcrypt.hashpw(password.encode(), salt).decode()

    def check_password(self, password: str) -> bool:
        """Check if the provided password matches."""
        return bcrypt.checkpw(password.encode(), self.password.encode())

    def has_permission(self, module: Module, permission_type: PermissionType) -> bool:
        """
        Check if user has specific permission
        
        Args:
            module (Module): The module to check permission for
            permission_type (PermissionType): The type of permission to check
            
        Returns:
            bool: True if user has permission, False otherwise
        """
        if self.is_admin:  # Admin has all permissions
            return True

        user_perms = {f"{p.module}.{p.permission_type}" for p in self.permissions}
        role_perms = {f"{p.module}.{p.permission_type}" for p in self.role.role_permissions} if self.role else set()
        perm_key = f"{module.name}.{permission_type.name}"
        
        return perm_key in user_perms or perm_key in role_perms

    def __repr__(self):
        return f"User({self.username})"
