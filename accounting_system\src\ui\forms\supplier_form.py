"""
Supplier management form.
"""
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QPushButton, 
    QLabel, QLineEdit, QTextEdit, QFormLayout,
    QTableWidget, QTableWidgetItem, QMessageBox,
    QSpinBox, QDoubleSpinBox, QCheckBox
)
from PyQt6.QtCore import Qt
from src.database.database import SessionLocal
from src.models.models import Supplier

class SupplierForm(QWidget):
    """Supplier form class."""
    
    def __init__(self):
        super().__init__()
        self._init_ui()
        self._load_data()
    
    def _init_ui(self):
        """Initialize the UI."""
        layout = QHBoxLayout()
        self.setLayout(layout)
        
        # Left side - Supplier list
        list_widget = QWidget()
        list_layout = QVBoxLayout()
        list_widget.setLayout(list_layout)
        
        # Add search box
        search_layout = QHBoxLayout()
        search_label = QLabel('بحث:')
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText('ابحث باسم المورد أو الرقم...')
        self.search_input.textChanged.connect(self._filter_suppliers)
        search_layout.addWidget(search_label)
        search_layout.addWidget(self.search_input)
        list_layout.addLayout(search_layout)
        
        # Add supplier table
        self.table = QTableWidget()
        self.table.setColumnCount(4)
        self.table.setHorizontalHeaderLabels(['الرقم', 'الاسم', 'الهاتف', 'الرصيد'])
        self.table.horizontalHeader().setStretchLastSection(True)
        self.table.itemClicked.connect(self._load_supplier)
        list_layout.addWidget(self.table)
        
        # Right side - Supplier details
        details_widget = QWidget()
        details_layout = QFormLayout()
        details_widget.setLayout(details_layout)
        
        # Add supplier fields
        self.code_input = QLineEdit()
        details_layout.addRow('رقم المورد:', self.code_input)
        
        self.name_input = QLineEdit()
        details_layout.addRow('اسم المورد:', self.name_input)
        
        self.contact_input = QLineEdit()
        details_layout.addRow('الشخص المسؤول:', self.contact_input)
        
        self.phone_input = QLineEdit()
        details_layout.addRow('رقم الهاتف:', self.phone_input)
        
        self.email_input = QLineEdit()
        details_layout.addRow('البريد الإلكتروني:', self.email_input)
        
        self.address_input = QTextEdit()
        self.address_input.setMaximumHeight(100)
        details_layout.addRow('العنوان:', self.address_input)
        
        self.tax_number_input = QLineEdit()
        details_layout.addRow('الرقم الضريبي:', self.tax_number_input)
        
        self.payment_terms_input = QLineEdit()
        details_layout.addRow('شروط الدفع:', self.payment_terms_input)
        
        self.is_active_input = QCheckBox('نشط')
        self.is_active_input.setChecked(True)
        details_layout.addRow('الحالة:', self.is_active_input)
        
        self.notes_input = QTextEdit()
        self.notes_input.setMaximumHeight(100)
        details_layout.addRow('ملاحظات:', self.notes_input)
        
        # Add buttons
        buttons_layout = QHBoxLayout()
        
        self.new_btn = QPushButton('جديد')
        self.new_btn.clicked.connect(self._clear_form)
        buttons_layout.addWidget(self.new_btn)
        
        self.save_btn = QPushButton('حفظ')
        self.save_btn.clicked.connect(self._save_supplier)
        buttons_layout.addWidget(self.save_btn)
        
        self.delete_btn = QPushButton('حذف')
        self.delete_btn.clicked.connect(self._delete_supplier)
        buttons_layout.addWidget(self.delete_btn)
        
        details_layout.addRow('', buttons_layout)
        
        # Add widgets to main layout
        layout.addWidget(list_widget, stretch=1)
        layout.addWidget(details_widget, stretch=1)
        
        # Initialize member variables
        self.current_supplier = None
    
    def _load_data(self):
        """Load suppliers data into table."""
        try:
            db = SessionLocal()
            suppliers = db.query(Supplier).all()
            self.table.setRowCount(len(suppliers))
            
            for i, supplier in enumerate(suppliers):
                self.table.setItem(i, 0, QTableWidgetItem(supplier.code))
                self.table.setItem(i, 1, QTableWidgetItem(supplier.name))
                self.table.setItem(i, 2, QTableWidgetItem(supplier.phone or ''))
                self.table.setItem(i, 3, QTableWidgetItem(str(supplier.balance)))
        
        except Exception as e:
            QMessageBox.critical(self, 'خطأ', f'حدث خطأ أثناء تحميل البيانات: {str(e)}')
        finally:
            db.close()
    
    def _filter_suppliers(self):
        """Filter suppliers based on search text."""
        search_text = self.search_input.text().lower()
        for row in range(self.table.rowCount()):
            match = False
            for col in range(self.table.columnCount()):
                item = self.table.item(row, col)
                if item and search_text in item.text().lower():
                    match = True
                    break
            self.table.setRowHidden(row, not match)
    
    def _load_supplier(self, item):
        """Load supplier details when selected from table."""
        try:
            db = SessionLocal()
            supplier_code = self.table.item(item.row(), 0).text()
            supplier = db.query(Supplier).filter(Supplier.code == supplier_code).first()
            
            if supplier:
                self.current_supplier = supplier
                self.code_input.setText(supplier.code)
                self.name_input.setText(supplier.name)
                self.contact_input.setText(supplier.contact_person or '')
                self.phone_input.setText(supplier.phone or '')
                self.email_input.setText(supplier.email or '')
                self.address_input.setText(supplier.address or '')
                self.tax_number_input.setText(supplier.tax_number or '')
                self.payment_terms_input.setText(supplier.payment_terms or '')
                self.is_active_input.setChecked(supplier.is_active)
                self.notes_input.setText(supplier.notes or '')
        
        except Exception as e:
            QMessageBox.critical(self, 'خطأ', f'حدث خطأ أثناء تحميل بيانات المورد: {str(e)}')
        finally:
            db.close()
    
    def _clear_form(self):
        """Clear form fields."""
        self.current_supplier = None
        self.code_input.clear()
        self.name_input.clear()
        self.contact_input.clear()
        self.phone_input.clear()
        self.email_input.clear()
        self.address_input.clear()
        self.tax_number_input.clear()
        self.payment_terms_input.clear()
        self.is_active_input.setChecked(True)
        self.notes_input.clear()
    
    def _save_supplier(self):
        """Save supplier data."""
        try:
            if not self.code_input.text() or not self.name_input.text():
                QMessageBox.warning(self, 'تنبيه', 'الرجاء إدخال رقم واسم المورد')
                return
            
            db = SessionLocal()
            
            if self.current_supplier:
                supplier = self.current_supplier
            else:
                # Check if code exists
                existing = db.query(Supplier).filter(
                    Supplier.code == self.code_input.text()
                ).first()
                if existing:
                    QMessageBox.warning(
                        self, 'تنبيه', 'رقم المورد موجود مسبقاً'
                    )
                    return
                supplier = Supplier()
            
            supplier.code = self.code_input.text()
            supplier.name = self.name_input.text()
            supplier.contact_person = self.contact_input.text()
            supplier.phone = self.phone_input.text()
            supplier.email = self.email_input.text()
            supplier.address = self.address_input.toPlainText()
            supplier.tax_number = self.tax_number_input.text()
            supplier.payment_terms = self.payment_terms_input.text()
            supplier.is_active = self.is_active_input.isChecked()
            supplier.notes = self.notes_input.toPlainText()
            
            if not self.current_supplier:
                db.add(supplier)
            
            db.commit()
            
            self._load_data()
            self._clear_form()
            
            QMessageBox.information(
                self, 'نجاح', 'تم حفظ بيانات المورد بنجاح'
            )
        
        except Exception as e:
            db.rollback()
            QMessageBox.critical(
                self, 'خطأ', f'حدث خطأ أثناء حفظ بيانات المورد: {str(e)}'
            )
        finally:
            db.close()
    
    def _delete_supplier(self):
        """Delete supplier."""
        if not self.current_supplier:
            QMessageBox.warning(self, 'تنبيه', 'الرجاء اختيار مورد للحذف')
            return
        
        reply = QMessageBox.question(
            self, 'تأكيد الحذف',
            'هل أنت متأكد من حذف هذا المورد؟',
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            try:
                db = SessionLocal()
                db.delete(self.current_supplier)
                db.commit()
                
                self._load_data()
                self._clear_form()
                
                QMessageBox.information(
                    self, 'نجاح', 'تم حذف المورد بنجاح'
                )
            
            except Exception as e:
                db.rollback()
                QMessageBox.critical(
                    self, 'خطأ', f'حدث خطأ أثناء حذف المورد: {str(e)}'
                )
            finally:
                db.close()
