"""
Sales and Purchase related models.
"""
from sqlalchemy import Column, Integer, String, Float, DateTime, ForeignKey, Enum, Text
from sqlalchemy.orm import relationship
from datetime import datetime
from src.database.database import Base
from src.models.customers import Customer
from src.models.suppliers import Supplier
import enum

class PaymentStatus(enum.Enum):
    """Payment status enumeration."""
    PENDING = "pending"
    PARTIAL = "partial"
    PAID = "paid"
    CANCELLED = "cancelled"

class DocumentStatus(enum.Enum):
    """Document status enumeration."""
    DRAFT = "draft"
    CONFIRMED = "confirmed"
    CANCELLED = "cancelled"

class Invoice(Base):
    """Invoice model for sales transactions."""
    __tablename__ = 'invoices'

    id = Column(Integer, primary_key=True)
    number = Column(String(20), unique=True, nullable=False)
    customer_id = Column(Integer, ForeignKey('customers.id'), nullable=False)
    date = Column(DateTime, nullable=False, default=datetime.utcnow)
    due_date = Column(DateTime)
    subtotal = Column(Float, default=0.0)
    tax_amount = Column(Float, default=0.0)
    discount = Column(Float, default=0.0)
    total = Column(Float, default=0.0)
    paid_amount = Column(Float, default=0.0)
    balance = Column(Float, default=0.0)
    status = Column(Enum(DocumentStatus), default=DocumentStatus.DRAFT)
    payment_status = Column(Enum(PaymentStatus), default=PaymentStatus.PENDING)
    notes = Column(Text)
    created_at = Column(DateTime, default=datetime.utcnow)
    created_by = Column(Integer, ForeignKey('users.id'))

    # Relationships
    customer = relationship('Customer', back_populates='invoices')
    items = relationship('InvoiceItem', back_populates='invoice')
    payments = relationship('Payment', back_populates='invoice')

    def __repr__(self):
        return f'<Invoice {self.number}>'

class InvoiceItem(Base):
    """Invoice item model."""
    __tablename__ = 'invoice_items'

    id = Column(Integer, primary_key=True)
    invoice_id = Column(Integer, ForeignKey('invoices.id'), nullable=False)
    product_id = Column(Integer, ForeignKey('products.id'), nullable=False)
    quantity = Column(Float, default=0.0)
    unit_price = Column(Float, default=0.0)
    tax_rate = Column(Float, default=0.0)
    tax_amount = Column(Float, default=0.0)
    discount = Column(Float, default=0.0)
    total = Column(Float, default=0.0)
    
    # Relationships
    invoice = relationship('Invoice', back_populates='items')
    product = relationship('Product', back_populates='invoice_items')

    def __repr__(self):
        return f'<InvoiceItem {self.id}>'

class Payment(Base):
    """Customer payment model."""
    __tablename__ = 'payments'

    id = Column(Integer, primary_key=True)
    number = Column(String(20), unique=True, nullable=False)
    customer_id = Column(Integer, ForeignKey('customers.id'), nullable=False)
    invoice_id = Column(Integer, ForeignKey('invoices.id'))
    date = Column(DateTime, nullable=False, default=datetime.utcnow)
    amount = Column(Float, default=0.0)
    payment_method = Column(String(50))
    reference = Column(String(100))
    notes = Column(Text)
    created_at = Column(DateTime, default=datetime.utcnow)
    created_by = Column(Integer, ForeignKey('users.id'))

    # Relationships
    invoice = relationship('Invoice', back_populates='payments')
    customer = relationship('Customer', back_populates='payments')

    def __repr__(self):
        return f'<Payment {self.number}>'

class PurchaseOrder(Base):
    """Purchase order model."""
    __tablename__ = 'purchase_orders'

    id = Column(Integer, primary_key=True)
    number = Column(String(20), unique=True, nullable=False)
    supplier_id = Column(Integer, ForeignKey('suppliers.id'), nullable=False)
    date = Column(DateTime, nullable=False, default=datetime.utcnow)
    expected_date = Column(DateTime)
    subtotal = Column(Float, default=0.0)
    tax_amount = Column(Float, default=0.0)
    discount = Column(Float, default=0.0)
    total = Column(Float, default=0.0)
    status = Column(Enum(DocumentStatus), default=DocumentStatus.DRAFT)
    notes = Column(Text)
    created_at = Column(DateTime, default=datetime.utcnow)
    created_by = Column(Integer, ForeignKey('users.id'))

    # Relationships
    items = relationship('PurchaseOrderItem', back_populates='purchase_order')
    payments = relationship('SupplierPayment', back_populates='purchase_order')
    supplier = relationship('Supplier', back_populates='purchase_orders')

    def __repr__(self):
        return f'<PurchaseOrder {self.number}>'

class PurchaseOrderItem(Base):
    """Purchase order item model."""
    __tablename__ = 'purchase_order_items'

    id = Column(Integer, primary_key=True)
    purchase_order_id = Column(Integer, ForeignKey('purchase_orders.id'), nullable=False)
    product_id = Column(Integer, ForeignKey('products.id'), nullable=False)
    description = Column(String(200))
    quantity = Column(Float, default=0)
    unit_price = Column(Float, default=0)
    tax_rate = Column(Float, default=0)
    discount = Column(Float, default=0)
    total = Column(Float, default=0)

    # Relationships
    purchase_order = relationship('PurchaseOrder', back_populates='items')
    product = relationship('Product', back_populates='purchase_order_items')

    def __repr__(self):
        return f'<PurchaseOrderItem {self.id}>'

class SupplierPayment(Base):
    """Supplier payment model."""
    __tablename__ = 'supplier_payments'

    id = Column(Integer, primary_key=True)
    number = Column(String(20), unique=True, nullable=False)
    supplier_id = Column(Integer, ForeignKey('suppliers.id'), nullable=False)
    purchase_order_id = Column(Integer, ForeignKey('purchase_orders.id'))
    date = Column(DateTime, nullable=False, default=datetime.utcnow)
    amount = Column(Float, default=0.0)
    payment_method = Column(String(50))
    reference = Column(String(100))
    notes = Column(Text)
    created_at = Column(DateTime, default=datetime.utcnow)
    created_by = Column(Integer, ForeignKey('users.id'))    # Relationships
    purchase_order = relationship('PurchaseOrder', back_populates='payments')
    supplier = relationship('Supplier', back_populates='supplier_payments')

    def __repr__(self):
        return f'<SupplierPayment {self.number}>'
