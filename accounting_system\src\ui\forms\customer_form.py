"""
Customer management form.
"""
from PyQt6.QtWidgets import (
    QW<PERSON>t, QVBoxLayout, QHBoxLayout, QPushButton, 
    QLabel, QLineEdit, QTextEdit, QFormLayout,
    QTableWidget, QTableWidgetItem, QMessageBox,
    QSpinBox, QDoubleSpinBox, QCheckBox
)
from PyQt6.QtCore import Qt
from src.database.database import SessionLocal
from src.models.models import Customer

class CustomerForm(QWidget):
    """Customer form class."""
    
    def __init__(self):
        super().__init__()
        self._init_ui()
        self._load_data()
    
    def _init_ui(self):
        """Initialize the UI."""
        layout = QHBoxLayout()
        self.setLayout(layout)
        
        # Left side - Customer list
        list_widget = QWidget()
        list_layout = QVBoxLayout()
        list_widget.setLayout(list_layout)
        
        # Add search box
        search_layout = QHBoxLayout()
        search_label = QLabel('بحث:')
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText('ابحث باسم العميل أو الرقم...')
        self.search_input.textChanged.connect(self._filter_customers)
        search_layout.addWidget(search_label)
        search_layout.addWidget(self.search_input)
        list_layout.addLayout(search_layout)
        
        # Add customer table
        self.table = QTableWidget()
        self.table.setColumnCount(4)
        self.table.setHorizontalHeaderLabels(['الرقم', 'الاسم', 'الهاتف', 'الرصيد'])
        self.table.horizontalHeader().setStretchLastSection(True)
        self.table.itemClicked.connect(self._load_customer)
        list_layout.addWidget(self.table)
        
        # Right side - Customer details
        details_widget = QWidget()
        details_layout = QFormLayout()
        details_widget.setLayout(details_layout)
        
        # Add customer fields
        self.code_input = QLineEdit()
        details_layout.addRow('رقم العميل:', self.code_input)
        
        self.name_input = QLineEdit()
        details_layout.addRow('اسم العميل:', self.name_input)
        
        self.contact_input = QLineEdit()
        details_layout.addRow('الشخص المسؤول:', self.contact_input)
        
        self.phone_input = QLineEdit()
        details_layout.addRow('رقم الهاتف:', self.phone_input)
        
        self.email_input = QLineEdit()
        details_layout.addRow('البريد الإلكتروني:', self.email_input)
        
        self.address_input = QTextEdit()
        self.address_input.setMaximumHeight(100)
        details_layout.addRow('العنوان:', self.address_input)
        
        self.tax_number_input = QLineEdit()
        details_layout.addRow('الرقم الضريبي:', self.tax_number_input)
        
        self.credit_limit_input = QDoubleSpinBox()
        self.credit_limit_input.setMaximum(1000000)
        self.credit_limit_input.setMinimum(0)
        details_layout.addRow('حد الائتمان:', self.credit_limit_input)
        
        self.is_active_input = QCheckBox('نشط')
        self.is_active_input.setChecked(True)
        details_layout.addRow('الحالة:', self.is_active_input)
        
        self.notes_input = QTextEdit()
        self.notes_input.setMaximumHeight(100)
        details_layout.addRow('ملاحظات:', self.notes_input)
        
        # Add buttons
        buttons_layout = QHBoxLayout()
        
        self.new_btn = QPushButton('جديد')
        self.new_btn.clicked.connect(self._clear_form)
        buttons_layout.addWidget(self.new_btn)
        
        self.save_btn = QPushButton('حفظ')
        self.save_btn.clicked.connect(self._save_customer)
        buttons_layout.addWidget(self.save_btn)
        
        self.delete_btn = QPushButton('حذف')
        self.delete_btn.clicked.connect(self._delete_customer)
        buttons_layout.addWidget(self.delete_btn)
        
        details_layout.addRow('', buttons_layout)
        
        # Add widgets to main layout
        layout.addWidget(list_widget, stretch=1)
        layout.addWidget(details_widget, stretch=1)
        
        # Initialize member variables
        self.current_customer = None
    
    def _load_data(self):
        """Load customers data into table."""
        try:
            db = SessionLocal()
            customers = db.query(Customer).all()
            self.table.setRowCount(len(customers))
            
            for i, customer in enumerate(customers):
                self.table.setItem(i, 0, QTableWidgetItem(customer.code))
                self.table.setItem(i, 1, QTableWidgetItem(customer.name))
                self.table.setItem(i, 2, QTableWidgetItem(customer.phone or ''))
                self.table.setItem(i, 3, QTableWidgetItem(str(customer.balance)))
        
        except Exception as e:
            QMessageBox.critical(self, 'خطأ', f'حدث خطأ أثناء تحميل البيانات: {str(e)}')
        finally:
            db.close()
    
    def _filter_customers(self):
        """Filter customers based on search text."""
        search_text = self.search_input.text().lower()
        for row in range(self.table.rowCount()):
            match = False
            for col in range(self.table.columnCount()):
                item = self.table.item(row, col)
                if item and search_text in item.text().lower():
                    match = True
                    break
            self.table.setRowHidden(row, not match)
    
    def _load_customer(self, item):
        """Load customer details when selected from table."""
        try:
            db = SessionLocal()
            customer_code = self.table.item(item.row(), 0).text()
            customer = db.query(Customer).filter(Customer.code == customer_code).first()
            
            if customer:
                self.current_customer = customer
                self.code_input.setText(customer.code)
                self.name_input.setText(customer.name)
                self.contact_input.setText(customer.contact_person or '')
                self.phone_input.setText(customer.phone or '')
                self.email_input.setText(customer.email or '')
                self.address_input.setText(customer.address or '')
                self.tax_number_input.setText(customer.tax_number or '')
                self.credit_limit_input.setValue(customer.credit_limit or 0)
                self.is_active_input.setChecked(customer.is_active)
                self.notes_input.setText(customer.notes or '')
        
        except Exception as e:
            QMessageBox.critical(self, 'خطأ', f'حدث خطأ أثناء تحميل بيانات العميل: {str(e)}')
        finally:
            db.close()
    
    def _clear_form(self):
        """Clear form fields."""
        self.current_customer = None
        self.code_input.clear()
        self.name_input.clear()
        self.contact_input.clear()
        self.phone_input.clear()
        self.email_input.clear()
        self.address_input.clear()
        self.tax_number_input.clear()
        self.credit_limit_input.setValue(0)
        self.is_active_input.setChecked(True)
        self.notes_input.clear()
    
    def _save_customer(self):
        """Save customer data."""
        try:
            if not self.code_input.text() or not self.name_input.text():
                QMessageBox.warning(self, 'تنبيه', 'الرجاء إدخال رقم واسم العميل')
                return
            
            db = SessionLocal()
            
            if self.current_customer:
                customer = self.current_customer
            else:
                # Check if code exists
                existing = db.query(Customer).filter(
                    Customer.code == self.code_input.text()
                ).first()
                if existing:
                    QMessageBox.warning(
                        self, 'تنبيه', 'رقم العميل موجود مسبقاً'
                    )
                    return
                customer = Customer()
            
            customer.code = self.code_input.text()
            customer.name = self.name_input.text()
            customer.contact_person = self.contact_input.text()
            customer.phone = self.phone_input.text()
            customer.email = self.email_input.text()
            customer.address = self.address_input.toPlainText()
            customer.tax_number = self.tax_number_input.text()
            customer.credit_limit = self.credit_limit_input.value()
            customer.is_active = self.is_active_input.isChecked()
            customer.notes = self.notes_input.toPlainText()
            
            if not self.current_customer:
                db.add(customer)
            
            db.commit()
            
            self._load_data()
            self._clear_form()
            
            QMessageBox.information(
                self, 'نجاح', 'تم حفظ بيانات العميل بنجاح'
            )
        
        except Exception as e:
            db.rollback()
            QMessageBox.critical(
                self, 'خطأ', f'حدث خطأ أثناء حفظ بيانات العميل: {str(e)}'
            )
        finally:
            db.close()
    
    def _delete_customer(self):
        """Delete customer."""
        if not self.current_customer:
            QMessageBox.warning(self, 'تنبيه', 'الرجاء اختيار عميل للحذف')
            return
        
        reply = QMessageBox.question(
            self, 'تأكيد الحذف',
            'هل أنت متأكد من حذف هذا العميل؟',
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            try:
                db = SessionLocal()
                db.delete(self.current_customer)
                db.commit()
                
                self._load_data()
                self._clear_form()
                
                QMessageBox.information(
                    self, 'نجاح', 'تم حذف العميل بنجاح'
                )
            
            except Exception as e:
                db.rollback()
                QMessageBox.critical(
                    self, 'خطأ', f'حدث خطأ أثناء حذف العميل: {str(e)}'
                )
            finally:
                db.close()
