"""
نموذج أوامر الشراء المتكامل
Enhanced Purchase Order Form
"""
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
    QLabel, QLineEdit, QTextEdit, QFormLayout,
    QTableWidget, QTableWidgetItem, QMessageBox,
    QDateEdit, QDoubleSpinBox, QComboBox, QSpinBox,
    QHeaderView, QAbstractItemView, QDialog, QDialogButtonBox,
    QGroupBox, QGridLayout, QTabWidget, QCheckBox
)
from PyQt6.QtCore import Qt, QDate
from PyQt6.QtGui import QFont
from src.database.database import SessionLocal
from src.models.suppliers import Supplier
from src.models.transactions import PurchaseOrder, PurchaseOrderItem, DocumentStatus
from src.models.inventory import Product
from src.utils.translation import _
from datetime import datetime

class PurchaseForm(QWidget):
    """Enhanced Purchase Order form class."""

    def __init__(self):
        super().__init__()
        self.current_purchase = None
        self._init_ui()
        self._load_data()

    def _init_ui(self):
        """Initialize the enhanced UI."""
        layout = QVBoxLayout()
        self.setLayout(layout)

        # Title
        title = QLabel(_('Purchase Order Management'))
        title.setFont(QFont('Arial', 16, QFont.Weight.Bold))
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title)

        # Search and filter section
        search_group = QGroupBox(_('Search & Filter'))
        search_layout = QGridLayout()
        search_group.setLayout(search_layout)

        # Search box
        search_layout.addWidget(QLabel(_('Search') + ':'), 0, 0)
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText(_('Search by purchase order number or supplier...'))
        self.search_input.textChanged.connect(self._filter_purchases)
        search_layout.addWidget(self.search_input, 0, 1)

        # Date filter
        search_layout.addWidget(QLabel(_('From Date') + ':'), 0, 2)
        self.from_date = QDateEdit()
        self.from_date.setCalendarPopup(True)
        self.from_date.setDate(QDate.currentDate().addDays(-30))
        search_layout.addWidget(self.from_date, 0, 3)

        search_layout.addWidget(QLabel(_('To Date') + ':'), 0, 4)
        self.to_date = QDateEdit()
        self.to_date.setCalendarPopup(True)
        self.to_date.setDate(QDate.currentDate())
        search_layout.addWidget(self.to_date, 0, 5)

        # Status filter
        search_layout.addWidget(QLabel(_('Status') + ':'), 1, 0)
        self.status_filter = QComboBox()
        self.status_filter.addItem(_('All Statuses'))
        self.status_filter.addItems([status.value for status in DocumentStatus])
        self.status_filter.currentTextChanged.connect(self._filter_purchases)
        search_layout.addWidget(self.status_filter, 1, 1)

        layout.addWidget(search_group)

        # Buttons section
        buttons_layout = QHBoxLayout()

        self.new_btn = QPushButton(_('New Purchase Order'))
        self.new_btn.clicked.connect(self._new_purchase)
        buttons_layout.addWidget(self.new_btn)

        self.edit_btn = QPushButton(_('Edit Purchase Order'))
        self.edit_btn.clicked.connect(self._edit_purchase)
        self.edit_btn.setEnabled(False)
        buttons_layout.addWidget(self.edit_btn)

        self.delete_btn = QPushButton(_('Delete Purchase Order'))
        self.delete_btn.clicked.connect(self._delete_purchase)
        self.delete_btn.setEnabled(False)
        buttons_layout.addWidget(self.delete_btn)

        self.receive_btn = QPushButton(_('Receive Items'))
        self.receive_btn.clicked.connect(self._receive_items)
        self.receive_btn.setEnabled(False)
        buttons_layout.addWidget(self.receive_btn)

        self.refresh_btn = QPushButton(_('Refresh'))
        self.refresh_btn.clicked.connect(self._load_data)
        buttons_layout.addWidget(self.refresh_btn)

        buttons_layout.addStretch()
        layout.addLayout(buttons_layout)

        # Purchase orders table
        self.table = QTableWidget()
        self.table.setColumnCount(8)
        self.table.setHorizontalHeaderLabels([
            _('PO Number'), _('Date'), _('Supplier'), _('Subtotal'),
            _('Tax'), _('Total'), _('Status'), _('Received')
        ])

        # Configure table
        header = self.table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        self.table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.table.setAlternatingRowColors(True)
        self.table.itemSelectionChanged.connect(self._on_selection_changed)
        self.table.itemDoubleClicked.connect(self._edit_purchase)

        layout.addWidget(self.table)

    def _on_selection_changed(self):
        """Handle selection change."""
        has_selection = len(self.table.selectedItems()) > 0
        self.edit_btn.setEnabled(has_selection)
        self.delete_btn.setEnabled(has_selection)
        self.receive_btn.setEnabled(has_selection)

    def _load_data(self):
        """Load purchase orders data."""
        self._load_purchases()

    def _load_purchases(self):
        """Load purchase orders into table."""
        session = SessionLocal()
        try:
            purchases = session.query(PurchaseOrder).all()
            self.table.setRowCount(len(purchases))

            for i, purchase in enumerate(purchases):
                # PO Number
                self.table.setItem(i, 0, QTableWidgetItem(purchase.po_number))

                # Date
                date_str = purchase.order_date.strftime('%Y-%m-%d') if purchase.order_date else ''
                self.table.setItem(i, 1, QTableWidgetItem(date_str))

                # Supplier
                supplier_name = purchase.supplier.name if purchase.supplier else _('No Supplier')
                self.table.setItem(i, 2, QTableWidgetItem(supplier_name))

                # Subtotal
                self.table.setItem(i, 3, QTableWidgetItem(f"{purchase.subtotal:.2f}"))

                # Tax
                self.table.setItem(i, 4, QTableWidgetItem(f"{purchase.tax_amount:.2f}"))

                # Total
                self.table.setItem(i, 5, QTableWidgetItem(f"{purchase.total_amount:.2f}"))

                # Status
                status_text = purchase.status.value if purchase.status else _('Draft')
                self.table.setItem(i, 6, QTableWidgetItem(status_text))

                # Received
                received_text = _('Yes') if purchase.is_received else _('No')
                self.table.setItem(i, 7, QTableWidgetItem(received_text))

                # Store purchase ID in first column
                self.table.item(i, 0).setData(Qt.ItemDataRole.UserRole, purchase.id)

        finally:
            session.close()

    def _filter_purchases(self):
        """Filter purchase orders based on search criteria."""
        search_text = self.search_input.text().lower()
        status_filter = self.status_filter.currentText()

        for row in range(self.table.rowCount()):
            show_row = True

            # Search filter
            if search_text:
                po_number = self.table.item(row, 0).text().lower()
                supplier = self.table.item(row, 2).text().lower()
                if search_text not in po_number and search_text not in supplier:
                    show_row = False

            # Status filter
            if status_filter != _('All Statuses'):
                status = self.table.item(row, 6).text()
                if status != status_filter:
                    show_row = False

            self.table.setRowHidden(row, not show_row)

    def _new_purchase(self):
        """Create new purchase order."""
        dialog = PurchaseOrderDialog(parent=self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            if dialog.validate_data():
                self._save_purchase_data(dialog.get_purchase_data())

    def _edit_purchase(self):
        """Edit selected purchase order."""
        current_row = self.table.currentRow()
        if current_row < 0:
            return

        purchase_id = self.table.item(current_row, 0).data(Qt.ItemDataRole.UserRole)

        session = SessionLocal()
        try:
            purchase = session.query(PurchaseOrder).get(purchase_id)
            if purchase:
                dialog = PurchaseOrderDialog(purchase, parent=self)
                if dialog.exec() == QDialog.DialogCode.Accepted:
                    if dialog.validate_data():
                        self._update_purchase_data(purchase, dialog.get_purchase_data())
        finally:
            session.close()

    def _delete_purchase(self):
        """Delete selected purchase order."""
        current_row = self.table.currentRow()
        if current_row < 0:
            return

        po_number = self.table.item(current_row, 0).text()

        reply = QMessageBox.question(
            self, _('Confirm Delete'),
            _('Are you sure you want to delete purchase order: ') + po_number + '?',
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            purchase_id = self.table.item(current_row, 0).data(Qt.ItemDataRole.UserRole)

            session = SessionLocal()
            try:
                purchase = session.query(PurchaseOrder).get(purchase_id)
                if purchase:
                    # Delete purchase order items first
                    for item in purchase.items:
                        session.delete(item)
                    # Delete purchase order
                    session.delete(purchase)
                    session.commit()
                    QMessageBox.information(self, _('Success'), _('Purchase order deleted successfully'))
                    self._load_data()
            except Exception as e:
                session.rollback()
                QMessageBox.critical(self, _('Error'), _('Failed to delete purchase order: ') + str(e))
            finally:
                session.close()

    def _receive_items(self):
        """Receive items from purchase order."""
        current_row = self.table.currentRow()
        if current_row < 0:
            return

        purchase_id = self.table.item(current_row, 0).data(Qt.ItemDataRole.UserRole)

        session = SessionLocal()
        try:
            purchase = session.query(PurchaseOrder).get(purchase_id)
            if purchase:
                dialog = ReceiveItemsDialog(purchase, parent=self)
                if dialog.exec() == QDialog.DialogCode.Accepted:
                    # Update inventory and mark as received
                    self._process_received_items(purchase, dialog.get_received_items())
        finally:
            session.close()

    def _save_purchase_data(self, data):
        """Save new purchase order."""
        session = SessionLocal()
        try:
            # Generate PO number if not provided
            if not data['po_number']:
                last_purchase = session.query(PurchaseOrder).order_by(PurchaseOrder.id.desc()).first()
                if last_purchase:
                    last_num = int(last_purchase.po_number.split('-')[-1]) if '-' in last_purchase.po_number else 0
                    data['po_number'] = f"PO-{last_num + 1:06d}"
                else:
                    data['po_number'] = "PO-000001"

            purchase = PurchaseOrder(
                po_number=data['po_number'],
                order_date=data['order_date'],
                supplier_id=data['supplier_id'],
                subtotal=data['subtotal'],
                tax_amount=data['tax_amount'],
                discount_amount=data['discount_amount'],
                total_amount=data['total_amount'],
                status=data['status'],
                notes=data['notes']
            )

            session.add(purchase)
            session.flush()  # Get purchase ID

            # Add purchase order items
            for item_data in data['items']:
                item = PurchaseOrderItem(
                    purchase_order_id=purchase.id,
                    product_id=item_data['product_id'],
                    quantity=item_data['quantity'],
                    unit_price=item_data['unit_price'],
                    tax_rate=item_data['tax_rate'],
                    line_total=item_data['line_total']
                )
                session.add(item)

            session.commit()
            QMessageBox.information(self, _('Success'), _('Purchase order saved successfully'))
            self._load_data()

        except Exception as e:
            session.rollback()
            QMessageBox.critical(self, _('Error'), _('Failed to save purchase order: ') + str(e))
        finally:
            session.close()

    def _update_purchase_data(self, purchase, data):
        """Update existing purchase order."""
        session = SessionLocal()
        try:
            # Update purchase fields
            purchase.po_number = data['po_number']
            purchase.order_date = data['order_date']
            purchase.supplier_id = data['supplier_id']
            purchase.subtotal = data['subtotal']
            purchase.tax_amount = data['tax_amount']
            purchase.discount_amount = data['discount_amount']
            purchase.total_amount = data['total_amount']
            purchase.status = data['status']
            purchase.notes = data['notes']

            # Delete existing items
            for item in purchase.items:
                session.delete(item)

            # Add new items
            for item_data in data['items']:
                item = PurchaseOrderItem(
                    purchase_order_id=purchase.id,
                    product_id=item_data['product_id'],
                    quantity=item_data['quantity'],
                    unit_price=item_data['unit_price'],
                    tax_rate=item_data['tax_rate'],
                    line_total=item_data['line_total']
                )
                session.add(item)

            session.commit()
            QMessageBox.information(self, _('Success'), _('Purchase order updated successfully'))
            self._load_data()

        except Exception as e:
            session.rollback()
            QMessageBox.critical(self, _('Error'), _('Failed to update purchase order: ') + str(e))
        finally:
            session.close()

    def _process_received_items(self, purchase, received_items):
        """Process received items and update inventory."""
        session = SessionLocal()
        try:
            from src.models.inventory import InventoryTransaction

            for item_id, received_qty in received_items.items():
                if received_qty > 0:
                    # Find the purchase order item
                    po_item = session.query(PurchaseOrderItem).get(item_id)
                    if po_item:
                        # Update product stock
                        product = po_item.product
                        product.current_stock += received_qty

                        # Create inventory transaction
                        transaction = InventoryTransaction(
                            product_id=product.id,
                            transaction_type='IN',
                            quantity=received_qty,
                            unit_price=po_item.unit_price,
                            transaction_date=datetime.now(),
                            notes=f'Received from PO: {purchase.po_number}',
                            reference_type='purchase',
                            reference_id=purchase.id
                        )
                        session.add(transaction)

            # Mark purchase as received
            purchase.is_received = True
            purchase.received_date = datetime.now()

            session.commit()
            QMessageBox.information(self, _('Success'), _('Items received successfully'))
            self._load_data()

        except Exception as e:
            session.rollback()
            QMessageBox.critical(self, _('Error'), _('Failed to process received items: ') + str(e))
        finally:
            session.close()


class PurchaseOrderDialog(QDialog):
    """Dialog for adding/editing purchase orders."""

    def __init__(self, purchase=None, parent=None):
        super().__init__(parent)
        self.purchase = purchase
        self.setWindowTitle(_('New Purchase Order') if purchase is None else _('Edit Purchase Order'))
        self.setModal(True)
        self.setMinimumSize(800, 700)

        self.setup_ui()
        if purchase:
            self.load_purchase_data()
        else:
            self.generate_po_number()

    def setup_ui(self):
        """Setup the dialog UI."""
        layout = QVBoxLayout()

        # Create tabs
        tab_widget = QTabWidget()

        # Purchase Order Header Tab
        header_tab = QWidget()
        header_layout = QFormLayout()
        header_tab.setLayout(header_layout)

        # PO number
        self.po_number_edit = QLineEdit()
        self.po_number_edit.setReadOnly(True)
        header_layout.addRow(_('PO Number') + ':', self.po_number_edit)

        # Date
        self.date_edit = QDateEdit()
        self.date_edit.setCalendarPopup(True)
        self.date_edit.setDate(QDate.currentDate())
        header_layout.addRow(_('Date') + '*:', self.date_edit)

        # Supplier
        self.supplier_combo = QComboBox()
        self.load_suppliers()
        header_layout.addRow(_('Supplier') + '*:', self.supplier_combo)

        # Notes
        self.notes_edit = QTextEdit()
        self.notes_edit.setMaximumHeight(80)
        header_layout.addRow(_('Notes') + ':', self.notes_edit)

        # Status
        self.status_combo = QComboBox()
        self.status_combo.addItems([status.value for status in DocumentStatus])
        header_layout.addRow(_('Status') + ':', self.status_combo)

        tab_widget.addTab(header_tab, _('Purchase Order Header'))

        # Purchase Order Items Tab
        items_tab = QWidget()
        items_layout = QVBoxLayout()
        items_tab.setLayout(items_layout)

        # Items toolbar
        items_toolbar = QHBoxLayout()

        self.add_item_btn = QPushButton(_('Add Item'))
        self.add_item_btn.clicked.connect(self.add_item)
        items_toolbar.addWidget(self.add_item_btn)

        self.remove_item_btn = QPushButton(_('Remove Item'))
        self.remove_item_btn.clicked.connect(self.remove_item)
        items_toolbar.addWidget(self.remove_item_btn)

        items_toolbar.addStretch()
        items_layout.addLayout(items_toolbar)

        # Items table
        self.items_table = QTableWidget()
        self.items_table.setColumnCount(6)
        self.items_table.setHorizontalHeaderLabels([
            _('Product'), _('Quantity'), _('Unit Price'),
            _('Tax Rate') + ' %', _('Tax Amount'), _('Line Total')
        ])

        # Configure items table
        header = self.items_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        self.items_table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)

        items_layout.addWidget(self.items_table)

        # Totals section
        totals_group = QGroupBox(_('Totals'))
        totals_layout = QFormLayout()
        totals_group.setLayout(totals_layout)

        self.subtotal_edit = QLineEdit()
        self.subtotal_edit.setReadOnly(True)
        totals_layout.addRow(_('Subtotal') + ':', self.subtotal_edit)

        self.discount_spin = QDoubleSpinBox()
        self.discount_spin.setMaximum(999999.99)
        self.discount_spin.setDecimals(2)
        self.discount_spin.valueChanged.connect(self.calculate_totals)
        totals_layout.addRow(_('Discount') + ':', self.discount_spin)

        self.tax_amount_edit = QLineEdit()
        self.tax_amount_edit.setReadOnly(True)
        totals_layout.addRow(_('Tax Amount') + ':', self.tax_amount_edit)

        self.total_edit = QLineEdit()
        self.total_edit.setReadOnly(True)
        totals_layout.addRow(_('Total Amount') + ':', self.total_edit)

        items_layout.addWidget(totals_group)

        tab_widget.addTab(items_tab, _('Purchase Order Items'))

        layout.addWidget(tab_widget)

        # Buttons
        button_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok |
                                     QDialogButtonBox.StandardButton.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)

        self.setLayout(layout)

    def load_suppliers(self):
        """Load suppliers."""
        session = SessionLocal()
        try:
            suppliers = session.query(Supplier).filter(Supplier.is_active == True).all()
            self.supplier_combo.addItem(_('Select Supplier'), None)
            for supplier in suppliers:
                self.supplier_combo.addItem(supplier.name, supplier.id)
        finally:
            session.close()

    def generate_po_number(self):
        """Generate new PO number."""
        session = SessionLocal()
        try:
            last_purchase = session.query(PurchaseOrder).order_by(PurchaseOrder.id.desc()).first()
            if last_purchase:
                last_num = int(last_purchase.po_number.split('-')[-1]) if '-' in last_purchase.po_number else 0
                new_number = f"PO-{last_num + 1:06d}"
            else:
                new_number = "PO-000001"
            self.po_number_edit.setText(new_number)
        finally:
            session.close()

    def add_item(self):
        """Add new item to purchase order."""
        dialog = PurchaseOrderItemDialog(parent=self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            item_data = dialog.get_item_data()

            row = self.items_table.rowCount()
            self.items_table.insertRow(row)

            # Product
            product_item = QTableWidgetItem(item_data['product_name'])
            product_item.setData(Qt.ItemDataRole.UserRole, item_data['product_id'])
            self.items_table.setItem(row, 0, product_item)

            # Quantity
            self.items_table.setItem(row, 1, QTableWidgetItem(str(item_data['quantity'])))

            # Unit Price
            self.items_table.setItem(row, 2, QTableWidgetItem(f"{item_data['unit_price']:.2f}"))

            # Tax Rate
            self.items_table.setItem(row, 3, QTableWidgetItem(f"{item_data['tax_rate']:.2f}"))

            # Tax Amount
            tax_amount = (item_data['quantity'] * item_data['unit_price'] * item_data['tax_rate']) / 100
            self.items_table.setItem(row, 4, QTableWidgetItem(f"{tax_amount:.2f}"))

            # Line Total
            line_total = (item_data['quantity'] * item_data['unit_price']) + tax_amount
            self.items_table.setItem(row, 5, QTableWidgetItem(f"{line_total:.2f}"))

            self.calculate_totals()

    def remove_item(self):
        """Remove selected item."""
        current_row = self.items_table.currentRow()
        if current_row >= 0:
            self.items_table.removeRow(current_row)
            self.calculate_totals()

    def calculate_totals(self):
        """Calculate purchase order totals."""
        subtotal = 0.0
        tax_amount = 0.0

        for row in range(self.items_table.rowCount()):
            line_total_item = self.items_table.item(row, 5)
            tax_amount_item = self.items_table.item(row, 4)

            if line_total_item:
                line_total = float(line_total_item.text())
                subtotal += line_total

            if tax_amount_item:
                tax_amount += float(tax_amount_item.text())

        discount = self.discount_spin.value()
        total = subtotal - discount

        self.subtotal_edit.setText(f"{subtotal:.2f}")
        self.tax_amount_edit.setText(f"{tax_amount:.2f}")
        self.total_edit.setText(f"{total:.2f}")

    def load_purchase_data(self):
        """Load purchase order data for editing."""
        if not self.purchase:
            return

        self.po_number_edit.setText(self.purchase.po_number)
        self.date_edit.setDate(QDate.fromString(self.purchase.order_date.strftime('%Y-%m-%d'), 'yyyy-MM-dd'))
        self.notes_edit.setPlainText(self.purchase.notes or '')
        self.discount_spin.setValue(self.purchase.discount_amount or 0)

        # Set supplier
        if self.purchase.supplier_id:
            for i in range(self.supplier_combo.count()):
                if self.supplier_combo.itemData(i) == self.purchase.supplier_id:
                    self.supplier_combo.setCurrentIndex(i)
                    break

        # Set status
        if self.purchase.status:
            status_index = self.status_combo.findText(self.purchase.status.value)
            if status_index >= 0:
                self.status_combo.setCurrentIndex(status_index)

        # Load items
        session = SessionLocal()
        try:
            for item in self.purchase.items:
                row = self.items_table.rowCount()
                self.items_table.insertRow(row)

                # Product
                product_name = item.product.name if item.product else _('Unknown Product')
                product_item = QTableWidgetItem(product_name)
                product_item.setData(Qt.ItemDataRole.UserRole, item.product_id)
                self.items_table.setItem(row, 0, product_item)

                # Quantity
                self.items_table.setItem(row, 1, QTableWidgetItem(str(item.quantity)))

                # Unit Price
                self.items_table.setItem(row, 2, QTableWidgetItem(f"{item.unit_price:.2f}"))

                # Tax Rate
                self.items_table.setItem(row, 3, QTableWidgetItem(f"{item.tax_rate:.2f}"))

                # Tax Amount
                tax_amount = (item.quantity * item.unit_price * item.tax_rate) / 100
                self.items_table.setItem(row, 4, QTableWidgetItem(f"{tax_amount:.2f}"))

                # Line Total
                self.items_table.setItem(row, 5, QTableWidgetItem(f"{item.line_total:.2f}"))
        finally:
            session.close()

        self.calculate_totals()

    def get_purchase_data(self):
        """Get purchase order data from form."""
        items = []
        for row in range(self.items_table.rowCount()):
            product_id = self.items_table.item(row, 0).data(Qt.ItemDataRole.UserRole)
            quantity = float(self.items_table.item(row, 1).text())
            unit_price = float(self.items_table.item(row, 2).text())
            tax_rate = float(self.items_table.item(row, 3).text())
            line_total = float(self.items_table.item(row, 5).text())

            items.append({
                'product_id': product_id,
                'quantity': quantity,
                'unit_price': unit_price,
                'tax_rate': tax_rate,
                'line_total': line_total
            })

        return {
            'po_number': self.po_number_edit.text().strip(),
            'order_date': self.date_edit.date().toPython(),
            'supplier_id': self.supplier_combo.currentData(),
            'notes': self.notes_edit.toPlainText().strip(),
            'subtotal': float(self.subtotal_edit.text() or 0),
            'tax_amount': float(self.tax_amount_edit.text() or 0),
            'discount_amount': self.discount_spin.value(),
            'total_amount': float(self.total_edit.text() or 0),
            'status': DocumentStatus(self.status_combo.currentText()),
            'items': items
        }

    def validate_data(self):
        """Validate form data."""
        data = self.get_purchase_data()

        if not data['supplier_id']:
            QMessageBox.warning(self, _('Validation Error'), _('Please select a supplier'))
            return False

        if not data['items']:
            QMessageBox.warning(self, _('Validation Error'), _('Please add at least one item'))
            return False

        return True


class PurchaseOrderItemDialog(QDialog):
    """Dialog for adding purchase order items."""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle(_('Add Purchase Order Item'))
        self.setModal(True)
        self.setMinimumSize(400, 300)

        self.setup_ui()

    def setup_ui(self):
        """Setup the dialog UI."""
        layout = QVBoxLayout()

        # Form layout
        form_layout = QFormLayout()

        # Product
        self.product_combo = QComboBox()
        self.load_products()
        self.product_combo.currentTextChanged.connect(self.on_product_changed)
        form_layout.addRow(_('Product') + '*:', self.product_combo)

        # Quantity
        self.quantity_spin = QDoubleSpinBox()
        self.quantity_spin.setMinimum(0.01)
        self.quantity_spin.setMaximum(999999.99)
        self.quantity_spin.setDecimals(2)
        self.quantity_spin.setValue(1.0)
        form_layout.addRow(_('Quantity') + '*:', self.quantity_spin)

        # Unit Price
        self.unit_price_spin = QDoubleSpinBox()
        self.unit_price_spin.setMaximum(999999.99)
        self.unit_price_spin.setDecimals(2)
        form_layout.addRow(_('Unit Price') + '*:', self.unit_price_spin)

        # Tax Rate
        self.tax_rate_spin = QDoubleSpinBox()
        self.tax_rate_spin.setMaximum(100.0)
        self.tax_rate_spin.setDecimals(2)
        self.tax_rate_spin.setSuffix('%')
        form_layout.addRow(_('Tax Rate') + ':', self.tax_rate_spin)

        layout.addLayout(form_layout)

        # Buttons
        button_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok |
                                     QDialogButtonBox.StandardButton.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)

        self.setLayout(layout)

    def load_products(self):
        """Load products."""
        session = SessionLocal()
        try:
            products = session.query(Product).filter(Product.is_active == True).all()
            self.product_combo.addItem(_('Select Product'), None)
            for product in products:
                self.product_combo.addItem(f"{product.code} - {product.name}", product.id)
        finally:
            session.close()

    def on_product_changed(self):
        """Handle product selection change."""
        product_id = self.product_combo.currentData()
        if product_id:
            session = SessionLocal()
            try:
                product = session.query(Product).get(product_id)
                if product:
                    self.unit_price_spin.setValue(product.purchase_price)
                    self.tax_rate_spin.setValue(product.tax_rate)
            finally:
                session.close()

    def get_item_data(self):
        """Get item data from form."""
        return {
            'product_id': self.product_combo.currentData(),
            'product_name': self.product_combo.currentText(),
            'quantity': self.quantity_spin.value(),
            'unit_price': self.unit_price_spin.value(),
            'tax_rate': self.tax_rate_spin.value()
        }


class ReceiveItemsDialog(QDialog):
    """Dialog for receiving items from purchase order."""

    def __init__(self, purchase_order, parent=None):
        super().__init__(parent)
        self.purchase_order = purchase_order
        self.setWindowTitle(_('Receive Items') + f' - {purchase_order.po_number}')
        self.setModal(True)
        self.setMinimumSize(600, 400)

        self.setup_ui()
        self.load_items()

    def setup_ui(self):
        """Setup the dialog UI."""
        layout = QVBoxLayout()

        # Info label
        info_label = QLabel(_('Enter the quantity received for each item:'))
        layout.addWidget(info_label)

        # Items table
        self.items_table = QTableWidget()
        self.items_table.setColumnCount(5)
        self.items_table.setHorizontalHeaderLabels([
            _('Product'), _('Ordered Qty'), _('Unit Price'),
            _('Received Qty'), _('Remaining Qty')
        ])

        # Configure table
        header = self.items_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeMode.Stretch)

        layout.addWidget(self.items_table)

        # Buttons
        button_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok |
                                     QDialogButtonBox.StandardButton.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)

        self.setLayout(layout)

    def load_items(self):
        """Load purchase order items."""
        self.items_table.setRowCount(len(self.purchase_order.items))

        for i, item in enumerate(self.purchase_order.items):
            # Product
            product_name = item.product.name if item.product else _('Unknown Product')
            self.items_table.setItem(i, 0, QTableWidgetItem(product_name))

            # Ordered Quantity
            self.items_table.setItem(i, 1, QTableWidgetItem(str(item.quantity)))

            # Unit Price
            self.items_table.setItem(i, 2, QTableWidgetItem(f"{item.unit_price:.2f}"))

            # Received Quantity (editable)
            received_spin = QDoubleSpinBox()
            received_spin.setMaximum(item.quantity)
            received_spin.setDecimals(2)
            received_spin.setValue(item.quantity)  # Default to full quantity
            received_spin.valueChanged.connect(lambda: self.update_remaining())
            self.items_table.setCellWidget(i, 3, received_spin)

            # Remaining Quantity
            remaining_item = QTableWidgetItem("0.00")
            remaining_item.setFlags(remaining_item.flags() & ~Qt.ItemFlag.ItemIsEditable)
            self.items_table.setItem(i, 4, remaining_item)

            # Store item ID
            self.items_table.item(i, 0).setData(Qt.ItemDataRole.UserRole, item.id)

    def update_remaining(self):
        """Update remaining quantities."""
        for row in range(self.items_table.rowCount()):
            ordered_qty = float(self.items_table.item(row, 1).text())
            received_spin = self.items_table.cellWidget(row, 3)
            received_qty = received_spin.value()
            remaining_qty = ordered_qty - received_qty

            self.items_table.item(row, 4).setText(f"{remaining_qty:.2f}")

    def get_received_items(self):
        """Get received items data."""
        received_items = {}

        for row in range(self.items_table.rowCount()):
            item_id = self.items_table.item(row, 0).data(Qt.ItemDataRole.UserRole)
            received_spin = self.items_table.cellWidget(row, 3)
            received_qty = received_spin.value()

            received_items[item_id] = received_qty

        return received_items