"""
Purchase order form.
"""
from PyQt6.QtWidgets import <PERSON><PERSON><PERSON>t, QLabel, QVBoxLayout
from PyQt6.QtCore import Qt

class PurchaseForm(QWidget):
    """Purchase form class."""
    
    def __init__(self):
        super().__init__()
        self._init_ui()
    
    def _init_ui(self):
        """Initialize the UI."""
        layout = QVBoxLayout()
        self.setLayout(layout)
        
        # Placeholder label
        label = QLabel('نموذج المشتريات - قيد التطوير')
        label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(label)
