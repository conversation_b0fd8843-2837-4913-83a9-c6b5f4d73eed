#!/usr/bin/env python3
"""
ملف تشغيل مبسط للنظام
Simple Launch File
"""
import sys
import os

print("🚀 تشغيل نظام المحاسبة...")
print(f"📁 المجلد الحالي: {os.getcwd()}")

# Add src to path
src_path = os.path.join(os.getcwd(), 'src')
if os.path.exists(src_path):
    sys.path.insert(0, src_path)
    print(f"✅ تم إضافة مجلد src: {src_path}")
else:
    print(f"❌ مجلد src غير موجود: {src_path}")

try:
    # Test PyQt6
    from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel
    from PyQt6.QtCore import Qt
    from PyQt6.QtGui import QFont
    print("✅ PyQt6 متاح!")
    
    # Create simple app
    app = QApplication(sys.argv)
    
    # Create window
    window = QMainWindow()
    window.setWindowTitle("🏢 نظام المحاسبة - Accounting System")
    window.setGeometry(200, 200, 800, 600)
    
    # Create central widget
    central = QWidget()
    window.setCentralWidget(central)
    layout = QVBoxLayout()
    central.setLayout(layout)
    
    # Add title
    title = QLabel("🎉 نظام المحاسبة يعمل بنجاح!")
    title.setFont(QFont("Arial", 16, QFont.Weight.Bold))
    title.setAlignment(Qt.AlignmentFlag.AlignCenter)
    title.setStyleSheet("color: #2c3e50; padding: 20px; background-color: #ecf0f1; border-radius: 10px; margin: 20px;")
    layout.addWidget(title)
    
    # Add info
    info = QLabel("""
    ✅ النظام جاهز للاستخدام
    ✅ PyQt6 يعمل بنجاح
    ✅ الواجهة الرسومية متاحة
    
    🔑 بيانات الدخول التجريبية:
    👤 اسم المستخدم: admin
    🔒 كلمة المرور: admin123
    """)
    info.setAlignment(Qt.AlignmentFlag.AlignCenter)
    info.setStyleSheet("color: #27ae60; padding: 20px; font-size: 12px;")
    layout.addWidget(info)
    
    # Show window
    window.show()
    print("✅ تم فتح النافذة!")
    print("💡 أغلق النافذة للخروج")
    
    # Run app
    sys.exit(app.exec())
    
except ImportError as e:
    print(f"❌ خطأ في الاستيراد: {e}")
    print("💡 تأكد من تثبيت PyQt6: pip install PyQt6")
    
except Exception as e:
    print(f"❌ خطأ: {e}")
    import traceback
    traceback.print_exc()
