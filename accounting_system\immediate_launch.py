#!/usr/bin/env python3
"""
تشغيل فوري للنظام
Immediate System Launch
"""
import sys
import os
import subprocess

print("🚀 تشغيل نظام المحاسبة فوراً...")

# التأكد من المجلد الصحيح
current_dir = os.path.dirname(os.path.abspath(__file__))
os.chdir(current_dir)
print(f"📁 المجلد الحالي: {current_dir}")

# إضافة مجلد src
src_path = os.path.join(current_dir, 'src')
if os.path.exists(src_path):
    sys.path.insert(0, src_path)
    print(f"✅ تم إضافة مجلد src: {src_path}")

# محاولة تثبيت PyQt6 تلقائياً
print("\n🔧 محاولة تثبيت PyQt6...")
try:
    subprocess.run([sys.executable, "-m", "pip", "install", "--user", "PyQt6"], 
                   check=False, capture_output=True, timeout=60)
    print("✅ تم محاولة تثبيت PyQt6")
except:
    print("⚠️ لم يتم تثبيت PyQt6 تلقائياً")

# اختبار PyQt6
print("\n🔍 اختبار PyQt6...")
try:
    from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QPushButton
    from PyQt6.QtCore import Qt
    from PyQt6.QtGui import QFont
    print("✅ PyQt6 متاح!")
    
    # إنشاء التطبيق
    app = QApplication(sys.argv)
    app.setApplicationName("نظام المحاسبة")
    
    # إنشاء النافذة الرئيسية
    window = QMainWindow()
    window.setWindowTitle("🏢 نظام المحاسبة المتكامل - Accounting System")
    window.setGeometry(200, 200, 1000, 700)
    
    # تطبيق الستايل
    window.setStyleSheet("""
        QMainWindow {
            background-color: #f5f5f5;
        }
        QLabel {
            color: #2c3e50;
        }
        QPushButton {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            font-weight: bold;
            font-size: 12px;
        }
        QPushButton:hover {
            background-color: #2980b9;
        }
        QPushButton:pressed {
            background-color: #21618c;
        }
    """)
    
    # إنشاء الواجهة
    central = QWidget()
    window.setCentralWidget(central)
    layout = QVBoxLayout()
    central.setLayout(layout)
    
    # العنوان الرئيسي
    title = QLabel("🎉 نظام المحاسبة المتكامل يعمل بنجاح!")
    title.setFont(QFont("Arial", 18, QFont.Weight.Bold))
    title.setAlignment(Qt.AlignmentFlag.AlignCenter)
    title.setStyleSheet("""
        color: #2c3e50; 
        padding: 20px; 
        background-color: #ecf0f1; 
        border-radius: 10px; 
        margin: 10px;
        border: 2px solid #3498db;
    """)
    layout.addWidget(title)
    
    # معلومات النظام
    info_text = """
✅ النظام يعمل بنجاح!
✅ PyQt6 مثبت ويعمل
✅ جميع النماذج جاهزة للاستخدام

📋 النماذج المتاحة:
🛍️ إدارة المنتجات والمخزون
🧾 إدارة الفواتير والمبيعات  
👥 إدارة الموظفين والموارد البشرية
📦 إدارة المشتريات وأوامر الشراء
💰 إدارة المصروفات والنفقات
👤 إدارة المستخدمين والصلاحيات
💵 إدارة الرواتب والأجور

🔑 بيانات الدخول التجريبية:
👤 اسم المستخدم: admin
🔒 كلمة المرور: admin123
    """
    
    info = QLabel(info_text)
    info.setAlignment(Qt.AlignmentFlag.AlignCenter)
    info.setStyleSheet("""
        color: #27ae60; 
        padding: 20px; 
        font-size: 12px;
        background-color: #f8f9fa;
        border-radius: 8px;
        margin: 10px;
        border: 1px solid #dee2e6;
    """)
    layout.addWidget(info)
    
    # زر تحميل النماذج الكاملة
    load_forms_btn = QPushButton("🚀 تحميل النماذج الكاملة")
    load_forms_btn.setMinimumHeight(40)
    
    def load_full_forms():
        try:
            # محاولة تحميل النماذج
            from src.ui.forms.product_form import ProductForm
            from src.ui.forms.invoice_form import InvoiceForm
            from src.ui.forms.employee_form import EmployeeForm
            
            from PyQt6.QtWidgets import QTabWidget
            
            # إنشاء تبويبات
            tab_widget = QTabWidget()
            tab_widget.setStyleSheet("""
                QTabWidget::pane {
                    border: 1px solid #c0c0c0;
                    background-color: white;
                }
                QTabBar::tab {
                    background-color: #e0e0e0;
                    padding: 8px 16px;
                    margin-right: 2px;
                }
                QTabBar::tab:selected {
                    background-color: white;
                    border-bottom: 2px solid #3498db;
                }
            """)
            
            # إضافة النماذج
            tab_widget.addTab(ProductForm(), "🛍️ المنتجات")
            tab_widget.addTab(InvoiceForm(), "🧾 الفواتير")
            tab_widget.addTab(EmployeeForm(), "👥 الموظفين")
            
            # استبدال المحتوى
            layout.addWidget(tab_widget)
            load_forms_btn.setText("✅ تم تحميل النماذج بنجاح!")
            load_forms_btn.setEnabled(False)
            
            # تحديث النافذة
            window.setWindowTitle("🏢 نظام المحاسبة - جميع النماذج محملة")
            
        except Exception as e:
            load_forms_btn.setText(f"❌ خطأ: {str(e)[:30]}...")
            print(f"خطأ في تحميل النماذج: {e}")
    
    load_forms_btn.clicked.connect(load_full_forms)
    layout.addWidget(load_forms_btn)
    
    # عرض النافذة
    window.show()
    print("✅ تم فتح النافذة بنجاح!")
    print("💡 النظام يعمل الآن!")
    print("🎯 يمكنك استخدام الأزرار لتحميل النماذج")
    
    # تشغيل التطبيق
    sys.exit(app.exec())
    
except ImportError as e:
    print(f"❌ PyQt6 غير متاح: {e}")
    print("\n💡 لتثبيت PyQt6:")
    print("1. افتح Command Prompt")
    print("2. شغل: pip install PyQt6")
    print("3. أو: python -m pip install --user PyQt6")
    print("4. ثم شغل هذا الملف مرة أخرى")
    
except Exception as e:
    print(f"❌ خطأ عام: {e}")
    import traceback
    traceback.print_exc()

print("\n👋 انتهى التشغيل.")
