#!/usr/bin/env python3
"""
نافذة الإعدادات
Settings Dialog
"""
import sys
import os
import json

# Add parent directories to path
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(os.path.dirname(os.path.dirname(current_dir)))
sys.path.insert(0, parent_dir)

from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, 
    QPushButton, QFrame, QCheckBox, QComboBox, QSpinBox,
    QTabWidget, QWidget, QGroupBox, QGridLayout, QTextEdit,
    QColorDialog, QFontDialog, QMessageBox, QFileDialog
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont, QColor

class SettingsDialog(QDialog):
    """نافذة الإعدادات"""
    
    settings_changed = pyqtSignal(dict)  # إشارة تغيير الإعدادات
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.settings = self.load_settings()
        self.setup_ui()
        self.apply_styles()
        self.load_current_settings()
        
    def setup_ui(self):
        """إعداد واجهة الإعدادات"""
        self.setWindowTitle("⚙️ إعدادات النظام - System Settings")
        self.setFixedSize(600, 500)
        self.setModal(True)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        self.setLayout(main_layout)
        
        # عنوان النافذة
        title_label = QLabel("⚙️ إعدادات النظام")
        title_label.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        main_layout.addWidget(title_label)
        
        # التبويبات
        self.tab_widget = QTabWidget()
        main_layout.addWidget(self.tab_widget)
        
        # تبويب الإعدادات العامة
        self.setup_general_tab()
        
        # تبويب إعدادات المظهر
        self.setup_appearance_tab()
        
        # تبويب إعدادات قاعدة البيانات
        self.setup_database_tab()
        
        # تبويب إعدادات النسخ الاحتياطي
        self.setup_backup_tab()
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        
        self.save_button = QPushButton("💾 حفظ")
        self.save_button.clicked.connect(self.save_settings)
        buttons_layout.addWidget(self.save_button)
        
        self.reset_button = QPushButton("🔄 إعادة تعيين")
        self.reset_button.clicked.connect(self.reset_settings)
        buttons_layout.addWidget(self.reset_button)
        
        self.cancel_button = QPushButton("❌ إلغاء")
        self.cancel_button.clicked.connect(self.reject)
        buttons_layout.addWidget(self.cancel_button)
        
        main_layout.addLayout(buttons_layout)
    
    def setup_general_tab(self):
        """إعداد تبويب الإعدادات العامة"""
        general_widget = QWidget()
        layout = QVBoxLayout()
        general_widget.setLayout(layout)
        
        # مجموعة معلومات الشركة
        company_group = QGroupBox("🏢 معلومات الشركة")
        company_layout = QGridLayout()
        company_group.setLayout(company_layout)
        
        company_layout.addWidget(QLabel("اسم الشركة:"), 0, 0)
        self.company_name = QLineEdit()
        company_layout.addWidget(self.company_name, 0, 1)
        
        company_layout.addWidget(QLabel("العنوان:"), 1, 0)
        self.company_address = QTextEdit()
        self.company_address.setMaximumHeight(60)
        company_layout.addWidget(self.company_address, 1, 1)
        
        company_layout.addWidget(QLabel("الهاتف:"), 2, 0)
        self.company_phone = QLineEdit()
        company_layout.addWidget(self.company_phone, 2, 1)
        
        company_layout.addWidget(QLabel("البريد الإلكتروني:"), 3, 0)
        self.company_email = QLineEdit()
        company_layout.addWidget(self.company_email, 3, 1)
        
        layout.addWidget(company_group)
        
        # مجموعة الإعدادات العامة
        general_group = QGroupBox("🔧 الإعدادات العامة")
        general_layout = QGridLayout()
        general_group.setLayout(general_layout)
        
        general_layout.addWidget(QLabel("اللغة:"), 0, 0)
        self.language_combo = QComboBox()
        self.language_combo.addItems(["العربية", "English"])
        general_layout.addWidget(self.language_combo, 0, 1)
        
        general_layout.addWidget(QLabel("العملة الافتراضية:"), 1, 0)
        self.currency_combo = QComboBox()
        self.currency_combo.addItems(["ريال سعودي", "دولار أمريكي", "يورو"])
        general_layout.addWidget(self.currency_combo, 1, 1)
        
        general_layout.addWidget(QLabel("عدد الأرقام العشرية:"), 2, 0)
        self.decimal_places = QSpinBox()
        self.decimal_places.setRange(0, 6)
        self.decimal_places.setValue(2)
        general_layout.addWidget(self.decimal_places, 2, 1)
        
        self.auto_save = QCheckBox("حفظ تلقائي كل 5 دقائق")
        general_layout.addWidget(self.auto_save, 3, 0, 1, 2)
        
        self.show_splash = QCheckBox("عرض شاشة البداية")
        general_layout.addWidget(self.show_splash, 4, 0, 1, 2)
        
        layout.addWidget(general_group)
        
        self.tab_widget.addTab(general_widget, "🔧 عام")
    
    def setup_appearance_tab(self):
        """إعداد تبويب المظهر"""
        appearance_widget = QWidget()
        layout = QVBoxLayout()
        appearance_widget.setLayout(layout)
        
        # مجموعة الألوان
        colors_group = QGroupBox("🎨 الألوان")
        colors_layout = QGridLayout()
        colors_group.setLayout(colors_layout)
        
        colors_layout.addWidget(QLabel("لون الخلفية:"), 0, 0)
        self.bg_color_button = QPushButton("اختيار اللون")
        self.bg_color_button.clicked.connect(lambda: self.choose_color('background'))
        colors_layout.addWidget(self.bg_color_button, 0, 1)
        
        colors_layout.addWidget(QLabel("لون النص:"), 1, 0)
        self.text_color_button = QPushButton("اختيار اللون")
        self.text_color_button.clicked.connect(lambda: self.choose_color('text'))
        colors_layout.addWidget(self.text_color_button, 1, 1)
        
        layout.addWidget(colors_group)
        
        # مجموعة الخطوط
        fonts_group = QGroupBox("🔤 الخطوط")
        fonts_layout = QGridLayout()
        fonts_group.setLayout(fonts_layout)
        
        fonts_layout.addWidget(QLabel("خط النظام:"), 0, 0)
        self.font_button = QPushButton("اختيار الخط")
        self.font_button.clicked.connect(self.choose_font)
        fonts_layout.addWidget(self.font_button, 0, 1)
        
        fonts_layout.addWidget(QLabel("حجم الخط:"), 1, 0)
        self.font_size = QSpinBox()
        self.font_size.setRange(8, 24)
        self.font_size.setValue(12)
        fonts_layout.addWidget(self.font_size, 1, 1)
        
        layout.addWidget(fonts_group)
        
        # مجموعة النمط
        theme_group = QGroupBox("🌙 النمط")
        theme_layout = QGridLayout()
        theme_group.setLayout(theme_layout)
        
        theme_layout.addWidget(QLabel("نمط الواجهة:"), 0, 0)
        self.theme_combo = QComboBox()
        self.theme_combo.addItems(["فاتح", "داكن", "تلقائي"])
        theme_layout.addWidget(self.theme_combo, 0, 1)
        
        self.animations_enabled = QCheckBox("تفعيل الحركات والتأثيرات")
        theme_layout.addWidget(self.animations_enabled, 1, 0, 1, 2)
        
        layout.addWidget(theme_group)
        
        self.tab_widget.addTab(appearance_widget, "🎨 المظهر")
    
    def setup_database_tab(self):
        """إعداد تبويب قاعدة البيانات"""
        db_widget = QWidget()
        layout = QVBoxLayout()
        db_widget.setLayout(layout)
        
        # مجموعة إعدادات قاعدة البيانات
        db_group = QGroupBox("🗄️ إعدادات قاعدة البيانات")
        db_layout = QGridLayout()
        db_group.setLayout(db_layout)
        
        db_layout.addWidget(QLabel("نوع قاعدة البيانات:"), 0, 0)
        self.db_type_combo = QComboBox()
        self.db_type_combo.addItems(["SQLite", "MySQL", "PostgreSQL"])
        db_layout.addWidget(self.db_type_combo, 0, 1)
        
        db_layout.addWidget(QLabel("مسار قاعدة البيانات:"), 1, 0)
        self.db_path = QLineEdit()
        db_layout.addWidget(self.db_path, 1, 1)
        
        browse_button = QPushButton("📁 تصفح")
        browse_button.clicked.connect(self.browse_database)
        db_layout.addWidget(browse_button, 1, 2)
        
        self.auto_backup = QCheckBox("نسخ احتياطي تلقائي يومي")
        db_layout.addWidget(self.auto_backup, 2, 0, 1, 3)
        
        layout.addWidget(db_group)
        
        # مجموعة الصيانة
        maintenance_group = QGroupBox("🔧 صيانة قاعدة البيانات")
        maintenance_layout = QVBoxLayout()
        maintenance_group.setLayout(maintenance_layout)
        
        optimize_button = QPushButton("🚀 تحسين قاعدة البيانات")
        optimize_button.clicked.connect(self.optimize_database)
        maintenance_layout.addWidget(optimize_button)
        
        repair_button = QPushButton("🔨 إصلاح قاعدة البيانات")
        repair_button.clicked.connect(self.repair_database)
        maintenance_layout.addWidget(repair_button)
        
        layout.addWidget(maintenance_group)
        
        self.tab_widget.addTab(db_widget, "🗄️ قاعدة البيانات")
    
    def setup_backup_tab(self):
        """إعداد تبويب النسخ الاحتياطي"""
        backup_widget = QWidget()
        layout = QVBoxLayout()
        backup_widget.setLayout(layout)
        
        # مجموعة إعدادات النسخ الاحتياطي
        backup_group = QGroupBox("💾 إعدادات النسخ الاحتياطي")
        backup_layout = QGridLayout()
        backup_group.setLayout(backup_layout)
        
        backup_layout.addWidget(QLabel("مجلد النسخ الاحتياطي:"), 0, 0)
        self.backup_path = QLineEdit()
        backup_layout.addWidget(self.backup_path, 0, 1)
        
        browse_backup_button = QPushButton("📁 تصفح")
        browse_backup_button.clicked.connect(self.browse_backup_folder)
        backup_layout.addWidget(browse_backup_button, 0, 2)
        
        backup_layout.addWidget(QLabel("تكرار النسخ الاحتياطي:"), 1, 0)
        self.backup_frequency = QComboBox()
        self.backup_frequency.addItems(["يومي", "أسبوعي", "شهري", "يدوي"])
        backup_layout.addWidget(self.backup_frequency, 1, 1)
        
        backup_layout.addWidget(QLabel("عدد النسخ المحفوظة:"), 2, 0)
        self.backup_count = QSpinBox()
        self.backup_count.setRange(1, 30)
        self.backup_count.setValue(7)
        backup_layout.addWidget(self.backup_count, 2, 1)
        
        self.compress_backup = QCheckBox("ضغط النسخ الاحتياطي")
        backup_layout.addWidget(self.compress_backup, 3, 0, 1, 3)
        
        layout.addWidget(backup_group)
        
        # أزرار النسخ الاحتياطي
        backup_actions_group = QGroupBox("🔧 عمليات النسخ الاحتياطي")
        backup_actions_layout = QVBoxLayout()
        backup_actions_group.setLayout(backup_actions_layout)
        
        create_backup_button = QPushButton("💾 إنشاء نسخة احتياطية الآن")
        create_backup_button.clicked.connect(self.create_backup)
        backup_actions_layout.addWidget(create_backup_button)
        
        restore_backup_button = QPushButton("📥 استعادة من نسخة احتياطية")
        restore_backup_button.clicked.connect(self.restore_backup)
        backup_actions_layout.addWidget(restore_backup_button)
        
        layout.addWidget(backup_actions_group)
        
        self.tab_widget.addTab(backup_widget, "💾 النسخ الاحتياطي")
    
    def apply_styles(self):
        """تطبيق الأنماط"""
        self.setStyleSheet("""
            QDialog {
                background-color: #f8f9fa;
            }
            
            QTabWidget::pane {
                border: 1px solid #bdc3c7;
                background-color: white;
                border-radius: 6px;
            }
            
            QTabBar::tab {
                background-color: #ecf0f1;
                color: #2c3e50;
                padding: 10px 15px;
                margin-right: 2px;
                border-top-left-radius: 6px;
                border-top-right-radius: 6px;
                font-weight: bold;
            }
            
            QTabBar::tab:selected {
                background-color: white;
                border-bottom: 2px solid #3498db;
            }
            
            QGroupBox {
                font-weight: bold;
                border: 2px solid #bdc3c7;
                border-radius: 6px;
                margin-top: 10px;
                padding-top: 10px;
            }
            
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #2c3e50;
            }
            
            QLineEdit, QTextEdit, QComboBox, QSpinBox {
                padding: 8px;
                border: 1px solid #bdc3c7;
                border-radius: 4px;
                background-color: white;
            }
            
            QLineEdit:focus, QTextEdit:focus, QComboBox:focus, QSpinBox:focus {
                border-color: #3498db;
            }
            
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 4px;
                font-weight: bold;
            }
            
            QPushButton:hover {
                background-color: #2980b9;
            }
            
            QPushButton:pressed {
                background-color: #21618c;
            }
            
            QCheckBox {
                color: #2c3e50;
                font-weight: normal;
            }
            
            QLabel {
                color: #2c3e50;
                font-weight: normal;
            }
        """)
    
    def load_current_settings(self):
        """تحميل الإعدادات الحالية"""
        # تحميل إعدادات الشركة
        self.company_name.setText(self.settings.get('company_name', 'شركة المحاسبة'))
        self.company_address.setPlainText(self.settings.get('company_address', ''))
        self.company_phone.setText(self.settings.get('company_phone', ''))
        self.company_email.setText(self.settings.get('company_email', ''))
        
        # تحميل الإعدادات العامة
        self.language_combo.setCurrentText(self.settings.get('language', 'العربية'))
        self.currency_combo.setCurrentText(self.settings.get('currency', 'ريال سعودي'))
        self.decimal_places.setValue(self.settings.get('decimal_places', 2))
        self.auto_save.setChecked(self.settings.get('auto_save', True))
        self.show_splash.setChecked(self.settings.get('show_splash', True))
        
        # تحميل إعدادات المظهر
        self.font_size.setValue(self.settings.get('font_size', 12))
        self.theme_combo.setCurrentText(self.settings.get('theme', 'فاتح'))
        self.animations_enabled.setChecked(self.settings.get('animations', True))
        
        # تحميل إعدادات قاعدة البيانات
        self.db_type_combo.setCurrentText(self.settings.get('db_type', 'SQLite'))
        self.db_path.setText(self.settings.get('db_path', 'accounting.db'))
        self.auto_backup.setChecked(self.settings.get('auto_backup', True))
        
        # تحميل إعدادات النسخ الاحتياطي
        self.backup_path.setText(self.settings.get('backup_path', './backups'))
        self.backup_frequency.setCurrentText(self.settings.get('backup_frequency', 'يومي'))
        self.backup_count.setValue(self.settings.get('backup_count', 7))
        self.compress_backup.setChecked(self.settings.get('compress_backup', True))
    
    def choose_color(self, color_type):
        """اختيار لون"""
        color = QColorDialog.getColor()
        if color.isValid():
            if color_type == 'background':
                self.bg_color_button.setStyleSheet(f"background-color: {color.name()}")
            elif color_type == 'text':
                self.text_color_button.setStyleSheet(f"background-color: {color.name()}")
    
    def choose_font(self):
        """اختيار خط"""
        font, ok = QFontDialog.getFont()
        if ok:
            self.font_button.setText(f"{font.family()} - {font.pointSize()}pt")
    
    def browse_database(self):
        """تصفح قاعدة البيانات"""
        file_path, _ = QFileDialog.getOpenFileName(self, "اختيار قاعدة البيانات", "", "Database Files (*.db *.sqlite)")
        if file_path:
            self.db_path.setText(file_path)
    
    def browse_backup_folder(self):
        """تصفح مجلد النسخ الاحتياطي"""
        folder_path = QFileDialog.getExistingDirectory(self, "اختيار مجلد النسخ الاحتياطي")
        if folder_path:
            self.backup_path.setText(folder_path)
    
    def optimize_database(self):
        """تحسين قاعدة البيانات"""
        QMessageBox.information(self, "تحسين قاعدة البيانات", "تم تحسين قاعدة البيانات بنجاح!")
    
    def repair_database(self):
        """إصلاح قاعدة البيانات"""
        QMessageBox.information(self, "إصلاح قاعدة البيانات", "تم إصلاح قاعدة البيانات بنجاح!")
    
    def create_backup(self):
        """إنشاء نسخة احتياطية"""
        QMessageBox.information(self, "نسخة احتياطية", "تم إنشاء النسخة الاحتياطية بنجاح!")
    
    def restore_backup(self):
        """استعادة نسخة احتياطية"""
        file_path, _ = QFileDialog.getOpenFileName(self, "اختيار النسخة الاحتياطية", "", "Backup Files (*.bak *.zip)")
        if file_path:
            QMessageBox.information(self, "استعادة النسخة الاحتياطية", "تم استعادة النسخة الاحتياطية بنجاح!")
    
    def save_settings(self):
        """حفظ الإعدادات"""
        # جمع جميع الإعدادات
        new_settings = {
            # إعدادات الشركة
            'company_name': self.company_name.text(),
            'company_address': self.company_address.toPlainText(),
            'company_phone': self.company_phone.text(),
            'company_email': self.company_email.text(),
            
            # الإعدادات العامة
            'language': self.language_combo.currentText(),
            'currency': self.currency_combo.currentText(),
            'decimal_places': self.decimal_places.value(),
            'auto_save': self.auto_save.isChecked(),
            'show_splash': self.show_splash.isChecked(),
            
            # إعدادات المظهر
            'font_size': self.font_size.value(),
            'theme': self.theme_combo.currentText(),
            'animations': self.animations_enabled.isChecked(),
            
            # إعدادات قاعدة البيانات
            'db_type': self.db_type_combo.currentText(),
            'db_path': self.db_path.text(),
            'auto_backup': self.auto_backup.isChecked(),
            
            # إعدادات النسخ الاحتياطي
            'backup_path': self.backup_path.text(),
            'backup_frequency': self.backup_frequency.currentText(),
            'backup_count': self.backup_count.value(),
            'compress_backup': self.compress_backup.isChecked(),
        }
        
        # حفظ الإعدادات
        self.save_settings_to_file(new_settings)
        
        # إرسال إشارة التغيير
        self.settings_changed.emit(new_settings)
        
        QMessageBox.information(self, "حفظ الإعدادات", "تم حفظ الإعدادات بنجاح!")
        self.accept()
    
    def reset_settings(self):
        """إعادة تعيين الإعدادات"""
        reply = QMessageBox.question(self, "إعادة تعيين", 
                                    "هل أنت متأكد من إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟")
        
        if reply == QMessageBox.StandardButton.Yes:
            self.settings = self.get_default_settings()
            self.load_current_settings()
            QMessageBox.information(self, "إعادة تعيين", "تم إعادة تعيين الإعدادات بنجاح!")
    
    def load_settings(self):
        """تحميل الإعدادات من الملف"""
        try:
            settings_file = os.path.join(os.path.dirname(__file__), '..', '..', '..', 'settings.json')
            if os.path.exists(settings_file):
                with open(settings_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            print(f"Error loading settings: {e}")
        
        return self.get_default_settings()
    
    def save_settings_to_file(self, settings):
        """حفظ الإعدادات في الملف"""
        try:
            settings_file = os.path.join(os.path.dirname(__file__), '..', '..', '..', 'settings.json')
            with open(settings_file, 'w', encoding='utf-8') as f:
                json.dump(settings, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"Error saving settings: {e}")
    
    def get_default_settings(self):
        """الحصول على الإعدادات الافتراضية"""
        return {
            'company_name': 'شركة المحاسبة',
            'company_address': '',
            'company_phone': '',
            'company_email': '',
            'language': 'العربية',
            'currency': 'ريال سعودي',
            'decimal_places': 2,
            'auto_save': True,
            'show_splash': True,
            'font_size': 12,
            'theme': 'فاتح',
            'animations': True,
            'db_type': 'SQLite',
            'db_path': 'accounting.db',
            'auto_backup': True,
            'backup_path': './backups',
            'backup_frequency': 'يومي',
            'backup_count': 7,
            'compress_backup': True,
        }

# اختبار النافذة
if __name__ == "__main__":
    from PyQt6.QtWidgets import QApplication
    
    app = QApplication(sys.argv)
    
    dialog = SettingsDialog()
    
    def on_settings_changed(settings):
        print(f"تم تغيير الإعدادات: {settings}")
    
    dialog.settings_changed.connect(on_settings_changed)
    dialog.exec()
    
    sys.exit()
