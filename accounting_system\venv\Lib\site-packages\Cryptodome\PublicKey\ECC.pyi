from typing import Union, Callable, Optional, NamedTuple, List, Tuple, Dict, NamedTuple, Any

from Cryptodome.Math.Numbers import Integer

RNG = Callable[[int], bytes]

class UnsupportedEccFeature(ValueError): ...
class EccPoint(object):
    def __init__(self, x: Union[int, Integer], y: Union[int, Integer], curve: Optional[str] = ...) -> None: ...
    def set(self, point: EccPoint) -> EccPoint: ...
    def __eq__(self, point: object) -> bool: ...
    def __neg__(self) -> EccPoint: ...
    def copy(self) -> EccPoint: ...
    def is_point_at_infinity(self) -> bool: ...
    def point_at_infinity(self) -> EccPoint: ...
    @property
    def x(self) -> int: ...
    @property
    def y(self) -> int: ...
    @property
    def xy(self) -> <PERSON>ple[int, int]: ...
    def size_in_bytes(self) -> int: ...
    def size_in_bits(self) -> int: ...
    def double(self) -> EccPoint: ...
    def __iadd__(self, point: EccPoint) -> EccPoint: ...
    def __add__(self, point: EccPoint) -> EccPoint: ...
    def __imul__(self, scalar: int) -> EccPoint: ...
    def __mul__(self, scalar: int) -> EccPoint: ...

class EccKey(object):
    curve: str
    def __init__(self, *, curve: str = ..., d: int = ..., point: EccPoint = ...) -> None: ...
    def __eq__(self, other: object) -> bool: ...
    def __repr__(self) -> str: ...
    def has_private(self) -> bool: ...
    @property
    def d(self) -> int: ...
    @property
    def pointQ(self) -> EccPoint: ...
    def public_key(self) -> EccKey: ...
    def export_key(self, **kwargs: Union[str, bytes, bool]) -> Union[str,bytes]: ...


_Curve = NamedTuple("_Curve", [('p', Integer),
                               ('order', Integer),
                               ('b', Integer),
                               ('Gx', Integer),
                               ('Gy', Integer),
                               ('G', EccPoint),
                               ('modulus_bits', int),
                               ('oid', str),
                               ('context', Any),
                               ('desc', str),
                               ('openssh', Union[str, None]),
                               ])

_curves : Dict[str, _Curve]


def generate(**kwargs: Union[str, RNG]) -> EccKey: ...
def construct(**kwargs: Union[str, int]) -> EccKey: ...
def import_key(encoded: Union[bytes, str],
               passphrase: Optional[str]=None,
               curve_name:Optional[str]=None) -> EccKey: ...
def _import_ed25519_public_key(encoded: bytes) -> EccKey: ...
def _import_ed448_public_key(encoded: bytes) -> EccKey: ...
