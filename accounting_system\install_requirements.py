#!/usr/bin/env python3
"""
تثبيت المتطلبات
Install Requirements
"""
import subprocess
import sys
import os

def install_package(package):
    """تثبيت حزمة"""
    try:
        print(f"🔧 تثبيت {package}...")
        result = subprocess.run([sys.executable, "-m", "pip", "install", package, "--user"], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ تم تثبيت {package} بنجاح!")
            return True
        else:
            print(f"❌ فشل تثبيت {package}: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ خطأ في تثبيت {package}: {e}")
        return False

def test_import(module_name, package_name=None):
    """اختبار استيراد الحزمة"""
    if package_name is None:
        package_name = module_name
    
    try:
        __import__(module_name)
        print(f"✅ {package_name} متاح!")
        return True
    except ImportError:
        print(f"❌ {package_name} غير متاح")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء تثبيت المتطلبات...")
    print(f"🐍 Python: {sys.version}")
    print(f"📁 المجلد: {os.getcwd()}")
    
    # قائمة المتطلبات
    requirements = [
        ("PyQt6", "PyQt6.QtWidgets"),
        ("sqlalchemy", "sqlalchemy"),
        ("bcrypt", "bcrypt"),
        ("python-dotenv", "dotenv"),
    ]
    
    installed_count = 0
    
    for package, module in requirements:
        print(f"\n📦 معالجة {package}...")
        
        # اختبار إذا كانت مثبتة
        if test_import(module, package):
            installed_count += 1
            continue
        
        # محاولة التثبيت
        if install_package(package):
            # اختبار مرة أخرى
            if test_import(module, package):
                installed_count += 1
            else:
                print(f"⚠️ تم التثبيت لكن لا يمكن الاستيراد: {package}")
        else:
            print(f"❌ فشل تثبيت: {package}")
    
    print(f"\n📊 النتائج:")
    print(f"✅ تم تثبيت: {installed_count}/{len(requirements)}")
    
    if installed_count == len(requirements):
        print("🎉 جميع المتطلبات جاهزة!")
        print("💡 يمكنك الآن تشغيل النظام:")
        print("   python launch.py")
        return True
    else:
        print("⚠️ بعض المتطلبات مفقودة!")
        print("💡 جرب تثبيتها يدوياً:")
        for package, module in requirements:
            if not test_import(module, package):
                print(f"   pip install --user {package}")
        return False

if __name__ == "__main__":
    main()
