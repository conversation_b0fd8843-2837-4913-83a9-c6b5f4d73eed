<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🏢 نظام المحاسبة المتكامل</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .content {
            padding: 40px;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        
        .status-card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            border: 2px solid #e9ecef;
            transition: transform 0.3s ease;
        }
        
        .status-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        
        .status-card.success {
            border-color: #28a745;
            background: #d4edda;
        }
        
        .status-card.warning {
            border-color: #ffc107;
            background: #fff3cd;
        }
        
        .status-card.error {
            border-color: #dc3545;
            background: #f8d7da;
        }
        
        .status-icon {
            font-size: 3em;
            margin-bottom: 15px;
        }
        
        .status-title {
            font-size: 1.3em;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .modules-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        
        .module-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }
        
        .module-card:hover {
            border-color: #3498db;
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(52, 152, 219, 0.2);
        }
        
        .module-icon {
            font-size: 2.5em;
            margin-bottom: 15px;
        }
        
        .module-title {
            font-size: 1.4em;
            font-weight: bold;
            margin-bottom: 10px;
            color: #2c3e50;
        }
        
        .module-desc {
            color: #6c757d;
            line-height: 1.6;
        }
        
        .install-section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
        }
        
        .install-title {
            font-size: 1.8em;
            font-weight: bold;
            margin-bottom: 20px;
            color: #2c3e50;
            text-align: center;
        }
        
        .install-steps {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }
        
        .install-step {
            background: white;
            border-radius: 10px;
            padding: 20px;
            border-left: 5px solid #3498db;
        }
        
        .step-number {
            background: #3498db;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-bottom: 15px;
        }
        
        .code-block {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            overflow-x: auto;
        }
        
        .btn {
            display: inline-block;
            padding: 12px 25px;
            background: #3498db;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: bold;
            transition: background 0.3s ease;
            margin: 5px;
        }
        
        .btn:hover {
            background: #2980b9;
        }
        
        .btn-success {
            background: #28a745;
        }
        
        .btn-success:hover {
            background: #218838;
        }
        
        .footer {
            background: #2c3e50;
            color: white;
            padding: 20px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏢 نظام المحاسبة المتكامل</h1>
            <p>Comprehensive Accounting System</p>
        </div>
        
        <div class="content">
            <!-- حالة النظام -->
            <div class="status-grid">
                <div class="status-card success">
                    <div class="status-icon">✅</div>
                    <div class="status-title">النظام مكتمل</div>
                    <div>جميع النماذج السبعة جاهزة</div>
                </div>
                
                <div class="status-card success">
                    <div class="status-icon">🗄️</div>
                    <div class="status-title">قاعدة البيانات</div>
                    <div>SQLAlchemy مثبت ويعمل</div>
                </div>
                
                <div class="status-card success">
                    <div class="status-icon">🔒</div>
                    <div class="status-title">الأمان</div>
                    <div>bcrypt مثبت ويعمل</div>
                </div>
                
                <div class="status-card error">
                    <div class="status-icon">❌</div>
                    <div class="status-title">PyQt6</div>
                    <div>مطلوب للواجهة الرسومية</div>
                </div>
            </div>
            
            <!-- خطوات التثبيت -->
            <div class="install-section">
                <div class="install-title">🔧 خطوات التثبيت والتشغيل</div>
                
                <div class="install-steps">
                    <div class="install-step">
                        <div class="step-number">1</div>
                        <h3>افتح Command Prompt</h3>
                        <p>اضغط Win+R واكتب cmd</p>
                    </div>
                    
                    <div class="install-step">
                        <div class="step-number">2</div>
                        <h3>انتقل للمجلد</h3>
                        <div class="code-block">cd C:\Users\<USER>\Bob\accounting_system</div>
                    </div>
                    
                    <div class="install-step">
                        <div class="step-number">3</div>
                        <h3>ثبت PyQt6</h3>
                        <div class="code-block">pip install PyQt6</div>
                    </div>
                    
                    <div class="install-step">
                        <div class="step-number">4</div>
                        <h3>شغل النظام</h3>
                        <div class="code-block">python final_launch.py</div>
                    </div>
                </div>
            </div>
            
            <!-- النماذج المتاحة -->
            <h2 style="text-align: center; margin-bottom: 30px; color: #2c3e50;">📋 النماذج المتاحة</h2>
            
            <div class="modules-grid">
                <div class="module-card">
                    <div class="module-icon">🛍️</div>
                    <div class="module-title">إدارة المنتجات</div>
                    <div class="module-desc">إدارة المنتجات والمخزون مع تتبع الكميات وتنبيهات المخزون المنخفض</div>
                </div>
                
                <div class="module-card">
                    <div class="module-icon">🧾</div>
                    <div class="module-title">إدارة الفواتير</div>
                    <div class="module-desc">إنشاء وإدارة الفواتير مع حساب تلقائي للمجاميع والضرائب</div>
                </div>
                
                <div class="module-card">
                    <div class="module-icon">👥</div>
                    <div class="module-title">إدارة الموظفين</div>
                    <div class="module-desc">إدارة بيانات الموظفين الشخصية والوظيفية والمالية</div>
                </div>
                
                <div class="module-card">
                    <div class="module-icon">📦</div>
                    <div class="module-title">إدارة المشتريات</div>
                    <div class="module-desc">إنشاء أوامر الشراء واستلام البضائع وتحديث المخزون</div>
                </div>
                
                <div class="module-card">
                    <div class="module-icon">💰</div>
                    <div class="module-title">إدارة المصروفات</div>
                    <div class="module-desc">تسجيل وتصنيف المصروفات مع ربطها بالموظفين والمشاريع</div>
                </div>
                
                <div class="module-card">
                    <div class="module-icon">👤</div>
                    <div class="module-title">إدارة المستخدمين</div>
                    <div class="module-desc">إدارة المستخدمين والأدوار والصلاحيات مع تشفير آمن</div>
                </div>
                
                <div class="module-card">
                    <div class="module-icon">💵</div>
                    <div class="module-title">إدارة الرواتب</div>
                    <div class="module-desc">معالجة الرواتب الشهرية مع البدلات والخصومات</div>
                </div>
            </div>
            
            <!-- بيانات الدخول -->
            <div class="install-section">
                <div class="install-title">🔑 بيانات الدخول التجريبية</div>
                <div style="text-align: center; font-size: 1.2em;">
                    <p><strong>اسم المستخدم:</strong> <code>admin</code></p>
                    <p><strong>كلمة المرور:</strong> <code>admin123</code></p>
                </div>
            </div>
            
            <!-- أزرار التشغيل -->
            <div style="text-align: center; margin-top: 30px;">
                <a href="file:///C:/Users/<USER>/Bob/accounting_system/PYQT6_INSTALL_GUIDE.md" class="btn">📖 دليل التثبيت الشامل</a>
                <a href="file:///C:/Users/<USER>/Bob/accounting_system/SETUP_GUIDE.md" class="btn">🔧 دليل الإعداد</a>
                <a href="file:///C:/Users/<USER>/Bob/accounting_system" class="btn btn-success">📁 فتح مجلد النظام</a>
            </div>
        </div>
        
        <div class="footer">
            <p>🎉 نظام المحاسبة المتكامل - جاهز للاستخدام بعد تثبيت PyQt6</p>
        </div>
    </div>
</body>
</html>
