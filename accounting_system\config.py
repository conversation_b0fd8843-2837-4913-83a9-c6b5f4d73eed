#!/usr/bin/env python3
"""
ملف التكوين الرئيسي لنظام المحاسبة
Main Configuration File for Accounting System
"""
import os
from pathlib import Path

# مسارات المشروع
BASE_DIR = Path(__file__).parent
SRC_DIR = BASE_DIR / "src"
DATA_DIR = BASE_DIR / "data"
BACKUP_DIR = BASE_DIR / "backups"
REPORTS_DIR = BASE_DIR / "reports"
LOGS_DIR = BASE_DIR / "logs"

# إنشاء المجلدات إذا لم تكن موجودة
for directory in [DATA_DIR, BACKUP_DIR, REPORTS_DIR, LOGS_DIR]:
    directory.mkdir(exist_ok=True)

# إعدادات قاعدة البيانات
DATABASE_CONFIG = {
    'type': 'sqlite',
    'name': 'accounting.db',
    'path': DATA_DIR / 'accounting.db',
    'backup_path': BACKUP_DIR,
    'echo': False  # تفعيل لرؤية استعلامات SQL
}

# إعدادات التطبيق
APP_CONFIG = {
    'name': 'نظام المحاسبة المتكامل',
    'version': '1.0.0',
    'author': 'فريق التطوير',
    'description': 'نظام محاسبة شامل لإدارة الأعمال',
    'window_title': '🏢 نظام المحاسبة المتكامل - Comprehensive Accounting System',
    'window_size': (1400, 900),
    'min_window_size': (1200, 800),
    'icon_path': SRC_DIR / 'assets' / 'icon.png'
}

# إعدادات الواجهة
UI_CONFIG = {
    'theme': 'light',  # light, dark, auto
    'language': 'ar',  # ar, en
    'font_family': 'Arial',
    'font_size': 12,
    'show_splash': True,
    'splash_duration': 2000,  # milliseconds
    'auto_save_interval': 300000,  # 5 minutes in milliseconds
}

# إعدادات الأمان
SECURITY_CONFIG = {
    'session_timeout': 3600,  # 1 hour in seconds
    'max_login_attempts': 3,
    'password_min_length': 6,
    'require_password_change': False,
    'encrypt_database': False
}

# إعدادات النسخ الاحتياطي
BACKUP_CONFIG = {
    'auto_backup': True,
    'backup_interval': 'daily',  # daily, weekly, monthly
    'max_backups': 7,
    'compress_backups': True,
    'backup_format': 'zip'
}

# إعدادات التقارير
REPORTS_CONFIG = {
    'default_format': 'pdf',  # pdf, excel, csv
    'include_logo': True,
    'page_size': 'A4',
    'orientation': 'portrait',
    'margins': {
        'top': 2.5,
        'bottom': 2.5,
        'left': 2.0,
        'right': 2.0
    }
}

# إعدادات السجلات
LOGGING_CONFIG = {
    'level': 'INFO',  # DEBUG, INFO, WARNING, ERROR, CRITICAL
    'file_path': LOGS_DIR / 'accounting.log',
    'max_file_size': 10 * 1024 * 1024,  # 10 MB
    'backup_count': 5,
    'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
}

# إعدادات الشركة الافتراضية
COMPANY_CONFIG = {
    'name': 'شركة المحاسبة',
    'address': '',
    'phone': '',
    'email': '',
    'website': '',
    'tax_number': '',
    'commercial_register': '',
    'currency': 'SAR',
    'currency_symbol': 'ر.س',
    'decimal_places': 2
}

# إعدادات النظام المالي
FINANCIAL_CONFIG = {
    'fiscal_year_start': '01-01',  # MM-DD format
    'default_tax_rate': 15.0,
    'allow_negative_inventory': False,
    'auto_generate_codes': True,
    'invoice_prefix': 'INV',
    'purchase_prefix': 'PO',
    'expense_prefix': 'EXP',
    'receipt_prefix': 'REC'
}

# رسائل النظام
MESSAGES = {
    'ar': {
        'welcome': 'مرحباً بك في نظام المحاسبة المتكامل',
        'login_success': 'تم تسجيل الدخول بنجاح',
        'login_failed': 'فشل في تسجيل الدخول',
        'save_success': 'تم الحفظ بنجاح',
        'delete_confirm': 'هل أنت متأكد من الحذف؟',
        'operation_success': 'تمت العملية بنجاح',
        'operation_failed': 'فشلت العملية',
        'required_field': 'هذا الحقل مطلوب',
        'invalid_data': 'البيانات غير صحيحة',
        'access_denied': 'ليس لديك صلاحية للوصول'
    },
    'en': {
        'welcome': 'Welcome to Comprehensive Accounting System',
        'login_success': 'Login successful',
        'login_failed': 'Login failed',
        'save_success': 'Saved successfully',
        'delete_confirm': 'Are you sure you want to delete?',
        'operation_success': 'Operation completed successfully',
        'operation_failed': 'Operation failed',
        'required_field': 'This field is required',
        'invalid_data': 'Invalid data',
        'access_denied': 'Access denied'
    }
}

# أذونات النظام
PERMISSIONS = {
    'admin': [
        'view_all', 'create_all', 'edit_all', 'delete_all',
        'manage_users', 'manage_settings', 'view_reports',
        'backup_restore', 'system_maintenance'
    ],
    'accountant': [
        'view_financial', 'create_financial', 'edit_financial',
        'view_reports', 'manage_invoices', 'manage_expenses'
    ],
    'sales': [
        'view_sales', 'create_sales', 'edit_sales',
        'manage_customers', 'manage_products'
    ],
    'hr': [
        'view_hr', 'create_hr', 'edit_hr',
        'manage_employees', 'manage_salaries'
    ],
    'viewer': [
        'view_basic', 'view_reports'
    ]
}

def get_database_url():
    """الحصول على رابط قاعدة البيانات"""
    if DATABASE_CONFIG['type'] == 'sqlite':
        return f"sqlite:///{DATABASE_CONFIG['path']}"
    elif DATABASE_CONFIG['type'] == 'mysql':
        return f"mysql+pymysql://{DATABASE_CONFIG.get('user')}:{DATABASE_CONFIG.get('password')}@{DATABASE_CONFIG.get('host')}/{DATABASE_CONFIG.get('name')}"
    elif DATABASE_CONFIG['type'] == 'postgresql':
        return f"postgresql://{DATABASE_CONFIG.get('user')}:{DATABASE_CONFIG.get('password')}@{DATABASE_CONFIG.get('host')}/{DATABASE_CONFIG.get('name')}"
    else:
        raise ValueError(f"Unsupported database type: {DATABASE_CONFIG['type']}")

def get_message(key, language=None):
    """الحصول على رسالة بلغة محددة"""
    if language is None:
        language = UI_CONFIG['language']
    
    return MESSAGES.get(language, {}).get(key, key)

def has_permission(user_role, permission):
    """التحقق من وجود صلاحية"""
    user_permissions = PERMISSIONS.get(user_role, [])
    return permission in user_permissions or 'view_all' in user_permissions

# تصدير الإعدادات المهمة
__all__ = [
    'BASE_DIR', 'SRC_DIR', 'DATA_DIR', 'BACKUP_DIR', 'REPORTS_DIR', 'LOGS_DIR',
    'DATABASE_CONFIG', 'APP_CONFIG', 'UI_CONFIG', 'SECURITY_CONFIG',
    'BACKUP_CONFIG', 'REPORTS_CONFIG', 'LOGGING_CONFIG', 'COMPANY_CONFIG',
    'FINANCIAL_CONFIG', 'MESSAGES', 'PERMISSIONS',
    'get_database_url', 'get_message', 'has_permission'
]
