"""
نموذج الفواتير المحسن
Enhanced Invoice Form
"""
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
    QLabel, QLineEdit, QTextEdit, QFormLayout,
    QTableWidget, QTableWidgetItem, QMessageBox,
    QDateEdit, QDoubleSpinBox, QComboBox, QSpinBox,
    QHeaderView, QAbstractItemView, QDialog, QDialogButtonBox,
    QGroupBox, QGridLayout, QTabWidget, QCheckBox
)
from PyQt6.QtCore import Qt, QDate
from PyQt6.QtGui import QFont
from src.database.database import SessionLocal
from src.models.customers import Customer
from src.models.transactions import Invoice, InvoiceItem, DocumentStatus, PaymentStatus
from src.models.inventory import Product
from src.utils.translation import _
from datetime import datetime

class InvoiceForm(QWidget):
    """Enhanced Invoice form class."""

    def __init__(self):
        super().__init__()
        self.current_invoice = None
        self._init_ui()
        self._load_data()

    def _init_ui(self):
        """Initialize the enhanced UI."""
        layout = QVBoxLayout()
        self.setLayout(layout)

        # Title
        title = QLabel(_('Invoice Management'))
        title.setFont(QFont('Arial', 16, QFont.Weight.Bold))
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title)

        # Search and filter section
        search_group = QGroupBox(_('Search & Filter'))
        search_layout = QGridLayout()
        search_group.setLayout(search_layout)

        # Search box
        search_layout.addWidget(QLabel(_('Search') + ':'), 0, 0)
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText(_('Search by invoice number or customer...'))
        self.search_input.textChanged.connect(self._filter_invoices)
        search_layout.addWidget(self.search_input, 0, 1)

        # Date filter
        search_layout.addWidget(QLabel(_('From Date') + ':'), 0, 2)
        self.from_date = QDateEdit()
        self.from_date.setCalendarPopup(True)
        self.from_date.setDate(QDate.currentDate().addDays(-30))
        search_layout.addWidget(self.from_date, 0, 3)

        search_layout.addWidget(QLabel(_('To Date') + ':'), 0, 4)
        self.to_date = QDateEdit()
        self.to_date.setCalendarPopup(True)
        self.to_date.setDate(QDate.currentDate())
        search_layout.addWidget(self.to_date, 0, 5)

        # Status filter
        search_layout.addWidget(QLabel(_('Status') + ':'), 1, 0)
        self.status_filter = QComboBox()
        self.status_filter.addItem(_('All Statuses'))
        self.status_filter.addItems([status.value for status in DocumentStatus])
        self.status_filter.currentTextChanged.connect(self._filter_invoices)
        search_layout.addWidget(self.status_filter, 1, 1)

        layout.addWidget(search_group)

        # Buttons section
        buttons_layout = QHBoxLayout()

        self.new_btn = QPushButton(_('New Invoice'))
        self.new_btn.clicked.connect(self._new_invoice)
        buttons_layout.addWidget(self.new_btn)

        self.edit_btn = QPushButton(_('Edit Invoice'))
        self.edit_btn.clicked.connect(self._edit_invoice)
        self.edit_btn.setEnabled(False)
        buttons_layout.addWidget(self.edit_btn)

        self.delete_btn = QPushButton(_('Delete Invoice'))
        self.delete_btn.clicked.connect(self._delete_invoice)
        self.delete_btn.setEnabled(False)
        buttons_layout.addWidget(self.delete_btn)

        self.print_btn = QPushButton(_('Print Invoice'))
        self.print_btn.clicked.connect(self._print_invoice)
        self.print_btn.setEnabled(False)
        buttons_layout.addWidget(self.print_btn)

        self.refresh_btn = QPushButton(_('Refresh'))
        self.refresh_btn.clicked.connect(self._load_data)
        buttons_layout.addWidget(self.refresh_btn)

        buttons_layout.addStretch()
        layout.addLayout(buttons_layout)

        # Invoices table
        self.table = QTableWidget()
        self.table.setColumnCount(7)
        self.table.setHorizontalHeaderLabels([
            _('Invoice No'), _('Date'), _('Customer'), _('Subtotal'),
            _('Tax'), _('Total'), _('Status')
        ])

        # Configure table
        header = self.table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        self.table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.table.setAlternatingRowColors(True)
        self.table.itemSelectionChanged.connect(self._on_selection_changed)
        self.table.itemDoubleClicked.connect(self._edit_invoice)

        layout.addWidget(self.table)

    def _on_selection_changed(self):
        """Handle selection change."""
        has_selection = len(self.table.selectedItems()) > 0
        self.edit_btn.setEnabled(has_selection)
        self.delete_btn.setEnabled(has_selection)
        self.print_btn.setEnabled(has_selection)

    def _load_data(self):
        """Load invoices data."""
        self._load_invoices()

    def _load_invoices(self):
        """Load invoices into table."""
        session = SessionLocal()
        try:
            invoices = session.query(Invoice).all()
            self.table.setRowCount(len(invoices))

            for i, invoice in enumerate(invoices):
                # Invoice number
                self.table.setItem(i, 0, QTableWidgetItem(invoice.invoice_number))

                # Date
                date_str = invoice.invoice_date.strftime('%Y-%m-%d') if invoice.invoice_date else ''
                self.table.setItem(i, 1, QTableWidgetItem(date_str))

                # Customer
                customer_name = invoice.customer.name if invoice.customer else _('No Customer')
                self.table.setItem(i, 2, QTableWidgetItem(customer_name))

                # Subtotal
                self.table.setItem(i, 3, QTableWidgetItem(f"{invoice.subtotal:.2f}"))

                # Tax
                self.table.setItem(i, 4, QTableWidgetItem(f"{invoice.tax_amount:.2f}"))

                # Total
                self.table.setItem(i, 5, QTableWidgetItem(f"{invoice.total_amount:.2f}"))

                # Status
                status_text = invoice.status.value if invoice.status else _('Draft')
                self.table.setItem(i, 6, QTableWidgetItem(status_text))

                # Store invoice ID in first column
                self.table.item(i, 0).setData(Qt.ItemDataRole.UserRole, invoice.id)

        finally:
            session.close()

    def _filter_invoices(self):
        """Filter invoices based on search criteria."""
        search_text = self.search_input.text().lower()
        status_filter = self.status_filter.currentText()

        for row in range(self.table.rowCount()):
            show_row = True

            # Search filter
            if search_text:
                invoice_no = self.table.item(row, 0).text().lower()
                customer = self.table.item(row, 2).text().lower()
                if search_text not in invoice_no and search_text not in customer:
                    show_row = False

            # Status filter
            if status_filter != _('All Statuses'):
                status = self.table.item(row, 6).text()
                if status != status_filter:
                    show_row = False

            self.table.setRowHidden(row, not show_row)

    def _new_invoice(self):
        """Create new invoice."""
        dialog = InvoiceDialog(parent=self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            if dialog.validate_data():
                self._save_invoice_data(dialog.get_invoice_data())

    def _edit_invoice(self):
        """Edit selected invoice."""
        current_row = self.table.currentRow()
        if current_row < 0:
            return

        invoice_id = self.table.item(current_row, 0).data(Qt.ItemDataRole.UserRole)

        session = SessionLocal()
        try:
            invoice = session.query(Invoice).get(invoice_id)
            if invoice:
                dialog = InvoiceDialog(invoice, parent=self)
                if dialog.exec() == QDialog.DialogCode.Accepted:
                    if dialog.validate_data():
                        self._update_invoice_data(invoice, dialog.get_invoice_data())
        finally:
            session.close()

    def _delete_invoice(self):
        """Delete selected invoice."""
        current_row = self.table.currentRow()
        if current_row < 0:
            return

        invoice_no = self.table.item(current_row, 0).text()

        reply = QMessageBox.question(
            self, _('Confirm Delete'),
            _('Are you sure you want to delete invoice: ') + invoice_no + '?',
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            invoice_id = self.table.item(current_row, 0).data(Qt.ItemDataRole.UserRole)

            session = SessionLocal()
            try:
                invoice = session.query(Invoice).get(invoice_id)
                if invoice:
                    # Delete invoice items first
                    for item in invoice.items:
                        session.delete(item)
                    # Delete invoice
                    session.delete(invoice)
                    session.commit()
                    QMessageBox.information(self, _('Success'), _('Invoice deleted successfully'))
                    self._load_data()
            except Exception as e:
                session.rollback()
                QMessageBox.critical(self, _('Error'), _('Failed to delete invoice: ') + str(e))
            finally:
                session.close()

    def _print_invoice(self):
        """Print selected invoice."""
        current_row = self.table.currentRow()
        if current_row < 0:
            return

        invoice_no = self.table.item(current_row, 0).text()
        QMessageBox.information(self, _('Print'), _('Printing invoice: ') + invoice_no)
        # TODO: Implement actual printing functionality

    def _save_invoice_data(self, data):
        """Save new invoice."""
        session = SessionLocal()
        try:
            # Generate invoice number if not provided
            if not data['invoice_number']:
                last_invoice = session.query(Invoice).order_by(Invoice.id.desc()).first()
                if last_invoice:
                    last_num = int(last_invoice.invoice_number.split('-')[-1]) if '-' in last_invoice.invoice_number else 0
                    data['invoice_number'] = f"INV-{last_num + 1:06d}"
                else:
                    data['invoice_number'] = "INV-000001"

            invoice = Invoice(
                invoice_number=data['invoice_number'],
                invoice_date=data['invoice_date'],
                customer_id=data['customer_id'],
                subtotal=data['subtotal'],
                tax_amount=data['tax_amount'],
                discount_amount=data['discount_amount'],
                total_amount=data['total_amount'],
                status=data['status'],
                notes=data['notes']
            )

            session.add(invoice)
            session.flush()  # Get invoice ID

            # Add invoice items
            for item_data in data['items']:
                item = InvoiceItem(
                    invoice_id=invoice.id,
                    product_id=item_data['product_id'],
                    quantity=item_data['quantity'],
                    unit_price=item_data['unit_price'],
                    tax_rate=item_data['tax_rate'],
                    line_total=item_data['line_total']
                )
                session.add(item)

            session.commit()
            QMessageBox.information(self, _('Success'), _('Invoice saved successfully'))
            self._load_data()

        except Exception as e:
            session.rollback()
            QMessageBox.critical(self, _('Error'), _('Failed to save invoice: ') + str(e))
        finally:
            session.close()

    def _update_invoice_data(self, invoice, data):
        """Update existing invoice."""
        session = SessionLocal()
        try:
            # Update invoice fields
            invoice.invoice_number = data['invoice_number']
            invoice.invoice_date = data['invoice_date']
            invoice.customer_id = data['customer_id']
            invoice.subtotal = data['subtotal']
            invoice.tax_amount = data['tax_amount']
            invoice.discount_amount = data['discount_amount']
            invoice.total_amount = data['total_amount']
            invoice.status = data['status']
            invoice.notes = data['notes']

            # Delete existing items
            for item in invoice.items:
                session.delete(item)

            # Add new items
            for item_data in data['items']:
                item = InvoiceItem(
                    invoice_id=invoice.id,
                    product_id=item_data['product_id'],
                    quantity=item_data['quantity'],
                    unit_price=item_data['unit_price'],
                    tax_rate=item_data['tax_rate'],
                    line_total=item_data['line_total']
                )
                session.add(item)

            session.commit()
            QMessageBox.information(self, _('Success'), _('Invoice updated successfully'))
            self._load_data()

        except Exception as e:
            session.rollback()
            QMessageBox.critical(self, _('Error'), _('Failed to update invoice: ') + str(e))
        finally:
            session.close()


class InvoiceDialog(QDialog):
    """Dialog for adding/editing invoices."""

    def __init__(self, invoice=None, parent=None):
        super().__init__(parent)
        self.invoice = invoice
        self.setWindowTitle(_('New Invoice') if invoice is None else _('Edit Invoice'))
        self.setModal(True)
        self.setMinimumSize(800, 700)

        self.setup_ui()
        if invoice:
            self.load_invoice_data()
        else:
            self.generate_invoice_number()

    def setup_ui(self):
        """Setup the dialog UI."""
        layout = QVBoxLayout()

        # Create tabs
        tab_widget = QTabWidget()

        # Invoice Header Tab
        header_tab = QWidget()
        header_layout = QFormLayout()
        header_tab.setLayout(header_layout)

        # Invoice number
        self.invoice_number_edit = QLineEdit()
        self.invoice_number_edit.setReadOnly(True)
        header_layout.addRow(_('Invoice Number') + ':', self.invoice_number_edit)

        # Date
        self.date_edit = QDateEdit()
        self.date_edit.setCalendarPopup(True)
        self.date_edit.setDate(QDate.currentDate())
        header_layout.addRow(_('Date') + '*:', self.date_edit)

        # Customer
        self.customer_combo = QComboBox()
        self.load_customers()
        header_layout.addRow(_('Customer') + '*:', self.customer_combo)

        # Notes
        self.notes_edit = QTextEdit()
        self.notes_edit.setMaximumHeight(80)
        header_layout.addRow(_('Notes') + ':', self.notes_edit)

        # Status
        self.status_combo = QComboBox()
        self.status_combo.addItems([status.value for status in DocumentStatus])
        header_layout.addRow(_('Status') + ':', self.status_combo)

        tab_widget.addTab(header_tab, _('Invoice Header'))

        # Invoice Items Tab
        items_tab = QWidget()
        items_layout = QVBoxLayout()
        items_tab.setLayout(items_layout)

        # Items toolbar
        items_toolbar = QHBoxLayout()

        self.add_item_btn = QPushButton(_('Add Item'))
        self.add_item_btn.clicked.connect(self.add_item)
        items_toolbar.addWidget(self.add_item_btn)

        self.remove_item_btn = QPushButton(_('Remove Item'))
        self.remove_item_btn.clicked.connect(self.remove_item)
        items_toolbar.addWidget(self.remove_item_btn)

        items_toolbar.addStretch()
        items_layout.addLayout(items_toolbar)

        # Items table
        self.items_table = QTableWidget()
        self.items_table.setColumnCount(6)
        self.items_table.setHorizontalHeaderLabels([
            _('Product'), _('Quantity'), _('Unit Price'),
            _('Tax Rate') + ' %', _('Tax Amount'), _('Line Total')
        ])

        # Configure items table
        header = self.items_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        self.items_table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)

        items_layout.addWidget(self.items_table)

        # Totals section
        totals_group = QGroupBox(_('Totals'))
        totals_layout = QFormLayout()
        totals_group.setLayout(totals_layout)

        self.subtotal_edit = QLineEdit()
        self.subtotal_edit.setReadOnly(True)
        totals_layout.addRow(_('Subtotal') + ':', self.subtotal_edit)

        self.discount_spin = QDoubleSpinBox()
        self.discount_spin.setMaximum(999999.99)
        self.discount_spin.setDecimals(2)
        self.discount_spin.valueChanged.connect(self.calculate_totals)
        totals_layout.addRow(_('Discount') + ':', self.discount_spin)

        self.tax_amount_edit = QLineEdit()
        self.tax_amount_edit.setReadOnly(True)
        totals_layout.addRow(_('Tax Amount') + ':', self.tax_amount_edit)

        self.total_edit = QLineEdit()
        self.total_edit.setReadOnly(True)
        totals_layout.addRow(_('Total Amount') + ':', self.total_edit)

        items_layout.addWidget(totals_group)

        tab_widget.addTab(items_tab, _('Invoice Items'))

        layout.addWidget(tab_widget)

        # Buttons
        button_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok |
                                     QDialogButtonBox.StandardButton.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)

        self.setLayout(layout)

    def load_customers(self):
        """Load customers."""
        session = SessionLocal()
        try:
            customers = session.query(Customer).filter(Customer.is_active == True).all()
            self.customer_combo.addItem(_('Select Customer'), None)
            for customer in customers:
                self.customer_combo.addItem(customer.name, customer.id)
        finally:
            session.close()

    def generate_invoice_number(self):
        """Generate new invoice number."""
        session = SessionLocal()
        try:
            last_invoice = session.query(Invoice).order_by(Invoice.id.desc()).first()
            if last_invoice:
                last_num = int(last_invoice.invoice_number.split('-')[-1]) if '-' in last_invoice.invoice_number else 0
                new_number = f"INV-{last_num + 1:06d}"
            else:
                new_number = "INV-000001"
            self.invoice_number_edit.setText(new_number)
        finally:
            session.close()

    def add_item(self):
        """Add new item to invoice."""
        dialog = InvoiceItemDialog(parent=self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            item_data = dialog.get_item_data()

            row = self.items_table.rowCount()
            self.items_table.insertRow(row)

            # Product
            product_item = QTableWidgetItem(item_data['product_name'])
            product_item.setData(Qt.ItemDataRole.UserRole, item_data['product_id'])
            self.items_table.setItem(row, 0, product_item)

            # Quantity
            self.items_table.setItem(row, 1, QTableWidgetItem(str(item_data['quantity'])))

            # Unit Price
            self.items_table.setItem(row, 2, QTableWidgetItem(f"{item_data['unit_price']:.2f}"))

            # Tax Rate
            self.items_table.setItem(row, 3, QTableWidgetItem(f"{item_data['tax_rate']:.2f}"))

            # Tax Amount
            tax_amount = (item_data['quantity'] * item_data['unit_price'] * item_data['tax_rate']) / 100
            self.items_table.setItem(row, 4, QTableWidgetItem(f"{tax_amount:.2f}"))

            # Line Total
            line_total = (item_data['quantity'] * item_data['unit_price']) + tax_amount
            self.items_table.setItem(row, 5, QTableWidgetItem(f"{line_total:.2f}"))

            self.calculate_totals()

    def remove_item(self):
        """Remove selected item."""
        current_row = self.items_table.currentRow()
        if current_row >= 0:
            self.items_table.removeRow(current_row)
            self.calculate_totals()

    def calculate_totals(self):
        """Calculate invoice totals."""
        subtotal = 0.0
        tax_amount = 0.0

        for row in range(self.items_table.rowCount()):
            line_total_item = self.items_table.item(row, 5)
            tax_amount_item = self.items_table.item(row, 4)

            if line_total_item:
                line_total = float(line_total_item.text())
                subtotal += line_total

            if tax_amount_item:
                tax_amount += float(tax_amount_item.text())

        discount = self.discount_spin.value()
        total = subtotal - discount

        self.subtotal_edit.setText(f"{subtotal:.2f}")
        self.tax_amount_edit.setText(f"{tax_amount:.2f}")
        self.total_edit.setText(f"{total:.2f}")

    def load_invoice_data(self):
        """Load invoice data for editing."""
        if not self.invoice:
            return

        self.invoice_number_edit.setText(self.invoice.invoice_number)
        self.date_edit.setDate(QDate.fromString(self.invoice.invoice_date.strftime('%Y-%m-%d'), 'yyyy-MM-dd'))
        self.notes_edit.setPlainText(self.invoice.notes or '')
        self.discount_spin.setValue(self.invoice.discount_amount or 0)

        # Set customer
        if self.invoice.customer_id:
            for i in range(self.customer_combo.count()):
                if self.customer_combo.itemData(i) == self.invoice.customer_id:
                    self.customer_combo.setCurrentIndex(i)
                    break

        # Set status
        if self.invoice.status:
            status_index = self.status_combo.findText(self.invoice.status.value)
            if status_index >= 0:
                self.status_combo.setCurrentIndex(status_index)

        # Load items
        session = SessionLocal()
        try:
            for item in self.invoice.items:
                row = self.items_table.rowCount()
                self.items_table.insertRow(row)

                # Product
                product_name = item.product.name if item.product else _('Unknown Product')
                product_item = QTableWidgetItem(product_name)
                product_item.setData(Qt.ItemDataRole.UserRole, item.product_id)
                self.items_table.setItem(row, 0, product_item)

                # Quantity
                self.items_table.setItem(row, 1, QTableWidgetItem(str(item.quantity)))

                # Unit Price
                self.items_table.setItem(row, 2, QTableWidgetItem(f"{item.unit_price:.2f}"))

                # Tax Rate
                self.items_table.setItem(row, 3, QTableWidgetItem(f"{item.tax_rate:.2f}"))

                # Tax Amount
                tax_amount = (item.quantity * item.unit_price * item.tax_rate) / 100
                self.items_table.setItem(row, 4, QTableWidgetItem(f"{tax_amount:.2f}"))

                # Line Total
                self.items_table.setItem(row, 5, QTableWidgetItem(f"{item.line_total:.2f}"))
        finally:
            session.close()

        self.calculate_totals()

    def get_invoice_data(self):
        """Get invoice data from form."""
        items = []
        for row in range(self.items_table.rowCount()):
            product_id = self.items_table.item(row, 0).data(Qt.ItemDataRole.UserRole)
            quantity = float(self.items_table.item(row, 1).text())
            unit_price = float(self.items_table.item(row, 2).text())
            tax_rate = float(self.items_table.item(row, 3).text())
            line_total = float(self.items_table.item(row, 5).text())

            items.append({
                'product_id': product_id,
                'quantity': quantity,
                'unit_price': unit_price,
                'tax_rate': tax_rate,
                'line_total': line_total
            })

        return {
            'invoice_number': self.invoice_number_edit.text().strip(),
            'invoice_date': self.date_edit.date().toPython(),
            'customer_id': self.customer_combo.currentData(),
            'notes': self.notes_edit.toPlainText().strip(),
            'subtotal': float(self.subtotal_edit.text() or 0),
            'tax_amount': float(self.tax_amount_edit.text() or 0),
            'discount_amount': self.discount_spin.value(),
            'total_amount': float(self.total_edit.text() or 0),
            'status': DocumentStatus(self.status_combo.currentText()),
            'items': items
        }

    def validate_data(self):
        """Validate form data."""
        data = self.get_invoice_data()

        if not data['customer_id']:
            QMessageBox.warning(self, _('Validation Error'), _('Please select a customer'))
            return False

        if not data['items']:
            QMessageBox.warning(self, _('Validation Error'), _('Please add at least one item'))
            return False

        return True


class InvoiceItemDialog(QDialog):
    """Dialog for adding invoice items."""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle(_('Add Invoice Item'))
        self.setModal(True)
        self.setMinimumSize(400, 300)

        self.setup_ui()

    def setup_ui(self):
        """Setup the dialog UI."""
        layout = QVBoxLayout()

        # Form layout
        form_layout = QFormLayout()

        # Product
        self.product_combo = QComboBox()
        self.load_products()
        self.product_combo.currentTextChanged.connect(self.on_product_changed)
        form_layout.addRow(_('Product') + '*:', self.product_combo)

        # Quantity
        self.quantity_spin = QDoubleSpinBox()
        self.quantity_spin.setMinimum(0.01)
        self.quantity_spin.setMaximum(999999.99)
        self.quantity_spin.setDecimals(2)
        self.quantity_spin.setValue(1.0)
        form_layout.addRow(_('Quantity') + '*:', self.quantity_spin)

        # Unit Price
        self.unit_price_spin = QDoubleSpinBox()
        self.unit_price_spin.setMaximum(999999.99)
        self.unit_price_spin.setDecimals(2)
        form_layout.addRow(_('Unit Price') + '*:', self.unit_price_spin)

        # Tax Rate
        self.tax_rate_spin = QDoubleSpinBox()
        self.tax_rate_spin.setMaximum(100.0)
        self.tax_rate_spin.setDecimals(2)
        self.tax_rate_spin.setSuffix('%')
        form_layout.addRow(_('Tax Rate') + ':', self.tax_rate_spin)

        layout.addLayout(form_layout)

        # Buttons
        button_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok |
                                     QDialogButtonBox.StandardButton.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)

        self.setLayout(layout)

    def load_products(self):
        """Load products."""
        session = SessionLocal()
        try:
            products = session.query(Product).filter(Product.is_active == True).all()
            self.product_combo.addItem(_('Select Product'), None)
            for product in products:
                self.product_combo.addItem(f"{product.code} - {product.name}", product.id)
        finally:
            session.close()

    def on_product_changed(self):
        """Handle product selection change."""
        product_id = self.product_combo.currentData()
        if product_id:
            session = SessionLocal()
            try:
                product = session.query(Product).get(product_id)
                if product:
                    self.unit_price_spin.setValue(product.sale_price)
                    self.tax_rate_spin.setValue(product.tax_rate)
            finally:
                session.close()

    def get_item_data(self):
        """Get item data from form."""
        return {
            'product_id': self.product_combo.currentData(),
            'product_name': self.product_combo.currentText(),
            'quantity': self.quantity_spin.value(),
            'unit_price': self.unit_price_spin.value(),
            'tax_rate': self.tax_rate_spin.value()
        }