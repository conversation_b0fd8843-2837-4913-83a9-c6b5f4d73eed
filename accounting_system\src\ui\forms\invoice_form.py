"""
Invoice form for sales.
"""
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QPushButton, 
    QLabel, QLineEdit, QTextEdit, QFormLayout,
    QTableWidget, QTableWidgetItem, QMessageBox,
    QDateEdit, QDoubleSpinBox, QComboBox
)
from PyQt6.QtCore import Qt, QDate
from src.database.database import SessionLocal
from src.models.models import Customer
from src.models.transactions import Invoice, InvoiceItem, DocumentStatus, PaymentStatus
from src.models.inventory import Product

class InvoiceForm(QWidget):
    """Invoice form class."""
    
    def __init__(self):
        super().__init__()
        self._init_ui()
    
    def _init_ui(self):
        """Initialize the UI."""
        layout = QHBoxLayout()
        self.setLayout(layout)
        
        # Left side - Invoice list
        list_widget = QWidget()
        list_layout = QVBoxLayout()
        list_widget.setLayout(list_layout)
        
        # Add search box
        search_layout = QHBoxLayout()
        search_label = QLabel('بحث:')
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText('ابحث برقم الفاتورة أو العميل...')
        search_layout.addWidget(search_label)
        search_layout.addWidget(self.search_input)
        list_layout.addLayout(search_layout)
        
        # Add invoice table
        self.table = QTableWidget()
        self.table.setColumnCount(5)
        self.table.setHorizontalHeaderLabels(['الرقم', 'التاريخ', 'العميل', 'المبلغ', 'الحالة'])
        self.table.horizontalHeader().setStretchLastSection(True)
        list_layout.addWidget(self.table)
        
        # Right side - Invoice details
        details_widget = QWidget()
        details_layout = QFormLayout()
        details_widget.setLayout(details_layout)
        
        # Add invoice header fields
        self.number_input = QLineEdit()
        details_layout.addRow('رقم الفاتورة:', self.number_input)
        
        self.date_input = QDateEdit()
        self.date_input.setCalendarPopup(True)
        self.date_input.setDate(QDate.currentDate())
        details_layout.addRow('التاريخ:', self.date_input)
        
        self.customer_input = QComboBox()
        self._load_customers()
        details_layout.addRow('العميل:', self.customer_input)
        
        # Add items table
        items_label = QLabel('الأصناف:')
        self.items_table = QTableWidget()
        self.items_table.setColumnCount(6)
        self.items_table.setHorizontalHeaderLabels(
            ['الصنف', 'الوصف', 'الكمية', 'السعر', 'الضريبة', 'الإجمالي']
        )
        details_layout.addRow(items_label)
        details_layout.addRow(self.items_table)
        
        # Add totals
        self.subtotal_input = QLineEdit()
        self.subtotal_input.setReadOnly(True)
        details_layout.addRow('المجموع:', self.subtotal_input)
        
        self.tax_amount_input = QLineEdit()
        self.tax_amount_input.setReadOnly(True)
        details_layout.addRow('الضريبة:', self.tax_amount_input)
        
        self.discount_input = QDoubleSpinBox()
        self.discount_input.setMaximum(1000000)
        details_layout.addRow('الخصم:', self.discount_input)
        
        self.total_input = QLineEdit()
        self.total_input.setReadOnly(True)
        details_layout.addRow('الإجمالي:', self.total_input)
        
        self.status_input = QComboBox()
        self.status_input.addItems([status.value for status in DocumentStatus])
        details_layout.addRow('الحالة:', self.status_input)
        
        # Add buttons
        buttons_layout = QHBoxLayout()
        
        self.new_btn = QPushButton('جديد')
        self.new_btn.clicked.connect(self._clear_form)
        buttons_layout.addWidget(self.new_btn)
        
        self.save_btn = QPushButton('حفظ')
        self.save_btn.clicked.connect(self._save_invoice)
        buttons_layout.addWidget(self.save_btn)
        
        self.print_btn = QPushButton('طباعة')
        self.print_btn.clicked.connect(self._print_invoice)
        buttons_layout.addWidget(self.print_btn)
        
        details_layout.addRow('', buttons_layout)
        
        # Add widgets to main layout
        layout.addWidget(list_widget, stretch=1)
        layout.addWidget(details_widget, stretch=1)
        
        # Initialize member variables
        self.current_invoice = None
    
    def _load_customers(self):
        """Load customers into combo box."""
        try:
            db = SessionLocal()
            customers = db.query(Customer).filter(Customer.is_active == True).all()
            self.customer_input.clear()
            self.customer_input.addItem('-- اختر العميل --', None)
            for customer in customers:
                self.customer_input.addItem(customer.name, customer.id)
        except Exception as e:
            QMessageBox.critical(self, 'خطأ', f'حدث خطأ أثناء تحميل العملاء: {str(e)}')
        finally:
            db.close()
    
    def _clear_form(self):
        """Clear form fields."""
        self.current_invoice = None
        self.number_input.clear()
        self.date_input.setDate(QDate.currentDate())
        self.customer_input.setCurrentIndex(0)
        self.items_table.setRowCount(0)
        self.subtotal_input.clear()
        self.tax_amount_input.clear()
        self.discount_input.setValue(0)
        self.total_input.clear()
        self.status_input.setCurrentIndex(0)
    
    def _save_invoice(self):
        """Save invoice data."""
        QMessageBox.information(self, 'حفظ', 'سيتم تنفيذ عملية الحفظ قريباً')
    
    def _print_invoice(self):
        """Print invoice."""
        QMessageBox.information(self, 'طباعة', 'سيتم تنفيذ عملية الطباعة قريباً')
