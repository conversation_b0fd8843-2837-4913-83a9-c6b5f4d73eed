print("🚀 اختبار Python...")
print("✅ Python يعمل!")

try:
    import sys
    print(f"🐍 Python: {sys.version}")
except:
    print("❌ خطأ في sys")

try:
    import os
    print(f"📁 المجلد: {os.getcwd()}")
except:
    print("❌ خطأ في os")

try:
    import sqlalchemy
    print(f"✅ SQLAlchemy: {sqlalchemy.__version__}")
except ImportError:
    print("❌ SQLAlchemy غير متاح")

try:
    import bcrypt
    print("✅ bcrypt متاح")
except ImportError:
    print("❌ bcrypt غير متاح")

try:
    from PyQt6.QtWidgets import QApplication
    print("✅ PyQt6 متاح!")
except ImportError:
    print("❌ PyQt6 غير متاح - يحتاج تثبيت")

print("🎉 انتهى الاختبار!")
