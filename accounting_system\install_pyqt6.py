#!/usr/bin/env python3
"""
تثبيت PyQt6 بطرق متعددة
Install PyQt6 with Multiple Methods
"""
import subprocess
import sys
import os

def run_command(command, description):
    """تشغيل أمر وإرجاع النتيجة"""
    print(f"🔧 {description}...")
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True, timeout=300)
        if result.returncode == 0:
            print(f"✅ {description} نجح!")
            print(f"📄 الإخراج: {result.stdout[:200]}...")
            return True
        else:
            print(f"❌ {description} فشل!")
            print(f"📄 الخطأ: {result.stderr[:200]}...")
            return False
    except subprocess.TimeoutExpired:
        print(f"⏰ {description} انتهت المهلة الزمنية!")
        return False
    except Exception as e:
        print(f"❌ خطأ في {description}: {e}")
        return False

def test_pyqt6():
    """اختبار PyQt6"""
    try:
        from PyQt6.QtWidgets import QApplication
        from PyQt6.QtCore import PYQT_VERSION_STR
        print(f"✅ PyQt6 يعمل! الإصدار: {PYQT_VERSION_STR}")
        return True
    except ImportError as e:
        print(f"❌ PyQt6 غير متاح: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء تثبيت PyQt6...")
    print(f"🐍 Python: {sys.version}")
    print(f"📁 المجلد: {os.getcwd()}")
    
    # اختبار أولي
    print("\n🔍 اختبار PyQt6 الحالي...")
    if test_pyqt6():
        print("🎉 PyQt6 مثبت بالفعل!")
        return True
    
    # طرق التثبيت المختلفة
    install_methods = [
        ("pip install --user PyQt6", "تثبيت PyQt6 للمستخدم"),
        ("python -m pip install --user PyQt6", "تثبيت PyQt6 باستخدام python -m pip"),
        ("pip install --user PyQt6 --no-cache-dir", "تثبيت PyQt6 بدون ذاكرة التخزين المؤقت"),
        ("python -m pip install --user PyQt6 --no-cache-dir", "تثبيت PyQt6 بدون ذاكرة التخزين المؤقت (python -m)"),
        ("pip install PyQt6", "تثبيت PyQt6 عام"),
        ("python -m pip install PyQt6", "تثبيت PyQt6 عام (python -m)"),
    ]
    
    for command, description in install_methods:
        print(f"\n📦 محاولة: {description}")
        if run_command(command, description):
            print("🔍 اختبار التثبيت...")
            if test_pyqt6():
                print("🎉 تم تثبيت PyQt6 بنجاح!")
                return True
            else:
                print("⚠️ التثبيت نجح لكن الاختبار فشل")
        
        print("⏭️ الانتقال للطريقة التالية...")
    
    # إذا فشلت جميع الطرق
    print("\n❌ فشل تثبيت PyQt6 بجميع الطرق!")
    print("\n💡 حلول بديلة:")
    print("1. جرب تشغيل Command Prompt كمدير (Run as Administrator)")
    print("2. تحديث pip: python -m pip install --upgrade pip")
    print("3. تنظيف ذاكرة التخزين المؤقت: pip cache purge")
    print("4. تثبيت من موقع PyQt مباشرة")
    print("5. استخدام conda بدلاً من pip: conda install pyqt")
    
    return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 PyQt6 جاهز للاستخدام!")
        print("💡 يمكنك الآن تشغيل النظام:")
        print("   python final_launch.py")
    else:
        print("\n😞 لم يتم تثبيت PyQt6 بنجاح")
        print("💡 جرب الحلول البديلة المذكورة أعلاه")
