"""
Product management form.
"""
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QPushButton, 
    QLabel, QLineEdit, QTextEdit, QFormLayout,
    QTableWidget, QTableWidgetItem, QMessageBox,
    QSpinBox, QDoubleSpinBox, QCheckBox, QComboBox
)
from PyQt6.QtCore import Qt
from src.database.database import SessionLocal
from src.models.inventory import Product, ProductCategory, UnitType

class ProductForm(QWidget):
    """Product form class."""
    
    def __init__(self):
        super().__init__()
        self._init_ui()
        self._load_data()
    
    def _init_ui(self):
        """Initialize the UI."""
        layout = QHBoxLayout()
        self.setLayout(layout)
        
        # Left side - Product list
        list_widget = QWidget()
        list_layout = QVBoxLayout()
        list_widget.setLayout(list_layout)
        
        # Add search box
        search_layout = QHBoxLayout()
        search_label = QLabel('بحث:')
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText('ابحث باسم المنتج أو الرقم...')
        self.search_input.textChanged.connect(self._filter_products)
        search_layout.addWidget(search_label)
        search_layout.addWidget(self.search_input)
        list_layout.addLayout(search_layout)
        
        # Add product table
        self.table = QTableWidget()
        self.table.setColumnCount(5)
        self.table.setHorizontalHeaderLabels(['الرقم', 'الاسم', 'الوحدة', 'سعر البيع', 'الكمية'])
        self.table.horizontalHeader().setStretchLastSection(True)
        self.table.itemClicked.connect(self._load_product)
        list_layout.addWidget(self.table)
        
        # Right side - Product details
        details_widget = QWidget()
        details_layout = QFormLayout()
        details_widget.setLayout(details_layout)
        
        # Add product fields
        self.code_input = QLineEdit()
        details_layout.addRow('رقم المنتج:', self.code_input)
        
        self.name_input = QLineEdit()
        details_layout.addRow('اسم المنتج:', self.name_input)
        
        self.description_input = QTextEdit()
        self.description_input.setMaximumHeight(100)
        details_layout.addRow('الوصف:', self.description_input)
        
        self.category_input = QComboBox()
        self._load_categories()
        details_layout.addRow('التصنيف:', self.category_input)
        
        self.unit_input = QComboBox()
        self.unit_input.addItems([unit.value for unit in UnitType])
        details_layout.addRow('الوحدة:', self.unit_input)
        
        self.purchase_price_input = QDoubleSpinBox()
        self.purchase_price_input.setMaximum(1000000)
        self.purchase_price_input.setMinimum(0)
        details_layout.addRow('سعر الشراء:', self.purchase_price_input)
        
        self.sale_price_input = QDoubleSpinBox()
        self.sale_price_input.setMaximum(1000000)
        self.sale_price_input.setMinimum(0)
        details_layout.addRow('سعر البيع:', self.sale_price_input)
        
        self.tax_rate_input = QDoubleSpinBox()
        self.tax_rate_input.setMaximum(100)
        self.tax_rate_input.setMinimum(0)
        details_layout.addRow('نسبة الضريبة:', self.tax_rate_input)
        
        self.min_stock_input = QDoubleSpinBox()
        self.min_stock_input.setMaximum(1000000)
        self.min_stock_input.setMinimum(0)
        details_layout.addRow('الحد الأدنى:', self.min_stock_input)
        
        self.max_stock_input = QDoubleSpinBox()
        self.max_stock_input.setMaximum(1000000)
        self.max_stock_input.setMinimum(0)
        details_layout.addRow('الحد الأقصى:', self.max_stock_input)
        
        self.current_stock_input = QDoubleSpinBox()
        self.current_stock_input.setMaximum(1000000)
        self.current_stock_input.setMinimum(0)
        details_layout.addRow('الكمية الحالية:', self.current_stock_input)
        
        self.is_active_input = QCheckBox('نشط')
        self.is_active_input.setChecked(True)
        details_layout.addRow('الحالة:', self.is_active_input)
        
        # Add buttons
        buttons_layout = QHBoxLayout()
        
        self.new_btn = QPushButton('جديد')
        self.new_btn.clicked.connect(self._clear_form)
        buttons_layout.addWidget(self.new_btn)
        
        self.save_btn = QPushButton('حفظ')
        self.save_btn.clicked.connect(self._save_product)
        buttons_layout.addWidget(self.save_btn)
        
        self.delete_btn = QPushButton('حذف')
        self.delete_btn.clicked.connect(self._delete_product)
        buttons_layout.addWidget(self.delete_btn)
        
        details_layout.addRow('', buttons_layout)
        
        # Add widgets to main layout
        layout.addWidget(list_widget, stretch=1)
        layout.addWidget(details_widget, stretch=1)
        
        # Initialize member variables
        self.current_product = None
    
    def _load_categories(self):
        """Load product categories into combo box."""
        try:
            db = SessionLocal()
            categories = db.query(ProductCategory).all()
            self.category_input.clear()
            self.category_input.addItem('-- اختر التصنيف --', None)
            for category in categories:
                self.category_input.addItem(category.name, category.id)
        except Exception as e:
            QMessageBox.critical(self, 'خطأ', f'حدث خطأ أثناء تحميل التصنيفات: {str(e)}')
        finally:
            db.close()
    
    def _load_data(self):
        """Load products data into table."""
        try:
            db = SessionLocal()
            products = db.query(Product).all()
            self.table.setRowCount(len(products))
            
            for i, product in enumerate(products):
                self.table.setItem(i, 0, QTableWidgetItem(product.code))
                self.table.setItem(i, 1, QTableWidgetItem(product.name))
                self.table.setItem(i, 2, QTableWidgetItem(product.unit.value))
                self.table.setItem(i, 3, QTableWidgetItem(str(product.sale_price)))
                self.table.setItem(i, 4, QTableWidgetItem(str(product.current_stock)))
        
        except Exception as e:
            QMessageBox.critical(self, 'خطأ', f'حدث خطأ أثناء تحميل البيانات: {str(e)}')
        finally:
            db.close()
    
    def _filter_products(self):
        """Filter products based on search text."""
        search_text = self.search_input.text().lower()
        for row in range(self.table.rowCount()):
            match = False
            for col in range(self.table.columnCount()):
                item = self.table.item(row, col)
                if item and search_text in item.text().lower():
                    match = True
                    break
            self.table.setRowHidden(row, not match)
    
    def _load_product(self, item):
        """Load product details when selected from table."""
        try:
            db = SessionLocal()
            product_code = self.table.item(item.row(), 0).text()
            product = db.query(Product).filter(Product.code == product_code).first()
            
            if product:
                self.current_product = product
                self.code_input.setText(product.code)
                self.name_input.setText(product.name)
                self.description_input.setText(product.description or '')
                
                # Set category
                category_index = self.category_input.findData(product.category_id)
                if category_index >= 0:
                    self.category_input.setCurrentIndex(category_index)
                
                # Set unit
                unit_index = self.unit_input.findText(product.unit.value)
                if unit_index >= 0:
                    self.unit_input.setCurrentIndex(unit_index)
                
                self.purchase_price_input.setValue(product.purchase_price)
                self.sale_price_input.setValue(product.sale_price)
                self.tax_rate_input.setValue(product.tax_rate)
                self.min_stock_input.setValue(product.min_stock)
                self.max_stock_input.setValue(product.max_stock)
                self.current_stock_input.setValue(product.current_stock)
                self.is_active_input.setChecked(product.is_active)
        
        except Exception as e:
            QMessageBox.critical(self, 'خطأ', f'حدث خطأ أثناء تحميل بيانات المنتج: {str(e)}')
        finally:
            db.close()
    
    def _clear_form(self):
        """Clear form fields."""
        self.current_product = None
        self.code_input.clear()
        self.name_input.clear()
        self.description_input.clear()
        self.category_input.setCurrentIndex(0)
        self.unit_input.setCurrentIndex(0)
        self.purchase_price_input.setValue(0)
        self.sale_price_input.setValue(0)
        self.tax_rate_input.setValue(0)
        self.min_stock_input.setValue(0)
        self.max_stock_input.setValue(0)
        self.current_stock_input.setValue(0)
        self.is_active_input.setChecked(True)
    
    def _save_product(self):
        """Save product data."""
        try:
            if not self.code_input.text() or not self.name_input.text():
                QMessageBox.warning(self, 'تنبيه', 'الرجاء إدخال رقم واسم المنتج')
                return
            
            db = SessionLocal()
            
            if self.current_product:
                product = self.current_product
            else:
                # Check if code exists
                existing = db.query(Product).filter(
                    Product.code == self.code_input.text()
                ).first()
                if existing:
                    QMessageBox.warning(
                        self, 'تنبيه', 'رقم المنتج موجود مسبقاً'
                    )
                    return
                product = Product()
            
            product.code = self.code_input.text()
            product.name = self.name_input.text()
            product.description = self.description_input.toPlainText()
            product.category_id = self.category_input.currentData()
            product.unit = UnitType(self.unit_input.currentText())
            product.purchase_price = self.purchase_price_input.value()
            product.sale_price = self.sale_price_input.value()
            product.tax_rate = self.tax_rate_input.value()
            product.min_stock = self.min_stock_input.value()
            product.max_stock = self.max_stock_input.value()
            product.current_stock = self.current_stock_input.value()
            product.is_active = self.is_active_input.isChecked()
            
            if not self.current_product:
                db.add(product)
            
            db.commit()
            
            self._load_data()
            self._clear_form()
            
            QMessageBox.information(
                self, 'نجاح', 'تم حفظ بيانات المنتج بنجاح'
            )
        
        except Exception as e:
            db.rollback()
            QMessageBox.critical(
                self, 'خطأ', f'حدث خطأ أثناء حفظ بيانات المنتج: {str(e)}'
            )
        finally:
            db.close()
    
    def _delete_product(self):
        """Delete product."""
        if not self.current_product:
            QMessageBox.warning(self, 'تنبيه', 'الرجاء اختيار منتج للحذف')
            return
        
        reply = QMessageBox.question(
            self, 'تأكيد الحذف',
            'هل أنت متأكد من حذف هذا المنتج؟',
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            try:
                db = SessionLocal()
                db.delete(self.current_product)
                db.commit()
                
                self._load_data()
                self._clear_form()
                
                QMessageBox.information(
                    self, 'نجاح', 'تم حذف المنتج بنجاح'
                )
            
            except Exception as e:
                db.rollback()
                QMessageBox.critical(
                    self, 'خطأ', f'حدث خطأ أثناء حذف المنتج: {str(e)}'
                )
            finally:
                db.close()
