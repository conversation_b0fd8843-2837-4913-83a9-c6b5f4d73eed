"""
نموذج إدارة المنتجات المحسن
Enhanced Product Management Form
"""
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
    QLabel, QLineEdit, QTextEdit, QFormLayout,
    QTableWidget, QTableWidgetItem, QMessageBox,
    QSpinBox, QDoubleSpinBox, QCheckBox, QComboBox,
    QHeaderView, QAbstractItemView, QDialog, QDialogButtonBox,
    QGroupBox, QGridLayout, QTabWidget
)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QFont
from src.database.database import SessionLocal
from src.models.inventory import Product, ProductCategory
from src.utils.translation import _

class ProductForm(QWidget):
    """Enhanced Product form class."""

    def __init__(self):
        super().__init__()
        self.current_product = None
        self._init_ui()
        self._load_data()

    def _init_ui(self):
        """Initialize the enhanced UI."""
        layout = QVBoxLayout()
        self.setLayout(layout)

        # Title
        title = QLabel(_('Product Management'))
        title.setFont(QFont('Arial', 16, QFont.Weight.Bold))
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title)

        # Search and filter section
        search_group = QGroupBox(_('Search & Filter'))
        search_layout = QGridLayout()
        search_group.setLayout(search_layout)

        # Search box
        search_layout.addWidget(QLabel(_('Search') + ':'), 0, 0)
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText(_('Search by code or name...'))
        self.search_input.textChanged.connect(self._filter_products)
        search_layout.addWidget(self.search_input, 0, 1)

        # Category filter
        search_layout.addWidget(QLabel(_('Category') + ':'), 0, 2)
        self.category_filter = QComboBox()
        self.category_filter.currentTextChanged.connect(self._filter_products)
        search_layout.addWidget(self.category_filter, 0, 3)

        # Status filter
        search_layout.addWidget(QLabel(_('Status') + ':'), 0, 4)
        self.status_filter = QComboBox()
        self.status_filter.addItems([_('All'), _('Active'), _('Inactive'), _('Low Stock')])
        self.status_filter.currentTextChanged.connect(self._filter_products)
        search_layout.addWidget(self.status_filter, 0, 5)

        layout.addWidget(search_group)

        # Buttons section
        buttons_layout = QHBoxLayout()

        self.add_btn = QPushButton(_('Add Product'))
        self.add_btn.clicked.connect(self._add_product)
        buttons_layout.addWidget(self.add_btn)

        self.edit_btn = QPushButton(_('Edit Product'))
        self.edit_btn.clicked.connect(self._edit_product)
        self.edit_btn.setEnabled(False)
        buttons_layout.addWidget(self.edit_btn)

        self.delete_btn = QPushButton(_('Delete Product'))
        self.delete_btn.clicked.connect(self._delete_product)
        self.delete_btn.setEnabled(False)
        buttons_layout.addWidget(self.delete_btn)

        self.refresh_btn = QPushButton(_('Refresh'))
        self.refresh_btn.clicked.connect(self._load_data)
        buttons_layout.addWidget(self.refresh_btn)

        buttons_layout.addStretch()
        layout.addLayout(buttons_layout)

        # Products table
        self.table = QTableWidget()
        self.table.setColumnCount(9)
        self.table.setHorizontalHeaderLabels([
            _('Code'), _('Name'), _('Category'), _('Unit'),
            _('Purchase Price'), _('Sale Price'), _('Current Stock'),
            _('Min Stock'), _('Status')
        ])

        # Configure table
        header = self.table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        self.table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.table.setAlternatingRowColors(True)
        self.table.itemSelectionChanged.connect(self._on_selection_changed)
        self.table.itemDoubleClicked.connect(self._edit_product)

        layout.addWidget(self.table)

    def _on_selection_changed(self):
        """Handle selection change."""
        has_selection = len(self.table.selectedItems()) > 0
        self.edit_btn.setEnabled(has_selection)
        self.delete_btn.setEnabled(has_selection)

    def _load_categories(self):
        """Load product categories for filter."""
        session = SessionLocal()
        try:
            self.category_filter.clear()
            self.category_filter.addItem(_('All Categories'))

            categories = session.query(ProductCategory).all()
            for category in categories:
                self.category_filter.addItem(category.name)
        finally:
            session.close()

    def _load_data(self):
        """Load products data."""
        self._load_categories()
        self._load_products()

    def _load_products(self):
        """Load products into table."""
        session = SessionLocal()
        try:
            products = session.query(Product).all()
            self.table.setRowCount(len(products))

            for i, product in enumerate(products):
                # Code
                self.table.setItem(i, 0, QTableWidgetItem(product.code))

                # Name
                self.table.setItem(i, 1, QTableWidgetItem(product.name))

                # Category
                category_name = product.category_rel.name if product.category_rel else _('No Category')
                self.table.setItem(i, 2, QTableWidgetItem(category_name))

                # Unit
                self.table.setItem(i, 3, QTableWidgetItem(product.unit or 'قطعة'))

                # Purchase Price
                self.table.setItem(i, 4, QTableWidgetItem(f"{product.purchase_price:.2f}"))

                # Sale Price
                self.table.setItem(i, 5, QTableWidgetItem(f"{product.sale_price:.2f}"))

                # Current Stock
                stock_item = QTableWidgetItem(f"{product.current_stock:.2f}")
                if product.current_stock <= product.min_stock:
                    stock_item.setBackground(Qt.GlobalColor.red)
                elif product.current_stock <= product.min_stock * 1.5:
                    stock_item.setBackground(Qt.GlobalColor.yellow)
                self.table.setItem(i, 6, stock_item)

                # Min Stock
                self.table.setItem(i, 7, QTableWidgetItem(f"{product.min_stock:.2f}"))

                # Status
                if product.current_stock <= product.min_stock:
                    status = _('Low Stock')
                elif product.current_stock <= product.min_stock * 1.5:
                    status = _('Warning')
                else:
                    status = _('Normal')
                self.table.setItem(i, 8, QTableWidgetItem(status))

                # Store product ID in first column
                self.table.item(i, 0).setData(Qt.ItemDataRole.UserRole, product.id)

        finally:
            session.close()

    def _filter_products(self):
        """Filter products based on search criteria."""
        search_text = self.search_input.text().lower()
        category_filter = self.category_filter.currentText()
        status_filter = self.status_filter.currentText()

        for row in range(self.table.rowCount()):
            show_row = True

            # Search filter
            if search_text:
                code = self.table.item(row, 0).text().lower()
                name = self.table.item(row, 1).text().lower()
                if search_text not in code and search_text not in name:
                    show_row = False

            # Category filter
            if category_filter != _('All Categories'):
                category = self.table.item(row, 2).text()
                if category != category_filter:
                    show_row = False

            # Status filter
            if status_filter != _('All'):
                if status_filter == _('Active'):
                    # Check if product is active (you might need to add this info to table)
                    pass
                elif status_filter == _('Inactive'):
                    # Check if product is inactive
                    pass
                elif status_filter == _('Low Stock'):
                    status = self.table.item(row, 8).text()
                    if status != _('Low Stock'):
                        show_row = False

            self.table.setRowHidden(row, not show_row)

    def _add_product(self):
        """Add new product."""
        dialog = ProductDialog(parent=self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            if dialog.validate_data():
                self._save_product_data(dialog.get_product_data())

    def _edit_product(self):
        """Edit selected product."""
        current_row = self.table.currentRow()
        if current_row < 0:
            return

        product_id = self.table.item(current_row, 0).data(Qt.ItemDataRole.UserRole)

        session = SessionLocal()
        try:
            product = session.query(Product).get(product_id)
            if product:
                dialog = ProductDialog(product, parent=self)
                if dialog.exec() == QDialog.DialogCode.Accepted:
                    if dialog.validate_data():
                        self._update_product_data(product, dialog.get_product_data())
        finally:
            session.close()

    def _delete_product(self):
        """Delete selected product."""
        current_row = self.table.currentRow()
        if current_row < 0:
            return

        product_name = self.table.item(current_row, 1).text()

        reply = QMessageBox.question(
            self, _('Confirm Delete'),
            _('Are you sure you want to delete product: ') + product_name + '?',
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            product_id = self.table.item(current_row, 0).data(Qt.ItemDataRole.UserRole)

            session = SessionLocal()
            try:
                product = session.query(Product).get(product_id)
                if product:
                    session.delete(product)
                    session.commit()
                    QMessageBox.information(self, _('Success'), _('Product deleted successfully'))
                    self._load_data()
            except Exception as e:
                session.rollback()
                QMessageBox.critical(self, _('Error'), _('Failed to delete product: ') + str(e))
            finally:
                session.close()

    def _save_product_data(self, data):
        """Save new product."""
        session = SessionLocal()
        try:
            # Check if code already exists
            existing = session.query(Product).filter_by(code=data['code']).first()
            if existing:
                QMessageBox.warning(self, _('Error'), _('Product code already exists'))
                return

            product = Product(
                code=data['code'],
                name=data['name'],
                description=data['description'],
                unit=data['unit'],
                purchase_price=data['purchase_price'],
                sale_price=data['sale_price'],
                tax_rate=data['tax_rate'],
                current_stock=data['current_stock'],
                min_stock=data['min_stock'],
                max_stock=data['max_stock'],
                is_active=data['is_active'],
                category_id=data['category_id']
            )

            # Set compatibility fields
            product.unit_price = data['sale_price']
            product.quantity = data['current_stock']
            product.minimum_quantity = data['min_stock']
            product.category = data.get('category_name', '')

            session.add(product)
            session.commit()
            QMessageBox.information(self, _('Success'), _('Product added successfully'))
            self._load_data()

        except Exception as e:
            session.rollback()
            QMessageBox.critical(self, _('Error'), _('Failed to add product: ') + str(e))
        finally:
            session.close()

    def _update_product_data(self, product, data):
        """Update existing product."""
        session = SessionLocal()
        try:
            # Check if code already exists (excluding current product)
            existing = session.query(Product).filter(
                Product.code == data['code'],
                Product.id != product.id
            ).first()
            if existing:
                QMessageBox.warning(self, _('Error'), _('Product code already exists'))
                return

            # Update product fields
            product.code = data['code']
            product.name = data['name']
            product.description = data['description']
            product.unit = data['unit']
            product.purchase_price = data['purchase_price']
            product.sale_price = data['sale_price']
            product.tax_rate = data['tax_rate']
            product.current_stock = data['current_stock']
            product.min_stock = data['min_stock']
            product.max_stock = data['max_stock']
            product.is_active = data['is_active']
            product.category_id = data['category_id']

            # Update compatibility fields
            product.unit_price = data['sale_price']
            product.quantity = data['current_stock']
            product.minimum_quantity = data['min_stock']

            session.commit()
            QMessageBox.information(self, _('Success'), _('Product updated successfully'))
            self._load_data()

        except Exception as e:
            session.rollback()
            QMessageBox.critical(self, _('Error'), _('Failed to update product: ') + str(e))
        finally:
            session.close()


class ProductDialog(QDialog):
    """Dialog for adding/editing products."""

    def __init__(self, product=None, parent=None):
        super().__init__(parent)
        self.product = product
        self.setWindowTitle(_('Add Product') if product is None else _('Edit Product'))
        self.setModal(True)
        self.setMinimumSize(500, 600)

        self.setup_ui()
        if product:
            self.load_product_data()

    def setup_ui(self):
        """Setup the dialog UI."""
        layout = QVBoxLayout()

        # Form layout
        form_layout = QFormLayout()

        # Product code
        self.code_edit = QLineEdit()
        self.code_edit.setMaxLength(20)
        form_layout.addRow(_('Product Code') + '*:', self.code_edit)

        # Product name
        self.name_edit = QLineEdit()
        self.name_edit.setMaxLength(100)
        form_layout.addRow(_('Product Name') + '*:', self.name_edit)

        # Description
        self.description_edit = QTextEdit()
        self.description_edit.setMaximumHeight(80)
        form_layout.addRow(_('Description') + ':', self.description_edit)

        # Category
        self.category_combo = QComboBox()
        self.load_categories()
        form_layout.addRow(_('Category') + ':', self.category_combo)

        # Unit
        self.unit_edit = QLineEdit()
        self.unit_edit.setText('قطعة')
        form_layout.addRow(_('Unit') + ':', self.unit_edit)

        # Purchase price
        self.purchase_price_spin = QDoubleSpinBox()
        self.purchase_price_spin.setMaximum(999999.99)
        self.purchase_price_spin.setDecimals(2)
        form_layout.addRow(_('Purchase Price') + ':', self.purchase_price_spin)

        # Sale price
        self.sale_price_spin = QDoubleSpinBox()
        self.sale_price_spin.setMaximum(999999.99)
        self.sale_price_spin.setDecimals(2)
        form_layout.addRow(_('Sale Price') + ':', self.sale_price_spin)

        # Tax rate
        self.tax_rate_spin = QDoubleSpinBox()
        self.tax_rate_spin.setMaximum(100.0)
        self.tax_rate_spin.setDecimals(2)
        self.tax_rate_spin.setSuffix('%')
        form_layout.addRow(_('Tax Rate') + ':', self.tax_rate_spin)

        # Current stock
        self.current_stock_spin = QDoubleSpinBox()
        self.current_stock_spin.setMaximum(999999.99)
        self.current_stock_spin.setDecimals(2)
        form_layout.addRow(_('Current Stock') + ':', self.current_stock_spin)

        # Minimum stock
        self.min_stock_spin = QDoubleSpinBox()
        self.min_stock_spin.setMaximum(999999.99)
        self.min_stock_spin.setDecimals(2)
        form_layout.addRow(_('Minimum Stock') + ':', self.min_stock_spin)

        # Maximum stock
        self.max_stock_spin = QDoubleSpinBox()
        self.max_stock_spin.setMaximum(999999.99)
        self.max_stock_spin.setDecimals(2)
        form_layout.addRow(_('Maximum Stock') + ':', self.max_stock_spin)

        # Is active
        self.is_active_check = QCheckBox()
        self.is_active_check.setChecked(True)
        form_layout.addRow(_('Active') + ':', self.is_active_check)

        layout.addLayout(form_layout)

        # Buttons
        button_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok |
                                     QDialogButtonBox.StandardButton.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)

        self.setLayout(layout)

    def load_categories(self):
        """Load product categories."""
        session = SessionLocal()
        try:
            categories = session.query(ProductCategory).all()
            self.category_combo.addItem(_('No Category'), None)
            for category in categories:
                self.category_combo.addItem(category.name, category.id)
        finally:
            session.close()

    def load_product_data(self):
        """Load product data for editing."""
        if not self.product:
            return

        self.code_edit.setText(self.product.code)
        self.name_edit.setText(self.product.name)
        self.description_edit.setPlainText(self.product.description or '')
        self.unit_edit.setText(self.product.unit or 'قطعة')
        self.purchase_price_spin.setValue(self.product.purchase_price)
        self.sale_price_spin.setValue(self.product.sale_price)
        self.tax_rate_spin.setValue(self.product.tax_rate)
        self.current_stock_spin.setValue(self.product.current_stock)
        self.min_stock_spin.setValue(self.product.min_stock)
        self.max_stock_spin.setValue(self.product.max_stock)
        self.is_active_check.setChecked(self.product.is_active)

        # Set category
        if self.product.category_id:
            for i in range(self.category_combo.count()):
                if self.category_combo.itemData(i) == self.product.category_id:
                    self.category_combo.setCurrentIndex(i)
                    break

    def get_product_data(self):
        """Get product data from form."""
        return {
            'code': self.code_edit.text().strip(),
            'name': self.name_edit.text().strip(),
            'description': self.description_edit.toPlainText().strip(),
            'unit': self.unit_edit.text().strip(),
            'purchase_price': self.purchase_price_spin.value(),
            'sale_price': self.sale_price_spin.value(),
            'tax_rate': self.tax_rate_spin.value(),
            'current_stock': self.current_stock_spin.value(),
            'min_stock': self.min_stock_spin.value(),
            'max_stock': self.max_stock_spin.value(),
            'is_active': self.is_active_check.isChecked(),
            'category_id': self.category_combo.currentData()
        }

    def validate_data(self):
        """Validate form data."""
        data = self.get_product_data()

        if not data['code']:
            QMessageBox.warning(self, _('Validation Error'), _('Product code is required'))
            return False

        if not data['name']:
            QMessageBox.warning(self, _('Validation Error'), _('Product name is required'))
            return False

        return True
