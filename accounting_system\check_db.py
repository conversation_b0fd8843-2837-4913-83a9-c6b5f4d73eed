#!/usr/bin/env python3
"""
فحص قاعدة البيانات
Check Database Script
"""
import sys
import sqlite3
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def check_database():
    """Check database structure."""
    db_path = project_root / "data" / "accounting.db"
    
    if not db_path.exists():
        print("❌ قاعدة البيانات غير موجودة / Database not found")
        return
    
    try:
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()
        
        # Get all tables
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        
        print("📋 الجداول الموجودة / Existing tables:")
        for table in tables:
            print(f"  - {table[0]}")
        
        # Check users table structure
        print("\n🔍 بنية جدول المستخدمين / Users table structure:")
        cursor.execute("PRAGMA table_info(users);")
        columns = cursor.fetchall()
        
        if columns:
            for col in columns:
                print(f"  - {col[1]} ({col[2]})")
        else:
            print("  ❌ جدول المستخدمين غير موجود / Users table not found")
        
        # Check products table structure
        print("\n🔍 بنية جدول المنتجات / Products table structure:")
        cursor.execute("PRAGMA table_info(products);")
        columns = cursor.fetchall()
        
        if columns:
            for col in columns:
                print(f"  - {col[1]} ({col[2]})")
        else:
            print("  ❌ جدول المنتجات غير موجود / Products table not found")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ في فحص قاعدة البيانات / Database check error: {e}")

if __name__ == '__main__':
    check_database()
