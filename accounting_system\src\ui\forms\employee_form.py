"""
نموذج إدارة الموظفين المحسن
Enhanced Employee Management Form
"""
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
    QLabel, QLineEdit, QTextEdit, QFormLayout,
    QTableWidget, QTableWidgetItem, QMessageBox,
    QDateEdit, QComboBox, QDoubleSpinBox, QCheckBox,
    QHeaderView, QAbstractItemView, QDialog, QDialogButtonBox,
    QGroupBox, QGridLayout, QTabWidget, QSpinBox
)
from PyQt6.QtCore import Qt, QDate
from PyQt6.QtGui import QFont
from src.database.database import SessionLocal
from src.models.hr import Employee, Department, EmploymentType
from src.utils.translation import _
from datetime import datetime

class EmployeeForm(QWidget):
    """Enhanced Employee form class."""

    def __init__(self):
        super().__init__()
        self.current_employee = None
        self._init_ui()
        self._load_data()

    def _init_ui(self):
        """Initialize the enhanced UI."""
        layout = QVBoxLayout()
        self.setLayout(layout)

        # Title
        title = QLabel(_('Employee Management'))
        title.setFont(QFont('Arial', 16, QFont.Weight.Bold))
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title)

        # Search and filter section
        search_group = QGroupBox(_('Search & Filter'))
        search_layout = QGridLayout()
        search_group.setLayout(search_layout)

        # Search box
        search_layout.addWidget(QLabel(_('Search') + ':'), 0, 0)
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText(_('Search by name or employee code...'))
        self.search_input.textChanged.connect(self._filter_employees)
        search_layout.addWidget(self.search_input, 0, 1)

        # Department filter
        search_layout.addWidget(QLabel(_('Department') + ':'), 0, 2)
        self.department_filter = QComboBox()
        self.department_filter.currentTextChanged.connect(self._filter_employees)
        search_layout.addWidget(self.department_filter, 0, 3)

        # Status filter
        search_layout.addWidget(QLabel(_('Status') + ':'), 0, 4)
        self.status_filter = QComboBox()
        self.status_filter.addItems([_('All'), _('Active'), _('Inactive')])
        self.status_filter.currentTextChanged.connect(self._filter_employees)
        search_layout.addWidget(self.status_filter, 0, 5)

        layout.addWidget(search_group)

        # Buttons section
        buttons_layout = QHBoxLayout()

        self.add_btn = QPushButton(_('Add Employee'))
        self.add_btn.clicked.connect(self._add_employee)
        buttons_layout.addWidget(self.add_btn)

        self.edit_btn = QPushButton(_('Edit Employee'))
        self.edit_btn.clicked.connect(self._edit_employee)
        self.edit_btn.setEnabled(False)
        buttons_layout.addWidget(self.edit_btn)

        self.delete_btn = QPushButton(_('Delete Employee'))
        self.delete_btn.clicked.connect(self._delete_employee)
        self.delete_btn.setEnabled(False)
        buttons_layout.addWidget(self.delete_btn)

        self.refresh_btn = QPushButton(_('Refresh'))
        self.refresh_btn.clicked.connect(self._load_data)
        buttons_layout.addWidget(self.refresh_btn)

        buttons_layout.addStretch()
        layout.addLayout(buttons_layout)

        # Employees table
        self.table = QTableWidget()
        self.table.setColumnCount(7)
        self.table.setHorizontalHeaderLabels([
            _('Employee Code'), _('Full Name'), _('Department'), _('Position'),
            _('Hire Date'), _('Basic Salary'), _('Status')
        ])

        # Configure table
        header = self.table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        self.table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.table.setAlternatingRowColors(True)
        self.table.itemSelectionChanged.connect(self._on_selection_changed)
        self.table.itemDoubleClicked.connect(self._edit_employee)

        layout.addWidget(self.table)

    def _on_selection_changed(self):
        """Handle selection change."""
        has_selection = len(self.table.selectedItems()) > 0
        self.edit_btn.setEnabled(has_selection)
        self.delete_btn.setEnabled(has_selection)

    def _load_data(self):
        """Load employees data."""
        self._load_departments_filter()
        self._load_employees()

    def _load_departments_filter(self):
        """Load departments for filter."""
        session = SessionLocal()
        try:
            self.department_filter.clear()
            self.department_filter.addItem(_('All Departments'))

            departments = session.query(Department).all()
            for department in departments:
                self.department_filter.addItem(department.name)
        finally:
            session.close()

    def _load_employees(self):
        """Load employees into table."""
        session = SessionLocal()
        try:
            employees = session.query(Employee).all()
            self.table.setRowCount(len(employees))

            for i, employee in enumerate(employees):
                # Employee Code
                self.table.setItem(i, 0, QTableWidgetItem(employee.code))

                # Full Name
                full_name = f"{employee.first_name} {employee.last_name}"
                self.table.setItem(i, 1, QTableWidgetItem(full_name))

                # Department
                department_name = employee.department.name if employee.department else _('No Department')
                self.table.setItem(i, 2, QTableWidgetItem(department_name))

                # Position
                self.table.setItem(i, 3, QTableWidgetItem(employee.position or ''))

                # Hire Date
                hire_date_str = employee.hire_date.strftime('%Y-%m-%d') if employee.hire_date else ''
                self.table.setItem(i, 4, QTableWidgetItem(hire_date_str))

                # Basic Salary
                self.table.setItem(i, 5, QTableWidgetItem(f"{employee.basic_salary:.2f}"))

                # Status
                status_text = _('Active') if employee.is_active else _('Inactive')
                self.table.setItem(i, 6, QTableWidgetItem(status_text))

                # Store employee ID in first column
                self.table.item(i, 0).setData(Qt.ItemDataRole.UserRole, employee.id)

        finally:
            session.close()

    def _filter_employees(self):
        """Filter employees based on search criteria."""
        search_text = self.search_input.text().lower()
        department_filter = self.department_filter.currentText()
        status_filter = self.status_filter.currentText()

        for row in range(self.table.rowCount()):
            show_row = True

            # Search filter
            if search_text:
                code = self.table.item(row, 0).text().lower()
                name = self.table.item(row, 1).text().lower()
                if search_text not in code and search_text not in name:
                    show_row = False

            # Department filter
            if department_filter != _('All Departments'):
                department = self.table.item(row, 2).text()
                if department != department_filter:
                    show_row = False

            # Status filter
            if status_filter != _('All'):
                status = self.table.item(row, 6).text()
                if status_filter == _('Active') and status != _('Active'):
                    show_row = False
                elif status_filter == _('Inactive') and status != _('Inactive'):
                    show_row = False

            self.table.setRowHidden(row, not show_row)

    def _add_employee(self):
        """Add new employee."""
        dialog = EmployeeDialog(parent=self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            if dialog.validate_data():
                self._save_employee_data(dialog.get_employee_data())

    def _edit_employee(self):
        """Edit selected employee."""
        current_row = self.table.currentRow()
        if current_row < 0:
            return

        employee_id = self.table.item(current_row, 0).data(Qt.ItemDataRole.UserRole)

        session = SessionLocal()
        try:
            employee = session.query(Employee).get(employee_id)
            if employee:
                dialog = EmployeeDialog(employee, parent=self)
                if dialog.exec() == QDialog.DialogCode.Accepted:
                    if dialog.validate_data():
                        self._update_employee_data(employee, dialog.get_employee_data())
        finally:
            session.close()

    def _delete_employee(self):
        """Delete selected employee."""
        current_row = self.table.currentRow()
        if current_row < 0:
            return

        employee_name = self.table.item(current_row, 1).text()

        reply = QMessageBox.question(
            self, _('Confirm Delete'),
            _('Are you sure you want to delete employee: ') + employee_name + '?',
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            employee_id = self.table.item(current_row, 0).data(Qt.ItemDataRole.UserRole)

            session = SessionLocal()
            try:
                employee = session.query(Employee).get(employee_id)
                if employee:
                    session.delete(employee)
                    session.commit()
                    QMessageBox.information(self, _('Success'), _('Employee deleted successfully'))
                    self._load_data()
            except Exception as e:
                session.rollback()
                QMessageBox.critical(self, _('Error'), _('Failed to delete employee: ') + str(e))
            finally:
                session.close()

    def _save_employee_data(self, data):
        """Save new employee."""
        session = SessionLocal()
        try:
            # Check if code already exists
            existing = session.query(Employee).filter_by(code=data['code']).first()
            if existing:
                QMessageBox.warning(self, _('Error'), _('Employee code already exists'))
                return

            employee = Employee(
                code=data['code'],
                first_name=data['first_name'],
                last_name=data['last_name'],
                email=data['email'],
                phone=data['phone'],
                address=data['address'],
                birth_date=data['birth_date'],
                hire_date=data['hire_date'],
                department_id=data['department_id'],
                position=data['position'],
                employment_type=data['employment_type'],
                basic_salary=data['basic_salary'],
                bank_name=data['bank_name'],
                bank_account=data['bank_account'],
                social_security_number=data['social_security_number'],
                tax_id=data['tax_id'],
                is_active=data['is_active']
            )

            session.add(employee)
            session.commit()
            QMessageBox.information(self, _('Success'), _('Employee added successfully'))
            self._load_data()

        except Exception as e:
            session.rollback()
            QMessageBox.critical(self, _('Error'), _('Failed to add employee: ') + str(e))
        finally:
            session.close()

    def _update_employee_data(self, employee, data):
        """Update existing employee."""
        session = SessionLocal()
        try:
            # Check if code already exists (excluding current employee)
            existing = session.query(Employee).filter(
                Employee.code == data['code'],
                Employee.id != employee.id
            ).first()
            if existing:
                QMessageBox.warning(self, _('Error'), _('Employee code already exists'))
                return

            # Update employee fields
            employee.code = data['code']
            employee.first_name = data['first_name']
            employee.last_name = data['last_name']
            employee.email = data['email']
            employee.phone = data['phone']
            employee.address = data['address']
            employee.birth_date = data['birth_date']
            employee.hire_date = data['hire_date']
            employee.department_id = data['department_id']
            employee.position = data['position']
            employee.employment_type = data['employment_type']
            employee.basic_salary = data['basic_salary']
            employee.bank_name = data['bank_name']
            employee.bank_account = data['bank_account']
            employee.social_security_number = data['social_security_number']
            employee.tax_id = data['tax_id']
            employee.is_active = data['is_active']

            session.commit()
            QMessageBox.information(self, _('Success'), _('Employee updated successfully'))
            self._load_data()

        except Exception as e:
            session.rollback()
            QMessageBox.critical(self, _('Error'), _('Failed to update employee: ') + str(e))
        finally:
            session.close()


class EmployeeDialog(QDialog):
    """Dialog for adding/editing employees."""

    def __init__(self, employee=None, parent=None):
        super().__init__(parent)
        self.employee = employee
        self.setWindowTitle(_('Add Employee') if employee is None else _('Edit Employee'))
        self.setModal(True)
        self.setMinimumSize(600, 700)

        self.setup_ui()
        if employee:
            self.load_employee_data()

    def setup_ui(self):
        """Setup the dialog UI."""
        layout = QVBoxLayout()

        # Create tabs
        tab_widget = QTabWidget()

        # Personal Information Tab
        personal_tab = QWidget()
        personal_layout = QFormLayout()
        personal_tab.setLayout(personal_layout)

        # Employee code
        self.code_edit = QLineEdit()
        self.code_edit.setMaxLength(20)
        personal_layout.addRow(_('Employee Code') + '*:', self.code_edit)

        # First name
        self.first_name_edit = QLineEdit()
        self.first_name_edit.setMaxLength(50)
        personal_layout.addRow(_('First Name') + '*:', self.first_name_edit)

        # Last name
        self.last_name_edit = QLineEdit()
        self.last_name_edit.setMaxLength(50)
        personal_layout.addRow(_('Last Name') + '*:', self.last_name_edit)

        # Email
        self.email_edit = QLineEdit()
        self.email_edit.setMaxLength(100)
        personal_layout.addRow(_('Email') + ':', self.email_edit)

        # Phone
        self.phone_edit = QLineEdit()
        self.phone_edit.setMaxLength(20)
        personal_layout.addRow(_('Phone') + ':', self.phone_edit)

        # Address
        self.address_edit = QTextEdit()
        self.address_edit.setMaximumHeight(80)
        personal_layout.addRow(_('Address') + ':', self.address_edit)

        # Birth date
        self.birth_date_edit = QDateEdit()
        self.birth_date_edit.setCalendarPopup(True)
        self.birth_date_edit.setDate(QDate.currentDate().addYears(-25))
        personal_layout.addRow(_('Birth Date') + ':', self.birth_date_edit)

        # Is active
        self.is_active_check = QCheckBox()
        self.is_active_check.setChecked(True)
        personal_layout.addRow(_('Active') + ':', self.is_active_check)

        tab_widget.addTab(personal_tab, _('Personal Information'))

        # Employment Information Tab
        employment_tab = QWidget()
        employment_layout = QFormLayout()
        employment_tab.setLayout(employment_layout)

        # Hire date
        self.hire_date_edit = QDateEdit()
        self.hire_date_edit.setCalendarPopup(True)
        self.hire_date_edit.setDate(QDate.currentDate())
        employment_layout.addRow(_('Hire Date') + '*:', self.hire_date_edit)

        # Department
        self.department_combo = QComboBox()
        self.load_departments()
        employment_layout.addRow(_('Department') + ':', self.department_combo)

        # Position
        self.position_edit = QLineEdit()
        self.position_edit.setMaxLength(100)
        employment_layout.addRow(_('Position') + ':', self.position_edit)

        # Employment type
        self.employment_type_combo = QComboBox()
        self.employment_type_combo.addItems([emp_type.value for emp_type in EmploymentType])
        employment_layout.addRow(_('Employment Type') + ':', self.employment_type_combo)

        # Basic salary
        self.basic_salary_spin = QDoubleSpinBox()
        self.basic_salary_spin.setMaximum(999999.99)
        self.basic_salary_spin.setDecimals(2)
        employment_layout.addRow(_('Basic Salary') + ':', self.basic_salary_spin)

        tab_widget.addTab(employment_tab, _('Employment Information'))

        # Financial Information Tab
        financial_tab = QWidget()
        financial_layout = QFormLayout()
        financial_tab.setLayout(financial_layout)

        # Bank name
        self.bank_name_edit = QLineEdit()
        self.bank_name_edit.setMaxLength(100)
        financial_layout.addRow(_('Bank Name') + ':', self.bank_name_edit)

        # Bank account
        self.bank_account_edit = QLineEdit()
        self.bank_account_edit.setMaxLength(50)
        financial_layout.addRow(_('Bank Account') + ':', self.bank_account_edit)

        # Social security number
        self.social_security_edit = QLineEdit()
        self.social_security_edit.setMaxLength(20)
        financial_layout.addRow(_('Social Security Number') + ':', self.social_security_edit)

        # Tax ID
        self.tax_id_edit = QLineEdit()
        self.tax_id_edit.setMaxLength(20)
        financial_layout.addRow(_('Tax ID') + ':', self.tax_id_edit)

        tab_widget.addTab(financial_tab, _('Financial Information'))

        layout.addWidget(tab_widget)

        # Buttons
        button_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok |
                                     QDialogButtonBox.StandardButton.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)

        self.setLayout(layout)

    def load_departments(self):
        """Load departments."""
        session = SessionLocal()
        try:
            departments = session.query(Department).all()
            self.department_combo.addItem(_('No Department'), None)
            for department in departments:
                self.department_combo.addItem(department.name, department.id)
        finally:
            session.close()

    def load_employee_data(self):
        """Load employee data for editing."""
        if not self.employee:
            return

        self.code_edit.setText(self.employee.code)
        self.first_name_edit.setText(self.employee.first_name)
        self.last_name_edit.setText(self.employee.last_name)
        self.email_edit.setText(self.employee.email or '')
        self.phone_edit.setText(self.employee.phone or '')
        self.address_edit.setPlainText(self.employee.address or '')
        self.is_active_check.setChecked(self.employee.is_active)
        self.position_edit.setText(self.employee.position or '')
        self.basic_salary_spin.setValue(self.employee.basic_salary)
        self.bank_name_edit.setText(self.employee.bank_name or '')
        self.bank_account_edit.setText(self.employee.bank_account or '')
        self.social_security_edit.setText(self.employee.social_security_number or '')
        self.tax_id_edit.setText(self.employee.tax_id or '')

        # Set dates
        if self.employee.birth_date:
            self.birth_date_edit.setDate(QDate.fromString(self.employee.birth_date.strftime('%Y-%m-%d'), 'yyyy-MM-dd'))
        if self.employee.hire_date:
            self.hire_date_edit.setDate(QDate.fromString(self.employee.hire_date.strftime('%Y-%m-%d'), 'yyyy-MM-dd'))

        # Set department
        if self.employee.department_id:
            for i in range(self.department_combo.count()):
                if self.department_combo.itemData(i) == self.employee.department_id:
                    self.department_combo.setCurrentIndex(i)
                    break

        # Set employment type
        if self.employee.employment_type:
            type_index = self.employment_type_combo.findText(self.employee.employment_type.value)
            if type_index >= 0:
                self.employment_type_combo.setCurrentIndex(type_index)

    def get_employee_data(self):
        """Get employee data from form."""
        return {
            'code': self.code_edit.text().strip(),
            'first_name': self.first_name_edit.text().strip(),
            'last_name': self.last_name_edit.text().strip(),
            'email': self.email_edit.text().strip(),
            'phone': self.phone_edit.text().strip(),
            'address': self.address_edit.toPlainText().strip(),
            'birth_date': self.birth_date_edit.date().toPython(),
            'hire_date': self.hire_date_edit.date().toPython(),
            'department_id': self.department_combo.currentData(),
            'position': self.position_edit.text().strip(),
            'employment_type': EmploymentType(self.employment_type_combo.currentText()),
            'basic_salary': self.basic_salary_spin.value(),
            'bank_name': self.bank_name_edit.text().strip(),
            'bank_account': self.bank_account_edit.text().strip(),
            'social_security_number': self.social_security_edit.text().strip(),
            'tax_id': self.tax_id_edit.text().strip(),
            'is_active': self.is_active_check.isChecked()
        }

    def validate_data(self):
        """Validate form data."""
        data = self.get_employee_data()

        if not data['code']:
            QMessageBox.warning(self, _('Validation Error'), _('Employee code is required'))
            return False

        if not data['first_name']:
            QMessageBox.warning(self, _('Validation Error'), _('First name is required'))
            return False

        if not data['last_name']:
            QMessageBox.warning(self, _('Validation Error'), _('Last name is required'))
            return False

        return True