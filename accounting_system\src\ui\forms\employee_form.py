"""
Employee management form.
"""
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QPushButton, 
    QLabel, QLineEdit, QTextEdit, QFormLayout,
    QTableWidget, QTableWidgetItem, QMessageBox,
    QDateEdit, QComboBox
)
from PyQt6.QtCore import Qt, QDate
from src.database.database import SessionLocal
from src.models.hr import Employee, Department, EmploymentType
from datetime import datetime

class EmployeeForm(QWidget):
    """Employee form class."""
    
    def __init__(self):
        super().__init__()
        self._init_ui()
        self._load_data()
    
    def _init_ui(self):
        """Initialize the UI."""
        layout = QHBoxLayout()
        self.setLayout(layout)
        
        # Left side - Employee list
        list_widget = QWidget()
        list_layout = QVBoxLayout()
        list_widget.setLayout(list_layout)
        
        # Add search box
        search_layout = QHBoxLayout()
        search_label = QLabel('بحث:')
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText('ابحث باسم الموظف أو الرقم...')
        self.search_input.textChanged.connect(self._filter_employees)
        search_layout.addWidget(search_label)
        search_layout.addWidget(self.search_input)
        list_layout.addLayout(search_layout)
        
        # Add employee table
        self.table = QTableWidget()
        self.table.setColumnCount(4)
        self.table.setHorizontalHeaderLabels(['الرقم', 'الاسم', 'القسم', 'الوظيفة'])
        self.table.horizontalHeader().setStretchLastSection(True)
        self.table.itemClicked.connect(self._load_employee)
        list_layout.addWidget(self.table)
        
        # Right side - Employee details
        details_widget = QWidget()
        details_layout = QFormLayout()
        details_widget.setLayout(details_layout)
        
        # Add employee fields
        self.code_input = QLineEdit()
        details_layout.addRow('رقم الموظف:', self.code_input)
        
        self.first_name_input = QLineEdit()
        details_layout.addRow('الاسم الأول:', self.first_name_input)
        
        self.last_name_input = QLineEdit()
        details_layout.addRow('اسم العائلة:', self.last_name_input)
        
        self.email_input = QLineEdit()
        details_layout.addRow('البريد الإلكتروني:', self.email_input)
        
        self.phone_input = QLineEdit()
        details_layout.addRow('رقم الهاتف:', self.phone_input)
        
        self.address_input = QTextEdit()
        self.address_input.setMaximumHeight(100)
        details_layout.addRow('العنوان:', self.address_input)
        
        self.birth_date_input = QDateEdit()
        self.birth_date_input.setCalendarPopup(True)
        details_layout.addRow('تاريخ الميلاد:', self.birth_date_input)
        
        self.hire_date_input = QDateEdit()
        self.hire_date_input.setCalendarPopup(True)
        self.hire_date_input.setDate(QDate.currentDate())
        details_layout.addRow('تاريخ التوظيف:', self.hire_date_input)
        
        self.department_input = QComboBox()
        self._load_departments()
        details_layout.addRow('القسم:', self.department_input)
        
        self.position_input = QLineEdit()
        details_layout.addRow('الوظيفة:', self.position_input)
        
        self.employment_type_input = QComboBox()
        self.employment_type_input.addItems([emp_type.value for emp_type in EmploymentType])
        details_layout.addRow('نوع التوظيف:', self.employment_type_input)
        
        self.basic_salary_input = QLineEdit()
        details_layout.addRow('الراتب الأساسي:', self.basic_salary_input)
        
        self.bank_name_input = QLineEdit()
        details_layout.addRow('اسم البنك:', self.bank_name_input)
        
        self.bank_account_input = QLineEdit()
        details_layout.addRow('رقم الحساب:', self.bank_account_input)
        
        self.social_security_input = QLineEdit()
        details_layout.addRow('رقم التأمينات:', self.social_security_input)
        
        self.tax_id_input = QLineEdit()
        details_layout.addRow('الرقم الضريبي:', self.tax_id_input)
        
        # Add buttons
        buttons_layout = QHBoxLayout()
        
        self.new_btn = QPushButton('جديد')
        self.new_btn.clicked.connect(self._clear_form)
        buttons_layout.addWidget(self.new_btn)
        
        self.save_btn = QPushButton('حفظ')
        self.save_btn.clicked.connect(self._save_employee)
        buttons_layout.addWidget(self.save_btn)
        
        self.delete_btn = QPushButton('حذف')
        self.delete_btn.clicked.connect(self._delete_employee)
        buttons_layout.addWidget(self.delete_btn)
        
        details_layout.addRow('', buttons_layout)
        
        # Add widgets to main layout
        layout.addWidget(list_widget, stretch=1)
        layout.addWidget(details_widget, stretch=1)
        
        # Initialize member variables
        self.current_employee = None
    
    def _load_departments(self):
        """Load departments into combo box."""
        try:
            db = SessionLocal()
            departments = db.query(Department).all()
            self.department_input.clear()
            self.department_input.addItem('-- اختر القسم --', None)
            for department in departments:
                self.department_input.addItem(department.name, department.id)
        except Exception as e:
            QMessageBox.critical(self, 'خطأ', f'حدث خطأ أثناء تحميل الأقسام: {str(e)}')
        finally:
            db.close()
    
    def _load_data(self):
        """Load employees data into table."""
        try:
            db = SessionLocal()
            employees = db.query(Employee).all()
            self.table.setRowCount(len(employees))
            
            for i, employee in enumerate(employees):
                self.table.setItem(i, 0, QTableWidgetItem(employee.code))
                self.table.setItem(i, 1, QTableWidgetItem(
                    f'{employee.first_name} {employee.last_name}'
                ))
                self.table.setItem(i, 2, QTableWidgetItem(
                    employee.department.name if employee.department else ''
                ))
                self.table.setItem(i, 3, QTableWidgetItem(employee.position or ''))
        
        except Exception as e:
            QMessageBox.critical(self, 'خطأ', f'حدث خطأ أثناء تحميل البيانات: {str(e)}')
        finally:
            db.close()
    
    def _filter_employees(self):
        """Filter employees based on search text."""
        search_text = self.search_input.text().lower()
        for row in range(self.table.rowCount()):
            match = False
            for col in range(self.table.columnCount()):
                item = self.table.item(row, col)
                if item and search_text in item.text().lower():
                    match = True
                    break
            self.table.setRowHidden(row, not match)
    
    def _load_employee(self, item):
        """Load employee details when selected from table."""
        try:
            db = SessionLocal()
            employee_code = self.table.item(item.row(), 0).text()
            employee = db.query(Employee).filter(Employee.code == employee_code).first()
            
            if employee:
                self.current_employee = employee
                self.code_input.setText(employee.code)
                self.first_name_input.setText(employee.first_name)
                self.last_name_input.setText(employee.last_name)
                self.email_input.setText(employee.email or '')
                self.phone_input.setText(employee.phone or '')
                self.address_input.setText(employee.address or '')
                
                if employee.birth_date:
                    self.birth_date_input.setDate(QDate(employee.birth_date))
                if employee.hire_date:
                    self.hire_date_input.setDate(QDate(employee.hire_date))
                
                # Set department
                department_index = self.department_input.findData(employee.department_id)
                if department_index >= 0:
                    self.department_input.setCurrentIndex(department_index)
                
                self.position_input.setText(employee.position or '')
                
                # Set employment type
                type_index = self.employment_type_input.findText(employee.employment_type.value)
                if type_index >= 0:
                    self.employment_type_input.setCurrentIndex(type_index)
                
                self.basic_salary_input.setText(str(employee.basic_salary))
                self.bank_name_input.setText(employee.bank_name or '')
                self.bank_account_input.setText(employee.bank_account or '')
                self.social_security_input.setText(employee.social_security_number or '')
                self.tax_id_input.setText(employee.tax_id or '')
        
        except Exception as e:
            QMessageBox.critical(self, 'خطأ', f'حدث خطأ أثناء تحميل بيانات الموظف: {str(e)}')
        finally:
            db.close()
    
    def _clear_form(self):
        """Clear form fields."""
        self.current_employee = None
        self.code_input.clear()
        self.first_name_input.clear()
        self.last_name_input.clear()
        self.email_input.clear()
        self.phone_input.clear()
        self.address_input.clear()
        self.birth_date_input.setDate(QDate.currentDate())
        self.hire_date_input.setDate(QDate.currentDate())
        self.department_input.setCurrentIndex(0)
        self.position_input.clear()
        self.employment_type_input.setCurrentIndex(0)
        self.basic_salary_input.clear()
        self.bank_name_input.clear()
        self.bank_account_input.clear()
        self.social_security_input.clear()
        self.tax_id_input.clear()
    
    def _save_employee(self):
        """Save employee data."""
        try:
            if not self.code_input.text() or not self.first_name_input.text():
                QMessageBox.warning(self, 'تنبيه', 'الرجاء إدخال رقم واسم الموظف')
                return
            
            db = SessionLocal()
            
            if self.current_employee:
                employee = self.current_employee
            else:
                # Check if code exists
                existing = db.query(Employee).filter(
                    Employee.code == self.code_input.text()
                ).first()
                if existing:
                    QMessageBox.warning(
                        self, 'تنبيه', 'رقم الموظف موجود مسبقاً'
                    )
                    return
                employee = Employee()
            
            employee.code = self.code_input.text()
            employee.first_name = self.first_name_input.text()
            employee.last_name = self.last_name_input.text()
            employee.email = self.email_input.text()
            employee.phone = self.phone_input.text()
            employee.address = self.address_input.toPlainText()
            employee.birth_date = self.birth_date_input.date().toPyDate()
            employee.hire_date = self.hire_date_input.date().toPyDate()
            employee.department_id = self.department_input.currentData()
            employee.position = self.position_input.text()
            employee.employment_type = EmploymentType(self.employment_type_input.currentText())
            employee.basic_salary = float(self.basic_salary_input.text() or 0)
            employee.bank_name = self.bank_name_input.text()
            employee.bank_account = self.bank_account_input.text()
            employee.social_security_number = self.social_security_input.text()
            employee.tax_id = self.tax_id_input.text()
            
            if not self.current_employee:
                db.add(employee)
            
            db.commit()
            
            self._load_data()
            self._clear_form()
            
            QMessageBox.information(
                self, 'نجاح', 'تم حفظ بيانات الموظف بنجاح'
            )
        
        except Exception as e:
            db.rollback()
            QMessageBox.critical(
                self, 'خطأ', f'حدث خطأ أثناء حفظ بيانات الموظف: {str(e)}'
            )
        finally:
            db.close()
    
    def _delete_employee(self):
        """Delete employee."""
        if not self.current_employee:
            QMessageBox.warning(self, 'تنبيه', 'الرجاء اختيار موظف للحذف')
            return
        
        reply = QMessageBox.question(
            self, 'تأكيد الحذف',
            'هل أنت متأكد من حذف هذا الموظف؟',
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            try:
                db = SessionLocal()
                db.delete(self.current_employee)
                db.commit()
                
                self._load_data()
                self._clear_form()
                
                QMessageBox.information(
                    self, 'نجاح', 'تم حذف الموظف بنجاح'
                )
            
            except Exception as e:
                db.rollback()
                QMessageBox.critical(
                    self, 'خطأ', f'حدث خطأ أثناء حذف الموظف: {str(e)}'
                )
            finally:
                db.close()
