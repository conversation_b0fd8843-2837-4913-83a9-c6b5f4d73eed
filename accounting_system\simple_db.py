#!/usr/bin/env python3
"""
إنشاء قاعدة بيانات بسيطة
Simple Database Creation
"""
import sys
import os
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def create_simple_database():
    """Create a simple database."""
    print("🔄 إنشاء قاعدة بيانات بسيطة / Creating simple database...")
    
    # Create data directory
    data_dir = project_root / "data"
    data_dir.mkdir(exist_ok=True)
    
    try:
        # Import only what we need
        from src.database.database import engine, Base
        
        # Create all tables
        Base.metadata.create_all(bind=engine)
        print("✅ تم إنشاء قاعدة البيانات / Database created")
        
        # Test connection
        from src.database.database import SessionLocal
        session = SessionLocal()
        session.close()
        print("✅ تم اختبار الاتصال / Connection tested")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    if create_simple_database():
        print("🎉 نجح إنشاء قاعدة البيانات / Database creation successful!")
    else:
        print("❌ فشل إنشاء قاعدة البيانات / Database creation failed!")
        sys.exit(1)
