# 🔧 دليل الإعداد والتثبيت - Setup Guide

## 📋 المتطلبات الأساسية

### 1️⃣ **Python 3.8+**
تأكد من تثبيت Python 3.8 أو أحدث:
```bash
python --version
```

### 2️⃣ **المكتبات المطلوبة:**
- **PyQt6** - للواجهة الرسومية
- **SQLAlchemy** - لقاعدة البيانات
- **bcrypt** - لتشفير كلمات المرور

---

## 🚀 خطوات التثبيت

### الطريقة 1: التثبيت السريع
```bash
# انتقل إلى مجلد النظام
cd accounting_system

# ثبت المتطلبات
pip install PyQt6 SQLAlchemy bcrypt python-dotenv

# شغل النظام
python final_launch.py
```

### الطريقة 2: التثبيت للمستخدم الحالي
```bash
pip install --user PyQt6 SQLAlchemy bcrypt python-dotenv
```

### الطريقة 3: استخدام البيئة الافتراضية
```bash
# إنشاء بيئة افتراضية جديدة
python -m venv accounting_venv

# تفعيل البيئة (Windows)
accounting_venv\Scripts\activate

# تفعيل البيئة (Linux/Mac)
source accounting_venv/bin/activate

# تثبيت المتطلبات
pip install PyQt6 SQLAlchemy bcrypt python-dotenv

# شغل النظام
python final_launch.py
```

---

## 🧪 اختبار التثبيت

### اختبار سريع:
```bash
python -c "
try:
    from PyQt6.QtWidgets import QApplication
    print('✅ PyQt6 متاح!')
except ImportError:
    print('❌ PyQt6 غير متاح')

try:
    import sqlalchemy
    print('✅ SQLAlchemy متاح!')
except ImportError:
    print('❌ SQLAlchemy غير متاح')

try:
    import bcrypt
    print('✅ bcrypt متاح!')
except ImportError:
    print('❌ bcrypt غير متاح')
"
```

---

## 🚀 تشغيل النظام

### الطرق المتاحة للتشغيل:

#### 1️⃣ **التشغيل الأساسي:**
```bash
python final_launch.py
```

#### 2️⃣ **التشغيل الكامل:**
```bash
python test_all_forms.py
```

#### 3️⃣ **التشغيل مع إعداد البيانات:**
```bash
python setup_test_data.py
python final_launch.py
```

---

## 🐛 حل المشاكل الشائعة

### ❌ **خطأ: "No module named 'PyQt6'"**
```bash
# جرب هذه الحلول بالترتيب:
pip install PyQt6
pip install --user PyQt6
python -m pip install PyQt6
```

### ❌ **خطأ: "No space left on device"**
```bash
# نظف ذاكرة التخزين المؤقت
pip cache purge

# أو ثبت بدون ذاكرة التخزين المؤقت
pip install --no-cache-dir PyQt6
```

### ❌ **خطأ: "Permission denied"**
```bash
# استخدم التثبيت للمستخدم
pip install --user PyQt6 SQLAlchemy bcrypt
```

### ❌ **النظام لا يعمل على Windows**
```powershell
# استخدم PowerShell
python -m pip install PyQt6 SQLAlchemy bcrypt
python final_launch.py
```

---

## 📁 هيكل الملفات

```
accounting_system/
├── src/                    # الكود المصدري
│   ├── ui/forms/          # النماذج
│   ├── models/            # نماذج البيانات
│   ├── database/          # قاعدة البيانات
│   └── utils/             # الأدوات المساعدة
├── final_launch.py        # ملف التشغيل الرئيسي
├── test_all_forms.py      # اختبار جميع النماذج
├── setup_test_data.py     # إعداد البيانات التجريبية
└── SETUP_GUIDE.md         # هذا الملف
```

---

## 🔑 بيانات الدخول التجريبية

- **اسم المستخدم:** `admin`
- **كلمة المرور:** `admin123`

---

## 📋 النماذج المتاحة

1. 🛍️ **المنتجات** - إدارة المنتجات والمخزون
2. 🧾 **الفواتير** - إدارة الفواتير والمبيعات
3. 👥 **الموظفين** - إدارة الموظفين والموارد البشرية
4. 📦 **المشتريات** - إدارة أوامر الشراء
5. 💰 **المصروفات** - إدارة المصروفات والنفقات
6. 👤 **المستخدمين** - إدارة المستخدمين والصلاحيات
7. 💵 **الرواتب** - إدارة الرواتب والأجور

---

## 💡 نصائح مهمة

### ✅ **للحصول على أفضل تجربة:**
1. استخدم Python 3.9+ للحصول على أفضل أداء
2. تأكد من وجود مساحة كافية (500MB على الأقل)
3. أغلق برامج مكافحة الفيروسات مؤقتاً أثناء التثبيت
4. استخدم اتصال إنترنت مستقر للتحميل

### ⚠️ **تجنب هذه المشاكل:**
- لا تثبت في مجلد يحتوي على مسافات في الاسم
- لا تستخدم أحرف خاصة في مسار المجلد
- تأكد من صلاحيات الكتابة في المجلد

---

## 📞 الدعم

إذا واجهت مشاكل:

1. **تحقق من رسائل الخطأ** في Terminal/Command Prompt
2. **جرب الطرق البديلة** للتثبيت المذكورة أعلاه
3. **أعد تشغيل الجهاز** وجرب مرة أخرى
4. **تأكد من تحديث pip:** `python -m pip install --upgrade pip`

---

## 🎉 استمتع بالاستخدام!

بعد التثبيت الناجح، ستحصل على نظام محاسبة متكامل مع:
- واجهة عربية احترافية
- 7 نماذج متكاملة
- قاعدة بيانات متطورة
- نظام صلاحيات آمن

**🚀 ابدأ الآن: `python final_launch.py`**
