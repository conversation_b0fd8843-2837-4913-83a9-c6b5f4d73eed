"""
Accounting and financial related models.
"""
from sqlalchemy import Column, Integer, String, Float, DateTime, ForeignKey, Enum, Text, Boolean, Date
from sqlalchemy.orm import relationship, backref
from datetime import datetime, timezone
from src.database.database import Base
import enum

class AccountType(enum.Enum):
    """Account type enumeration."""
    ASSET = "asset"
    LIABILITY = "liability"
    EQUITY = "equity"
    REVENUE = "revenue"
    EXPENSE = "expense"

class AccountSubType(enum.Enum):
    """Account sub-type enumeration."""
    CURRENT_ASSET = "current_asset"
    FIXED_ASSET = "fixed_asset"
    CURRENT_LIABILITY = "current_liability"
    LONG_TERM_LIABILITY = "long_term_liability"
    CAPITAL = "capital"
    RETAINED_EARNINGS = "retained_earnings"
    OPERATING_REVENUE = "operating_revenue"
    OTHER_REVENUE = "other_revenue"
    OPERATING_EXPENSE = "operating_expense"
    OTHER_EXPENSE = "other_expense"

class Account(Base):
    """Chart of accounts model."""
    __tablename__ = 'accounts'

    id = Column(Integer, primary_key=True)
    code = Column(String(20), unique=True, nullable=False)
    name = Column(String(100), nullable=False)
    account_type = Column(Enum(AccountType), nullable=False)
    sub_type = Column(Enum(AccountSubType))
    parent_id = Column(Integer, ForeignKey('accounts.id'))
    description = Column(Text)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    
    # Self-referential relationship for hierarchical accounts
    children = relationship('Account', 
                          backref=backref('parent', remote_side=[id]))
    # Relationships
    journal_entries = relationship('JournalEntryLine', back_populates='account')
    expense_categories = relationship('ExpenseCategory', back_populates='account')

    def __repr__(self):
        return f'<Account {self.code} - {self.name}>'

class JournalEntry(Base):
    """Journal entry model."""
    __tablename__ = 'journal_entries'

    id = Column(Integer, primary_key=True)
    number = Column(String(20), unique=True, nullable=False)
    date = Column(Date, nullable=False)
    reference_type = Column(String(50))  # invoice, payment, expense, etc.
    reference_id = Column(Integer)
    description = Column(Text)
    is_posted = Column(Boolean, default=False)
    posting_date = Column(DateTime)
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    created_by = Column(Integer, ForeignKey('users.id'))
    
    # Relationships
    lines = relationship('JournalEntryLine', back_populates='journal_entry')

    def __repr__(self):
        return f'<JournalEntry {self.number}>'

class JournalEntryLine(Base):
    """Journal entry line model."""
    __tablename__ = 'journal_entry_lines'

    id = Column(Integer, primary_key=True)
    journal_entry_id = Column(Integer, ForeignKey('journal_entries.id'), nullable=False)
    account_id = Column(Integer, ForeignKey('accounts.id'), nullable=False)
    description = Column(Text)
    debit = Column(Float, default=0.0)
    credit = Column(Float, default=0.0)

    # Relationships
    journal_entry = relationship('JournalEntry', back_populates='lines')
    account = relationship('Account', back_populates='journal_entries')

    def __repr__(self):
        return f'<JournalEntryLine {self.id}>'

class ExpenseCategory(Base):
    """Expense category model."""
    __tablename__ = 'expense_categories'

    id = Column(Integer, primary_key=True)
    name = Column(String(100), nullable=False)
    description = Column(Text)
    account_id = Column(Integer, ForeignKey('accounts.id'))
    is_active = Column(Boolean, default=True)

    # Relationships
    account = relationship('Account', back_populates='expense_categories')
    expenses = relationship('Expense', back_populates='category')

    def __repr__(self):
        return f'<ExpenseCategory {self.name}>'

class Expense(Base):
    """Expense model."""
    __tablename__ = 'expenses'

    id = Column(Integer, primary_key=True)
    number = Column(String(20), unique=True, nullable=False)
    date = Column(Date, nullable=False)
    expense_category_id = Column(Integer, ForeignKey('expense_categories.id'))
    payee = Column(String(100))
    amount = Column(Float, default=0.0)
    tax_amount = Column(Float, default=0.0)
    description = Column(Text)
    payment_method = Column(String(50))
    reference = Column(String(100))
    is_recurring = Column(Boolean, default=False)
    recurring_interval = Column(String(50))  # monthly, quarterly, yearly
    next_recurring_date = Column(Date)
    created_at = Column(DateTime, default=datetime.utcnow)
    created_by = Column(Integer, ForeignKey('users.id'))
    
    # Relationships
    category = relationship('ExpenseCategory', back_populates='expenses')

    def __repr__(self):
        return f'<Expense {self.number}>'

class FiscalYear(Base):
    """Fiscal year model."""
    __tablename__ = 'fiscal_years'

    id = Column(Integer, primary_key=True)
    name = Column(String(50), nullable=False)
    start_date = Column(Date, nullable=False)
    end_date = Column(Date, nullable=False)
    is_closed = Column(Boolean, default=False)
    closed_at = Column(DateTime)
    closed_by = Column(Integer, ForeignKey('users.id'))
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))

    def __repr__(self):
        return f'<FiscalYear {self.name}>'

class FinancialStatement(Base):
    """Financial statement model for storing generated reports."""
    __tablename__ = 'financial_statements'

    id = Column(Integer, primary_key=True)
    statement_type = Column(String(50), nullable=False)  # balance_sheet, income_statement, etc.
    fiscal_year_id = Column(Integer, ForeignKey('fiscal_years.id'))
    period_start = Column(Date)
    period_end = Column(Date)
    generated_date = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    generated_by = Column(Integer, ForeignKey('users.id'))
    file_path = Column(String(500))
    notes = Column(Text)

    # Relationship with fiscal year
    fiscal_year = relationship('FiscalYear', backref='financial_statements')

    def __repr__(self):
        return f'<FinancialStatement {self.statement_type}>'
