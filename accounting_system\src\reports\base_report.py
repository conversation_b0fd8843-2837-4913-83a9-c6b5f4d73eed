"""
Base report generation functionality.
"""
from abc import ABC, abstractmethod
import os
from datetime import datetime
from reportlab.lib import colors
from reportlab.lib.pagesizes import A4, landscape
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.platypus import SimpleDocTemplate, Paragraph, Table, TableStyle
from src.utils.translation import _

class BaseReport(ABC):
    """Abstract base class for all reports."""
    
    def __init__(self):
        self.styles = getSampleStyleSheet()
        # Add Arabic support
        self.styles.add(ParagraphStyle(
            name='Arabic',
            fontName='Helvetica',
            alignment=1,  # Center alignment
            leading=16
        ))
        
    def generate(self, filename=None):
        """Generate the report."""
        if not filename:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = os.path.join('reports', f'{self.get_report_name()}_{timestamp}.pdf')
            
        # Ensure reports directory exists
        os.makedirs('reports', exist_ok=True)
            
        # Create the PDF document
        doc = SimpleDocTemplate(
            filename,
            pagesize=self.get_page_size(),
            rightMargin=72,
            leftMargin=72,
            topMargin=72,
            bottomMargin=72
        )
        
        # Build the document
        story = []
        
        # Add title
        title = Paragraph(
            f"{_(self.get_report_name())}",
            self.styles['Title']
        )
        story.append(title)
        
        # Add date
        date_text = Paragraph(
            f"{_('Date')}: {datetime.now().strftime('%Y-%m-%d %H:%M')}",
            self.styles['Normal']
        )
        story.append(date_text)
        
        # Add content
        story.extend(self.get_content())
        
        # Build the PDF
        doc.build(story)
        return filename
    
    @abstractmethod
    def get_report_name(self) -> str:
        """Get the name of the report."""
        pass
    
    @abstractmethod
    def get_content(self) -> list:
        """Get the content of the report."""
        pass
    
    def get_page_size(self):
        """Get the page size for the report."""
        return A4  # Default to A4, can be overridden
    
    def create_table(self, data, colWidths=None):
        """Create a formatted table."""
        if not colWidths:
            colWidths = [None] * len(data[0])  # Auto-width columns
            
        table = Table(data, colWidths=colWidths)
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 14),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('TEXTCOLOR', (0, 1), (-1, -1), colors.black),
            ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 1), (-1, -1), 12),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        return table
